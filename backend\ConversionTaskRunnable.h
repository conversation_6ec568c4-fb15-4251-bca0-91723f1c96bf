﻿#ifndef CONVERSIONTASKRUNNABLE_H
#define CONVERSIONTASKRUNNABLE_H

#include <QObject>
#include <QRunnable>
#include <QProcess>
#include "ImageConversionManager.h" // 获取ConversionTask结构体定义

class ConversionTaskRunnable : public QObject, public QRunnable
{
    Q_OBJECT
public:
    ConversionTaskRunnable(ConversionTask task);
    ~ConversionTaskRunnable();

    void run() override;

signals:
    void taskCompleted(int index, const QString &newPath);
    void taskCompletedWithSize(int index, const QString &newPath, qint64 originalSize, qint64 newSize);
    void taskFailed(int index);
    void taskFinished(); // 信号用于通知任务结束，无论成功失败
    void fileSizeIncreasedAfterConversion(int index, const QString &path);
    void fileSizeIncreasedWithSize(int index, const QString &path, qint64 originalSize, qint64 newSize);
    void fileCorrupted(int index, const QString &path, const QString &reason); // 新增：文件损坏信号

private slots:
    void onProcessFinished(int exitCode, QProcess::ExitStatus exitStatus);
    void onReadyReadStandardError();
    void onReadyReadStandardOutput();

private:
    void convertToAvif();
    void convertToWebp();
    void convertToAV1();

    // 辅助方法
    int parseFFmpegProgress(const QString &output);
    void fallbackToQtWebp();
    void checkFileSize();
    // findExecutable函数已移除，现在直接使用应用程序目录下的可执行文件
    bool validateFile(const QString &filePath);
    bool moveToRecycleBin(const QString &filePath); // 移动文件到回收站
    bool isVideoAV1Encoded(const QString &filePath); // 检查视频是否为AV1编码

    ConversionTask m_task;
    QProcess* m_process = nullptr;
    bool m_processRunning;
    qint64 m_originalFileSize = 0;
};

#endif // CONVERSIONTASKRUNNABLE_H
