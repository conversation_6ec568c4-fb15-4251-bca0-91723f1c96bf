[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-<PERSON>i", "-RTC1", "-clang:-std=c++17", "-MDd", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.43", "-DBACKEND_LIBRARY", "-DMINGW_HAS_SECURE_API", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_DEPRECATED_WARNINGS", "-DQT_GUI_LIB", "-DQT_HTTPSERVER_LIB", "-DQT_NETWORK_LIB", "-DQT_OPENGL_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DQT_WEBSOCKETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-Dbackend_EXPORTS", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Qt\\file\\palyer\\build\\backend\\backend_autogen\\include_Debug", "-IC:\\Qt\\file\\palyer\\backend", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQuick", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQml", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlIntegration", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlMeta", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlModels", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlWorkerScript", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtOpenGL", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtHttpServer", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtWebSockets", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Qt\\file\\palyer\\backend\\ConversionTaskRunnable.cpp"], "directory": "C:/Qt/file/palyer/build/.qtc_clangd", "file": "C:/Qt/file/palyer/backend/ConversionTaskRunnable.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-<PERSON>i", "-RTC1", "-clang:-std=c++17", "-MDd", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.43", "-DBACKEND_LIBRARY", "-DMINGW_HAS_SECURE_API", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_DEPRECATED_WARNINGS", "-DQT_GUI_LIB", "-DQT_HTTPSERVER_LIB", "-DQT_NETWORK_LIB", "-DQT_OPENGL_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DQT_WEBSOCKETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-Dbackend_EXPORTS", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Qt\\file\\palyer\\build\\backend\\backend_autogen\\include_Debug", "-IC:\\Qt\\file\\palyer\\backend", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQuick", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQml", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlIntegration", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlMeta", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlModels", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlWorkerScript", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtOpenGL", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtHttpServer", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtWebSockets", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Qt\\file\\palyer\\backend\\ImageConversionManager.cpp"], "directory": "C:/Qt/file/palyer/build/.qtc_clangd", "file": "C:/Qt/file/palyer/backend/ImageConversionManager.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-<PERSON>i", "-RTC1", "-clang:-std=c++17", "-MDd", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.43", "-DBACKEND_LIBRARY", "-DMINGW_HAS_SECURE_API", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_DEPRECATED_WARNINGS", "-DQT_GUI_LIB", "-DQT_HTTPSERVER_LIB", "-DQT_NETWORK_LIB", "-DQT_OPENGL_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DQT_WEBSOCKETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-Dbackend_EXPORTS", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Qt\\file\\palyer\\build\\backend\\backend_autogen\\include_Debug", "-IC:\\Qt\\file\\palyer\\backend", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQuick", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQml", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlIntegration", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlMeta", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlModels", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlWorkerScript", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtOpenGL", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtHttpServer", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtWebSockets", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Qt\\file\\palyer\\backend\\ImageProcessor.cpp"], "directory": "C:/Qt/file/palyer/build/.qtc_clangd", "file": "C:/Qt/file/palyer/backend/ImageProcessor.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-<PERSON>i", "-RTC1", "-clang:-std=c++17", "-MDd", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.43", "-DBACKEND_LIBRARY", "-DMINGW_HAS_SECURE_API", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_DEPRECATED_WARNINGS", "-DQT_GUI_LIB", "-DQT_HTTPSERVER_LIB", "-DQT_NETWORK_LIB", "-DQT_OPENGL_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DQT_WEBSOCKETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-Dbackend_EXPORTS", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Qt\\file\\palyer\\build\\backend\\backend_autogen\\include_Debug", "-IC:\\Qt\\file\\palyer\\backend", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQuick", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQml", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlIntegration", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlMeta", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlModels", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlWorkerScript", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtOpenGL", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtHttpServer", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtWebSockets", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Qt\\file\\palyer\\backend\\NetworkBackend.cpp"], "directory": "C:/Qt/file/palyer/build/.qtc_clangd", "file": "C:/Qt/file/palyer/backend/NetworkBackend.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-<PERSON>i", "-RTC1", "-clang:-std=c++17", "-MDd", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.43", "-DBACKEND_LIBRARY", "-DMINGW_HAS_SECURE_API", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_DEPRECATED_WARNINGS", "-DQT_GUI_LIB", "-DQT_HTTPSERVER_LIB", "-DQT_NETWORK_LIB", "-DQT_OPENGL_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DQT_WEBSOCKETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-Dbackend_EXPORTS", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Qt\\file\\palyer\\build\\backend\\backend_autogen\\include_Debug", "-IC:\\Qt\\file\\palyer\\backend", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQuick", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQml", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlIntegration", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlMeta", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlModels", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlWorkerScript", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtOpenGL", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtHttpServer", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtWebSockets", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Qt\\file\\palyer\\backend\\server\\ClientMode.cpp"], "directory": "C:/Qt/file/palyer/build/.qtc_clangd", "file": "C:/Qt/file/palyer/backend/server/ClientMode.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-<PERSON>i", "-RTC1", "-clang:-std=c++17", "-MDd", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.43", "-DBACKEND_LIBRARY", "-DMINGW_HAS_SECURE_API", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_DEPRECATED_WARNINGS", "-DQT_GUI_LIB", "-DQT_HTTPSERVER_LIB", "-DQT_NETWORK_LIB", "-DQT_OPENGL_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DQT_WEBSOCKETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-Dbackend_EXPORTS", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Qt\\file\\palyer\\build\\backend\\backend_autogen\\include_Debug", "-IC:\\Qt\\file\\palyer\\backend", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQuick", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQml", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlIntegration", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlMeta", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlModels", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlWorkerScript", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtOpenGL", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtHttpServer", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtWebSockets", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Qt\\file\\palyer\\backend\\server\\ServerMode.cpp"], "directory": "C:/Qt/file/palyer/build/.qtc_clangd", "file": "C:/Qt/file/palyer/backend/server/ServerMode.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-<PERSON>i", "-RTC1", "-clang:-std=c++17", "-MDd", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.43", "-DBACKEND_LIBRARY", "-DMINGW_HAS_SECURE_API", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_DEPRECATED_WARNINGS", "-DQT_GUI_LIB", "-DQT_HTTPSERVER_LIB", "-DQT_NETWORK_LIB", "-DQT_OPENGL_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DQT_WEBSOCKETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-Dbackend_EXPORTS", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Qt\\file\\palyer\\build\\backend\\backend_autogen\\include_Debug", "-IC:\\Qt\\file\\palyer\\backend", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQuick", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQml", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlIntegration", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlMeta", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlModels", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlWorkerScript", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtOpenGL", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtHttpServer", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtWebSockets", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Qt\\file\\palyer\\backend\\ConversionTaskRunnable.h"], "directory": "C:/Qt/file/palyer/build/.qtc_clangd", "file": "C:/Qt/file/palyer/backend/ConversionTaskRunnable.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-<PERSON>i", "-RTC1", "-clang:-std=c++17", "-MDd", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.43", "-DBACKEND_LIBRARY", "-DMINGW_HAS_SECURE_API", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_DEPRECATED_WARNINGS", "-DQT_GUI_LIB", "-DQT_HTTPSERVER_LIB", "-DQT_NETWORK_LIB", "-DQT_OPENGL_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DQT_WEBSOCKETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-Dbackend_EXPORTS", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Qt\\file\\palyer\\build\\backend\\backend_autogen\\include_Debug", "-IC:\\Qt\\file\\palyer\\backend", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQuick", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQml", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlIntegration", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlMeta", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlModels", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlWorkerScript", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtOpenGL", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtHttpServer", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtWebSockets", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Qt\\file\\palyer\\backend\\ImageConversionManager.h"], "directory": "C:/Qt/file/palyer/build/.qtc_clangd", "file": "C:/Qt/file/palyer/backend/ImageConversionManager.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-<PERSON>i", "-RTC1", "-clang:-std=c++17", "-MDd", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.43", "-DBACKEND_LIBRARY", "-DMINGW_HAS_SECURE_API", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_DEPRECATED_WARNINGS", "-DQT_GUI_LIB", "-DQT_HTTPSERVER_LIB", "-DQT_NETWORK_LIB", "-DQT_OPENGL_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DQT_WEBSOCKETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-Dbackend_EXPORTS", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Qt\\file\\palyer\\build\\backend\\backend_autogen\\include_Debug", "-IC:\\Qt\\file\\palyer\\backend", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQuick", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQml", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlIntegration", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlMeta", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlModels", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlWorkerScript", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtOpenGL", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtHttpServer", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtWebSockets", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Qt\\file\\palyer\\backend\\ImageProcessor.h"], "directory": "C:/Qt/file/palyer/build/.qtc_clangd", "file": "C:/Qt/file/palyer/backend/ImageProcessor.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-<PERSON>i", "-RTC1", "-clang:-std=c++17", "-MDd", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.43", "-DBACKEND_LIBRARY", "-DMINGW_HAS_SECURE_API", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_DEPRECATED_WARNINGS", "-DQT_GUI_LIB", "-DQT_HTTPSERVER_LIB", "-DQT_NETWORK_LIB", "-DQT_OPENGL_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DQT_WEBSOCKETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-Dbackend_EXPORTS", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Qt\\file\\palyer\\build\\backend\\backend_autogen\\include_Debug", "-IC:\\Qt\\file\\palyer\\backend", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQuick", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQml", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlIntegration", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlMeta", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlModels", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlWorkerScript", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtOpenGL", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtHttpServer", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtWebSockets", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Qt\\file\\palyer\\backend\\NetworkBackend.h"], "directory": "C:/Qt/file/palyer/build/.qtc_clangd", "file": "C:/Qt/file/palyer/backend/NetworkBackend.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-<PERSON>i", "-RTC1", "-clang:-std=c++17", "-MDd", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.43", "-DBACKEND_LIBRARY", "-DMINGW_HAS_SECURE_API", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_DEPRECATED_WARNINGS", "-DQT_GUI_LIB", "-DQT_HTTPSERVER_LIB", "-DQT_NETWORK_LIB", "-DQT_OPENGL_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DQT_WEBSOCKETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-Dbackend_EXPORTS", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Qt\\file\\palyer\\build\\backend\\backend_autogen\\include_Debug", "-IC:\\Qt\\file\\palyer\\backend", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQuick", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQml", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlIntegration", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlMeta", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlModels", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlWorkerScript", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtOpenGL", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtHttpServer", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtWebSockets", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Qt\\file\\palyer\\backend\\server\\ClientMode.h"], "directory": "C:/Qt/file/palyer/build/.qtc_clangd", "file": "C:/Qt/file/palyer/backend/server/ClientMode.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-<PERSON>i", "-RTC1", "-clang:-std=c++17", "-MDd", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.43", "-DBACKEND_LIBRARY", "-DMINGW_HAS_SECURE_API", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_DEPRECATED_WARNINGS", "-DQT_GUI_LIB", "-DQT_HTTPSERVER_LIB", "-DQT_NETWORK_LIB", "-DQT_OPENGL_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DQT_WEBSOCKETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-Dbackend_EXPORTS", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Qt\\file\\palyer\\build\\backend\\backend_autogen\\include_Debug", "-IC:\\Qt\\file\\palyer\\backend", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQuick", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQml", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlIntegration", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlMeta", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlModels", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlWorkerScript", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtOpenGL", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtHttpServer", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtWebSockets", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Qt\\file\\palyer\\backend\\server\\ServerMode.h"], "directory": "C:/Qt/file/palyer/build/.qtc_clangd", "file": "C:/Qt/file/palyer/backend/server/ServerMode.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-<PERSON>i", "-RTC1", "-clang:-std=c++17", "-MDd", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_OPENGL_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-U__clang__", "-U__clang_major__", "-U__clang_minor__", "-U__clang_patchlevel__", "-U__clang_version__", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Qt\\file\\palyer\\build\\palyer_autogen\\include_Debug", "-IC:\\Qt\\file\\palyer", "-IC:\\Qt\\file\\palyer\\backend", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQml\\6.9.1", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQml\\6.9.1\\QtQml", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore\\6.9.1", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore\\6.9.1\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQml", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlIntegration", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQuick", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlMeta", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlModels", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlWorkerScript", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\mingw_64\\include\\QtOpenGL", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "C:\\Qt\\file\\palyer\\main.cpp"], "directory": "C:/Qt/file/palyer/build/.qtc_clangd", "file": "C:/Qt/file/palyer/main.cpp"}]