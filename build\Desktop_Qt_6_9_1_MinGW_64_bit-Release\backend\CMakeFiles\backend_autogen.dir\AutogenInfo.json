{"AUTOGEN_COMMAND_LINE_LENGTH_MAX": 32000, "BUILD_DIR": "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/backend/backend_autogen", "CMAKE_BINARY_DIR": "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release", "CMAKE_CURRENT_BINARY_DIR": "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/backend", "CMAKE_CURRENT_SOURCE_DIR": "C:/Qt/file/palyer/backend", "CMAKE_EXECUTABLE": "C:/Qt/Tools/CMake_64/bin/cmake.exe", "CMAKE_LIST_FILES": ["C:/Qt/file/palyer/backend/CMakeLists.txt", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6ConfigVersion.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Config.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6ConfigExtras.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtInstallPaths.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Targets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtFeature.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CheckCXXCompilerFlag.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtFeatureCommon.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicAppleHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicGitHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicPluginHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicTargetHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicTestHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicToolHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Dependencies.cmake"], "CMAKE_SOURCE_DIR": "C:/Qt/file/palyer", "CROSS_CONFIG": false, "DEP_FILE": "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/backend/backend_autogen/deps", "DEP_FILE_RULE_NAME": "backend_autogen/timestamp", "HEADERS": [["C:/Qt/file/palyer/backend/ConversionTaskRunnable.h", "Mu", "EWIEGA46WW/moc_ConversionTaskRunnable.cpp", null], ["C:/Qt/file/palyer/backend/ImageConversionManager.h", "Mu", "EWIEGA46WW/moc_ImageConversionManager.cpp", null], ["C:/Qt/file/palyer/backend/ImageProcessor.h", "Mu", "EWIEGA46WW/moc_ImageProcessor.cpp", null], ["C:/Qt/file/palyer/backend/server/ClientMode.h", "Mu", "ORNZQ2F6DW/moc_ClientMode.cpp", null], ["C:/Qt/file/palyer/backend/server/ServerMode.h", "Mu", "ORNZQ2F6DW/moc_ServerMode.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/backend/backend_autogen/include", "MOC_COMPILATION_FILE": "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/backend/backend_autogen/mocs_compilation.cpp", "MOC_DEFINITIONS": ["BACKEND_LIBRARY", "MINGW_HAS_SECURE_API=1", "QT_CONCURRENT_LIB", "QT_CORE_LIB", "QT_DEPRECATED_WARNINGS", "QT_GUI_LIB", "QT_MULTIMEDIA_LIB", "QT_NETWORK_LIB", "QT_NO_DEBUG", "QT_OPENGL_LIB", "QT_QMLINTEGRATION_LIB", "QT_QMLMETA_LIB", "QT_QMLMODELS_LIB", "QT_QMLWORKERSCRIPT_LIB", "QT_QML_LIB", "QT_QUICK_LIB", "UNICODE", "WIN32", "WIN64", "_ENABLE_EXTENDED_ALIGNED_STORAGE", "_UNICODE", "_WIN64", "backend_EXPORTS"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": ["C:/Qt/file/palyer/backend", "C:/Qt/6.9.1/mingw_64/include/QtCore", "C:/Qt/6.9.1/mingw_64/include", "C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++", "C:/Qt/6.9.1/mingw_64/include/QtConcurrent", "C:/Qt/6.9.1/mingw_64/include/QtGui", "C:/Qt/6.9.1/mingw_64/include/QtQuick", "C:/Qt/6.9.1/mingw_64/include/QtQml", "C:/Qt/6.9.1/mingw_64/include/QtQmlIntegration", "C:/Qt/6.9.1/mingw_64/include/QtNetwork", "C:/Qt/6.9.1/mingw_64/include/QtQmlMeta", "C:/Qt/6.9.1/mingw_64/include/QtQmlModels", "C:/Qt/6.9.1/mingw_64/include/QtQmlWorkerScript", "C:/Qt/6.9.1/mingw_64/include/QtOpenGL", "C:/Qt/6.9.1/mingw_64/include/QtMultimedia", "C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++", "C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32", "C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward", "C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include", "C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed", "C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT", "Q_GADGET_EXPORT", "Q_ENUM_NS"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": ["C:/Qt/Tools/mingw1310_64/bin/g++.exe", "-std=gnu++17", "-dM", "-E", "-c", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeCXXCompilerABI.cpp"], "MOC_PREDEFS_FILE": "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/backend/backend_autogen/moc_predefs.h", "MOC_RELAXED_MODE": false, "MOC_SKIP": [], "MULTI_CONFIG": false, "PARALLEL": 20, "PARSE_CACHE_FILE": "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/backend/CMakeFiles/backend_autogen.dir/ParseCache.txt", "QT_MOC_EXECUTABLE": "C:/Qt/6.9.1/mingw_64/bin/moc.exe", "QT_UIC_EXECUTABLE": "", "QT_VERSION_MAJOR": 6, "QT_VERSION_MINOR": 9, "SETTINGS_FILE": "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/backend/CMakeFiles/backend_autogen.dir/AutogenUsed.txt", "SOURCES": [["C:/Qt/file/palyer/backend/ConversionTaskRunnable.cpp", "Mu", null], ["C:/Qt/file/palyer/backend/ImageConversionManager.cpp", "Mu", null], ["C:/Qt/file/palyer/backend/ImageProcessor.cpp", "Mu", null], ["C:/Qt/file/palyer/backend/server/ClientMode.cpp", "Mu", null], ["C:/Qt/file/palyer/backend/server/ServerMode.cpp", "Mu", null]], "USE_BETTER_GRAPH": true, "VERBOSITY": 0}