﻿#pragma once
#include <QObject>
#include <QStringList>
#include <QTcpServer>
#include <QTcpSocket>
#include <QNetworkAccessManager>
#include <QAtomicInt>
#include <QMutex>
#include <QUrl>
#include <QUrlQuery>
#include <QTimer>
#include <QByteArray>
#include <QHash>
#include <QQueue>
#include <functional>
#include <string>
#include <vector>
#include <cstdint>

using ConnectionID = uint64_t;

class ImageProcessor; // 前向声明

class ServerMode : public QObject {
    Q_OBJECT
    Q_PROPERTY(QStringList ipv4Addresses READ ipv4Addresses NOTIFY localAddressesChanged)
    Q_PROPERTY(QStringList ipv6Addresses READ ipv6Addresses NOTIFY localAddressesChanged)
    Q_PROPERTY(bool serverRunning READ isServerRunning NOTIFY serverStatusChanged)
    Q_PROPERTY(int serverPort READ serverPort NOTIFY serverStatusChanged)
    Q_PROPERTY(QString serverUrl READ serverUrl NOTIFY serverStatusChanged)
    Q_PROPERTY(int activeRequests READ activeRequests NOTIFY requestStatsChanged)
    Q_PROPERTY(QString authToken READ authToken WRITE setAuthToken NOTIFY authTokenChanged)

public:
    explicit ServerMode(QObject* parent = nullptr);
    ~ServerMode();

    // IP地址功能
    QStringList ipv4Addresses() const { return m_ipv4Addresses; }
    QStringList ipv6Addresses() const { return m_ipv6Addresses; }
    Q_INVOKABLE void refreshLocalAddresses();

    // HTTP服务功能
    Q_INVOKABLE bool startImageServer(int port = 3333);
    Q_INVOKABLE void stopImageServer();
    Q_INVOKABLE bool isServerRunning() const { return m_tcpServer != nullptr && m_tcpServer->isListening(); }
    Q_INVOKABLE int serverPort() const { return m_currentPort; }
    Q_INVOKABLE QString serverUrl() const;
    Q_INVOKABLE int activeRequests() const { return m_activeRequests.loadRelaxed(); }

    // 监听文件夹管理
    Q_INVOKABLE void addWatchFolder(const QString& folderPath);
    Q_INVOKABLE void removeWatchFolder(const QString& folderPath);
    Q_INVOKABLE QStringList getWatchFolders() const { return m_watchFolders; }

    // 令牌管理
    QString authToken() const { return m_authToken; }
    Q_INVOKABLE void setAuthToken(const QString& token);
    Q_INVOKABLE QString getDefaultToken() const { return "imageviewer2025"; }

    // 设置ImageProcessor引用
    void setImageProcessor(ImageProcessor* processor) { m_imageProcessor = processor; }

    // 原有接口保持兼容
    void listen(int port);
    void onClientConnected(std::function<void(ConnectionID)> handler);
    void closeSession(ConnectionID id);
    static std::vector<std::string> getLocalAddresses();

signals:
    void localAddressesChanged();
    void serverStatusChanged();
    void requestStatsChanged();
    void requestReceived(const QString &clientIP, const QString &path);
    void authTokenChanged();

private:
    // IP地址成员
    QStringList m_ipv4Addresses;
    QStringList m_ipv6Addresses;

    // HTTP服务成员
    QTcpServer* m_tcpServer = nullptr;
    ImageProcessor* m_imageProcessor = nullptr;
    int m_currentPort = 0;
    QAtomicInt m_activeRequests{0};
    QMutex m_serverMutex;
    QHash<QTcpSocket*, QByteArray> m_pendingRequests;
    QStringList m_watchFolders;
    QString m_authToken;

    // 重试请求结构体
    struct RetryRequest {
        QString imagePath;
        int retryCount;
        QTcpSocket* socket;
        QHash<QString, QString> headers;
        bool isThumbnail;
    };

    // 重试队列管理
    QQueue<RetryRequest> m_retryQueue;
    QTimer* m_retryTimer;
    int m_maxRetries = 3;
    int m_maxConcurrentRetries = 2;
    int m_currentRetries = 0;

    // 兼容性成员
    std::function<void(ConnectionID)> m_clientConnectedHandler;

private slots:
    void onNewConnection();
    void onSocketReadyRead();
    void onSocketDisconnected();

private:
    // HTTP请求处理
    void processHttpRequest(QTcpSocket* socket, const QByteArray& requestData);
    QByteArray handleImageList(const QString& clientPath, const QHash<QString, QString>& params);
    QByteArray handleRootFolderList();
    QByteArray handleImageData(const QString& imagePath, const QHash<QString, QString>& headers);
    QByteArray handleThumbnail(const QString& imagePath);
    QByteArray createHttpResponse(int statusCode, const QString& contentType, const QByteArray& body, const QHash<QString, QString>& headers = {});
    QByteArray createErrorResponse(const QString &error, int statusCode = 500);
    QString extractPathFromUrl(const QString &urlPath, const QString &prefix);
    bool isValidImagePath(const QString &path);
    bool isValidFolderPath(const QString &path);

    // 重试机制
    void handleRetryRequest(const RetryRequest& request);
    void processRetryQueue();
    bool isPathInWatchFolders(const QString &path);
    QString mapClientPathToServer(const QString &clientPath);
    QString mapServerPathToClient(const QString &serverPath);
    QHash<QString, QString> parseHttpHeaders(const QByteArray& requestData);
    QString parseHttpMethod(const QByteArray& requestData);
    QString parseHttpPath(const QByteArray& requestData);
};