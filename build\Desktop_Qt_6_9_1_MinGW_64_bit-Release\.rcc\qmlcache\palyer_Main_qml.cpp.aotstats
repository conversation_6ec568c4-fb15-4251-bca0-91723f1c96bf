[{"moduleFiles": [{"entries": [{"codegenSuccessful": true, "column": 12, "durationMicroseconds": 367, "errorMessage": "", "functionName": "flags", "line": 17}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 86, "errorMessage": "", "functionName": "backgroundSource", "line": 24}, {"codegenSuccessful": true, "column": 14, "durationMicroseconds": 29, "errorMessage": "", "functionName": "opacity", "line": 27}, {"codegenSuccessful": true, "column": 26, "durationMicroseconds": 296, "errorMessage": "", "functionName": "onVisibilityChanged", "line": 33}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 72, "errorMessage": "Cannot generate efficient code for LoadClosure", "functionName": "closeWithAnimation", "line": 450}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 65, "errorMessage": "Cannot generate efficient code for LoadClosure", "functionName": "minimizeWithAnimation", "line": 455}, {"codegenSuccessful": true, "column": 5, "durationMicroseconds": 445, "errorMessage": "", "functionName": "toggleFullScreen", "line": 460}, {"codegenSuccessful": true, "column": 23, "durationMicroseconds": 31, "errorMessage": "", "functionName": "fill", "line": 42}, {"codegenSuccessful": false, "column": 21, "durationMicroseconds": 40, "errorMessage": "Cannot access value for name isVideo", "functionName": "source", "line": 50}, {"codegenSuccessful": true, "column": 20, "durationMicroseconds": 74, "errorMessage": "", "functionName": "loops", "line": 52}, {"codegenSuccessful": true, "column": 23, "durationMicroseconds": 51, "errorMessage": "", "functionName": "fillMode", "line": 53}, {"codegenSuccessful": true, "column": 27, "durationMicroseconds": 42, "errorMessage": "", "functionName": "fill", "line": 48}, {"codegenSuccessful": false, "column": 21, "durationMicroseconds": 34, "errorMessage": "Cannot access value for name isVideo", "functionName": "source", "line": 59}, {"codegenSuccessful": true, "column": 23, "durationMicroseconds": 35, "errorMessage": "", "functionName": "fillMode", "line": 60}, {"codegenSuccessful": true, "column": 21, "durationMicroseconds": 36, "errorMessage": "", "functionName": "height", "line": 61}, {"codegenSuccessful": true, "column": 20, "durationMicroseconds": 161, "errorMessage": "", "functionName": "width", "line": 62}, {"codegenSuccessful": false, "column": 22, "durationMicroseconds": 21, "errorMessage": "Cannot access value for name isVideo", "functionName": "opacity", "line": 63}, {"codegenSuccessful": true, "column": 31, "durationMicroseconds": 25, "errorMessage": "", "functionName": "centerIn", "line": 57}, {"codegenSuccessful": true, "column": 81, "durationMicroseconds": 37, "errorMessage": "", "functionName": "type", "line": 64}, {"codegenSuccessful": false, "column": 21, "durationMicroseconds": 24, "errorMessage": "Cannot access value for name isVideo", "functionName": "source", "line": 70}, {"codegenSuccessful": true, "column": 27, "durationMicroseconds": 42, "errorMessage": "", "functionName": "fill", "line": 69}, {"codegenSuccessful": false, "column": 20, "durationMicroseconds": 51, "errorMessage": "Cannot access value for name isVideo", "functionName": "color", "line": 77}, {"codegenSuccessful": false, "column": 16, "durationMicroseconds": 16, "errorMessage": "Cannot access value for name isVideo", "functionName": "z", "line": 78}, {"codegenSuccessful": true, "column": 27, "durationMicroseconds": 24, "errorMessage": "", "functionName": "fill", "line": 76}, {"codegenSuccessful": false, "column": 24, "durationMicroseconds": 21, "errorMessage": "Cannot access value for name mainw", "functionName": "width", "line": 82}, {"codegenSuccessful": false, "column": 25, "durationMicroseconds": 13, "errorMessage": "Cannot access value for name mainw", "functionName": "height", "line": 83}, {"codegenSuccessful": false, "column": 25, "durationMicroseconds": 12, "errorMessage": "Cannot access value for name mainw", "functionName": "radius", "line": 84}, {"codegenSuccessful": true, "column": 27, "durationMicroseconds": 30, "errorMessage": "", "functionName": "fill", "line": 89}, {"codegenSuccessful": true, "column": 24, "durationMicroseconds": 51, "errorMessage": "", "functionName": "width", "line": 94}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 63, "errorMessage": "", "functionName": "onPressed", "line": 99}, {"codegenSuccessful": true, "column": 35, "durationMicroseconds": 24, "errorMessage": "", "functionName": "fill", "line": 98}, {"codegenSuccessful": true, "column": 34, "durationMicroseconds": 83, "errorMessage": "", "functionName": "top", "line": 105}, {"codegenSuccessful": true, "column": 35, "durationMicroseconds": 47, "errorMessage": "", "functionName": "left", "line": 106}, {"codegenSuccessful": true, "column": 56, "durationMicroseconds": 95, "errorMessage": "", "functionName": "radius", "line": 112}, {"codegenSuccessful": true, "column": 90, "durationMicroseconds": 36, "errorMessage": "", "functionName": "type", "line": 114}, {"codegenSuccessful": true, "column": 92, "durationMicroseconds": 32, "errorMessage": "", "functionName": "type", "line": 115}, {"codegenSuccessful": true, "column": 38, "durationMicroseconds": 39, "errorMessage": "", "functionName": "visible", "line": 121}, {"codegenSuccessful": true, "column": 47, "durationMicroseconds": 27, "errorMessage": "", "functionName": "centerIn", "line": 119}, {"codegenSuccessful": false, "column": 40, "durationMicroseconds": 219, "errorMessage": "method closeWithAnimation cannot be resolved.", "functionName": "onClicked", "line": 127}, {"codegenSuccessful": true, "column": 40, "durationMicroseconds": 101, "errorMessage": "", "functionName": "onEntered", "line": 128}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 79, "errorMessage": "", "functionName": "onExited", "line": 129}, {"codegenSuccessful": true, "column": 43, "durationMicroseconds": 27, "errorMessage": "", "functionName": "fill", "line": 126}, {"codegenSuccessful": true, "column": 56, "durationMicroseconds": 75, "errorMessage": "", "functionName": "radius", "line": 135}, {"codegenSuccessful": true, "column": 90, "durationMicroseconds": 48, "errorMessage": "", "functionName": "type", "line": 137}, {"codegenSuccessful": true, "column": 92, "durationMicroseconds": 40, "errorMessage": "", "functionName": "type", "line": 138}, {"codegenSuccessful": true, "column": 38, "durationMicroseconds": 46, "errorMessage": "", "functionName": "visible", "line": 144}, {"codegenSuccessful": true, "column": 47, "durationMicroseconds": 26, "errorMessage": "", "functionName": "centerIn", "line": 142}, {"codegenSuccessful": false, "column": 40, "durationMicroseconds": 226, "errorMessage": "method minimizeWithAnimation cannot be resolved.", "functionName": "onClicked", "line": 151}, {"codegenSuccessful": true, "column": 40, "durationMicroseconds": 116, "errorMessage": "", "functionName": "onEntered", "line": 152}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 85, "errorMessage": "", "functionName": "onExited", "line": 153}, {"codegenSuccessful": true, "column": 43, "durationMicroseconds": 28, "errorMessage": "", "functionName": "fill", "line": 149}, {"codegenSuccessful": true, "column": 56, "durationMicroseconds": 74, "errorMessage": "", "functionName": "radius", "line": 159}, {"codegenSuccessful": true, "column": 90, "durationMicroseconds": 38, "errorMessage": "", "functionName": "type", "line": 161}, {"codegenSuccessful": true, "column": 92, "durationMicroseconds": 32, "errorMessage": "", "functionName": "type", "line": 162}, {"codegenSuccessful": true, "column": 38, "durationMicroseconds": 41, "errorMessage": "", "functionName": "visible", "line": 168}, {"codegenSuccessful": true, "column": 47, "durationMicroseconds": 25, "errorMessage": "", "functionName": "centerIn", "line": 166}, {"codegenSuccessful": false, "column": 40, "durationMicroseconds": 213, "errorMessage": "method toggleFullScreen cannot be resolved.", "functionName": "onClicked", "line": 175}, {"codegenSuccessful": true, "column": 40, "durationMicroseconds": 115, "errorMessage": "", "functionName": "onEntered", "line": 176}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 88, "errorMessage": "", "functionName": "onExited", "line": 177}, {"codegenSuccessful": true, "column": 43, "durationMicroseconds": 53, "errorMessage": "", "functionName": "fill", "line": 174}, {"codegenSuccessful": false, "column": 27, "durationMicroseconds": 1170, "errorMessage": "Cannot load property appText from const QMetaObject.", "functionName": "text", "line": 185}, {"codegenSuccessful": true, "column": 28, "durationMicroseconds": 44, "errorMessage": "", "functionName": "width", "line": 187}, {"codegenSuccessful": true, "column": 42, "durationMicroseconds": 47, "errorMessage": "", "functionName": "horizontalAlignment", "line": 188}, {"codegenSuccessful": true, "column": 24, "durationMicroseconds": 356, "errorMessage": "", "functionName": "x", "line": 191}, {"codegenSuccessful": true, "column": 45, "durationMicroseconds": 119, "errorMessage": "", "functionName": "verticalCenter", "line": 190}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 102, "errorMessage": "", "functionName": "right", "line": 197}, {"codegenSuccessful": true, "column": 45, "durationMicroseconds": 60, "errorMessage": "", "functionName": "verticalCenter", "line": 198}, {"codegenSuccessful": true, "column": 56, "durationMicroseconds": 62, "errorMessage": "", "functionName": "radius", "line": 201}, {"codegenSuccessful": true, "column": 47, "durationMicroseconds": 51, "errorMessage": "", "functionName": "centerIn", "line": 208}, {"codegenSuccessful": false, "column": 40, "durationMicroseconds": 266, "errorMessage": "Cannot access value for name serverMode", "functionName": "onClicked", "line": 214}, {"codegenSuccessful": true, "column": 43, "durationMicroseconds": 61, "errorMessage": "", "functionName": "fill", "line": 213}, {"codegenSuccessful": true, "column": 24, "durationMicroseconds": 57, "errorMessage": "", "functionName": "width", "line": 229}, {"codegenSuccessful": false, "column": 24, "durationMicroseconds": 130, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 230}, {"codegenSuccessful": true, "column": 24, "durationMicroseconds": 39, "errorMessage": "", "functionName": "width", "line": 235}, {"codegenSuccessful": true, "column": 25, "durationMicroseconds": 92, "errorMessage": "", "functionName": "height", "line": 236}, {"codegenSuccessful": true, "column": 29, "durationMicroseconds": 145, "errorMessage": "", "functionName": "height", "line": 241}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 86, "errorMessage": "", "functionName": "width", "line": 245}, {"codegenSuccessful": true, "column": 74, "durationMicroseconds": 56, "errorMessage": "", "functionName": "verticalCenter", "line": 249}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 29, "errorMessage": "", "functionName": "width", "line": 254}, {"codegenSuccessful": true, "column": 50, "durationMicroseconds": 42, "errorMessage": "", "functionName": "horizontalAlignment", "line": 255}, {"codegenSuccessful": true, "column": 53, "durationMicroseconds": 49, "errorMessage": "", "functionName": "verticalCenter", "line": 257}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 35, "errorMessage": "", "functionName": "width", "line": 260}, {"codegenSuccessful": true, "column": 37, "durationMicroseconds": 30, "errorMessage": "", "functionName": "height", "line": 261}, {"codegenSuccessful": false, "column": 35, "durationMicroseconds": 111, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 262}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 55, "errorMessage": "", "functionName": "x", "line": 263}, {"codegenSuccessful": true, "column": 96, "durationMicroseconds": 34, "errorMessage": "", "functionName": "type", "line": 264}, {"codegenSuccessful": true, "column": 44, "durationMicroseconds": 49, "errorMessage": "", "functionName": "onEntered", "line": 267}, {"codegenSuccessful": true, "column": 43, "durationMicroseconds": 39, "errorMessage": "", "functionName": "onExited", "line": 268}, {"codegenSuccessful": true, "column": 47, "durationMicroseconds": 22, "errorMessage": "", "functionName": "fill", "line": 266}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 61, "errorMessage": "", "functionName": "width", "line": 274}, {"codegenSuccessful": true, "column": 74, "durationMicroseconds": 44, "errorMessage": "", "functionName": "verticalCenter", "line": 278}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 72, "errorMessage": "", "functionName": "width", "line": 283}, {"codegenSuccessful": true, "column": 50, "durationMicroseconds": 30, "errorMessage": "", "functionName": "horizontalAlignment", "line": 284}, {"codegenSuccessful": true, "column": 53, "durationMicroseconds": 40, "errorMessage": "", "functionName": "verticalCenter", "line": 286}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 29, "errorMessage": "", "functionName": "width", "line": 289}, {"codegenSuccessful": true, "column": 37, "durationMicroseconds": 48, "errorMessage": "", "functionName": "height", "line": 290}, {"codegenSuccessful": false, "column": 35, "durationMicroseconds": 104, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 291}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 57, "errorMessage": "", "functionName": "x", "line": 292}, {"codegenSuccessful": true, "column": 96, "durationMicroseconds": 35, "errorMessage": "", "functionName": "type", "line": 293}, {"codegenSuccessful": true, "column": 44, "durationMicroseconds": 55, "errorMessage": "", "functionName": "onEntered", "line": 296}, {"codegenSuccessful": true, "column": 43, "durationMicroseconds": 61, "errorMessage": "", "functionName": "onExited", "line": 297}, {"codegenSuccessful": true, "column": 47, "durationMicroseconds": 25, "errorMessage": "", "functionName": "fill", "line": 295}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 64, "errorMessage": "", "functionName": "width", "line": 303}, {"codegenSuccessful": true, "column": 74, "durationMicroseconds": 49, "errorMessage": "", "functionName": "verticalCenter", "line": 307}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 24, "errorMessage": "", "functionName": "width", "line": 312}, {"codegenSuccessful": true, "column": 50, "durationMicroseconds": 34, "errorMessage": "", "functionName": "horizontalAlignment", "line": 313}, {"codegenSuccessful": true, "column": 53, "durationMicroseconds": 46, "errorMessage": "", "functionName": "verticalCenter", "line": 315}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 32, "errorMessage": "", "functionName": "width", "line": 318}, {"codegenSuccessful": true, "column": 37, "durationMicroseconds": 76, "errorMessage": "", "functionName": "height", "line": 319}, {"codegenSuccessful": false, "column": 35, "durationMicroseconds": 114, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 320}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 55, "errorMessage": "", "functionName": "x", "line": 321}, {"codegenSuccessful": true, "column": 96, "durationMicroseconds": 33, "errorMessage": "", "functionName": "type", "line": 322}, {"codegenSuccessful": true, "column": 44, "durationMicroseconds": 58, "errorMessage": "", "functionName": "onEntered", "line": 325}, {"codegenSuccessful": true, "column": 43, "durationMicroseconds": 40, "errorMessage": "", "functionName": "onExited", "line": 326}, {"codegenSuccessful": true, "column": 44, "durationMicroseconds": 66, "errorMessage": "", "functionName": "onClicked", "line": 327}, {"codegenSuccessful": true, "column": 47, "durationMicroseconds": 22, "errorMessage": "", "functionName": "fill", "line": 324}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 53, "errorMessage": "", "functionName": "width", "line": 334}, {"codegenSuccessful": true, "column": 74, "durationMicroseconds": 41, "errorMessage": "", "functionName": "verticalCenter", "line": 338}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 23, "errorMessage": "", "functionName": "width", "line": 343}, {"codegenSuccessful": true, "column": 50, "durationMicroseconds": 33, "errorMessage": "", "functionName": "horizontalAlignment", "line": 344}, {"codegenSuccessful": true, "column": 53, "durationMicroseconds": 43, "errorMessage": "", "functionName": "verticalCenter", "line": 346}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 31, "errorMessage": "", "functionName": "width", "line": 349}, {"codegenSuccessful": true, "column": 37, "durationMicroseconds": 29, "errorMessage": "", "functionName": "height", "line": 350}, {"codegenSuccessful": false, "column": 35, "durationMicroseconds": 161, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 351}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 64, "errorMessage": "", "functionName": "x", "line": 352}, {"codegenSuccessful": true, "column": 96, "durationMicroseconds": 36, "errorMessage": "", "functionName": "type", "line": 353}, {"codegenSuccessful": true, "column": 44, "durationMicroseconds": 53, "errorMessage": "", "functionName": "onEntered", "line": 356}, {"codegenSuccessful": true, "column": 43, "durationMicroseconds": 48, "errorMessage": "", "functionName": "onExited", "line": 357}, {"codegenSuccessful": true, "column": 44, "durationMicroseconds": 67, "errorMessage": "", "functionName": "onClicked", "line": 358}, {"codegenSuccessful": true, "column": 47, "durationMicroseconds": 23, "errorMessage": "", "functionName": "fill", "line": 355}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 31, "errorMessage": "", "functionName": "height", "line": 366}, {"codegenSuccessful": false, "column": 28, "durationMicroseconds": 99, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 367}, {"codegenSuccessful": true, "column": 28, "durationMicroseconds": 72, "errorMessage": "", "functionName": "width", "line": 372}, {"codegenSuccessful": true, "column": 29, "durationMicroseconds": 28, "errorMessage": "", "functionName": "height", "line": 373}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 31, "errorMessage": "", "functionName": "width", "line": 377}, {"codegenSuccessful": true, "column": 53, "durationMicroseconds": 29, "errorMessage": "", "functionName": "height", "line": 377}, {"codegenSuccessful": false, "column": 35, "durationMicroseconds": 352, "errorMessage": "Cannot generate efficient code for TypeofName", "functionName": "onLoaded", "line": 380}, {"codegenSuccessful": true, "column": 22, "durationMicroseconds": 86, "errorMessage": "", "functionName": "nameFilters", "line": 401}, {"codegenSuccessful": false, "column": 21, "durationMicroseconds": 105, "errorMessage": "Cannot find name backgroundSource", "functionName": "onAccepted", "line": 402}, {"codegenSuccessful": false, "column": 19, "durationMicroseconds": 121, "errorMessage": "Type (component in C:/Qt/file/palyer/Main.qml)::item with type QObject does not have a property opacity for writing", "functionName": "onLoaded", "line": 413}, {"codegenSuccessful": true, "column": 17, "durationMicroseconds": 50, "errorMessage": "", "functionName": "target", "line": 428}, {"codegenSuccessful": false, "column": 21, "durationMicroseconds": 40, "errorMessage": "method afterFadeOut cannot be resolved.", "functionName": "onFinished", "line": 432}, {"codegenSuccessful": true, "column": 17, "durationMicroseconds": 47, "errorMessage": "", "functionName": "target", "line": 443}], "filePath": "C:/Qt/file/palyer/Main.qml"}], "moduleId": "palyer(palyer)"}]