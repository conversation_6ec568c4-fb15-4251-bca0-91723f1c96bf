[{"moduleFiles": [{"entries": [{"codegenSuccessful": true, "column": 8, "durationMicroseconds": 318, "errorMessage": "", "functionName": "x", "line": 15}, {"codegenSuccessful": true, "column": 8, "durationMicroseconds": 134, "errorMessage": "", "functionName": "y", "line": 16}, {"codegenSuccessful": true, "column": 12, "durationMicroseconds": 95, "errorMessage": "", "functionName": "flags", "line": 17}, {"codegenSuccessful": true, "column": 14, "durationMicroseconds": 77, "errorMessage": "", "functionName": "opacity", "line": 19}, {"codegenSuccessful": false, "column": 35, "durationMicroseconds": 35, "errorMessage": "Cannot generate efficient code for storing an array in a non-sequence type", "functionName": "remoteImageList", "line": 37}, {"codegenSuccessful": true, "column": 5, "durationMicroseconds": 58, "errorMessage": "", "functionName": "closeWindow", "line": 50}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 244, "errorMessage": "Cannot generate efficient code for call to untyped JavaScript function", "functionName": "loadPreviousImage", "line": 69}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 222, "errorMessage": "Cannot load property length from (component in C:/Qt/file/palyer/compoment/ImagePreviewWindow.qml)::remoteImageList with type QVariant.", "functionName": "loadNextImage", "line": 79}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 138, "errorMessage": "Cannot generate efficient code for call to untyped JavaScript function", "functionName": "toggleFullScreen", "line": 89}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "loadRemoteImageByIndex", "line": 96}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "loadImageByIndex", "line": 125}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "updateImageSize", "line": 156}, {"codegenSuccessful": true, "column": 5, "durationMicroseconds": 604, "errorMessage": "", "functionName": "centerImage", "line": 210}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 290, "errorMessage": "function without return type annotation returns QString. This may prevent proper compilation to Cpp.", "functionName": "getRemoteImageInfo", "line": 533}, {"codegenSuccessful": true, "column": 73, "durationMicroseconds": 36, "errorMessage": "", "functionName": "type", "line": 22}, {"codegenSuccessful": true, "column": 22, "durationMicroseconds": 115, "errorMessage": "", "functionName": "onTriggered", "line": 46}, {"codegenSuccessful": false, "column": 22, "durationMicroseconds": 106, "errorMessage": "Cannot access value for name isFullScreen", "functionName": "onTriggered", "line": 59}, {"codegenSuccessful": false, "column": 17, "durationMicroseconds": 28, "errorMessage": "Cannot access value for name clientMode", "functionName": "target", "line": 221}, {"codegenSuccessful": false, "column": 9, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "onImageCached", "line": 222}, {"codegenSuccessful": false, "column": 28, "durationMicroseconds": 79, "errorMessage": "Cannot generate efficient code for LoadClosure", "functionName": "onCompleted", "line": 233}, {"codegenSuccessful": true, "column": 23, "durationMicroseconds": 23, "errorMessage": "", "functionName": "fill", "line": 237}, {"codegenSuccessful": false, "column": 29, "durationMicroseconds": 125, "errorMessage": "method loadPreviousImage cannot be resolved.", "functionName": "onLeftPressed", "line": 243}, {"codegenSuccessful": false, "column": 30, "durationMicroseconds": 86, "errorMessage": "method loadNextImage cannot be resolved.", "functionName": "onRightPressed", "line": 244}, {"codegenSuccessful": false, "column": 31, "durationMicroseconds": 76, "errorMessage": "method closeWindow cannot be resolved.", "functionName": "onEscapePressed", "line": 245}, {"codegenSuccessful": false, "column": 30, "durationMicroseconds": 89, "errorMessage": "method updateImageSize cannot be resolved.", "functionName": "onSpacePressed", "line": 246}, {"codegenSuccessful": true, "column": 20, "durationMicroseconds": 30, "errorMessage": "", "functionName": "width", "line": 251}, {"codegenSuccessful": true, "column": 28, "durationMicroseconds": 46, "errorMessage": "", "functionName": "onPressed", "line": 256}, {"codegenSuccessful": true, "column": 31, "durationMicroseconds": 20, "errorMessage": "", "functionName": "fill", "line": 255}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 42, "errorMessage": "", "functionName": "top", "line": 262}, {"codegenSuccessful": true, "column": 50, "durationMicroseconds": 38, "errorMessage": "", "functionName": "left", "line": 262}, {"codegenSuccessful": false, "column": 28, "durationMicroseconds": 229, "errorMessage": "Cannot generate efficient code for LoadClosure", "functionName": "model", "line": 266}, {"codegenSuccessful": true, "column": 56, "durationMicroseconds": 42, "errorMessage": "", "functionName": "radius", "line": 273}, {"codegenSuccessful": false, "column": 32, "durationMicroseconds": 91, "errorMessage": "Cannot access value for name modelData", "functionName": "color", "line": 274}, {"codegenSuccessful": true, "column": 34, "durationMicroseconds": 65, "errorMessage": "", "functionName": "opacity", "line": 275}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 50, "errorMessage": "", "functionName": "scale", "line": 276}, {"codegenSuccessful": true, "column": 91, "durationMicroseconds": 29, "errorMessage": "", "functionName": "type", "line": 278}, {"codegenSuccessful": true, "column": 93, "durationMicroseconds": 25, "errorMessage": "", "functionName": "type", "line": 279}, {"codegenSuccessful": false, "column": 35, "durationMicroseconds": 202, "errorMessage": "Cannot access value for name modelData", "functionName": "text", "line": 282}, {"codegenSuccessful": true, "column": 38, "durationMicroseconds": 39, "errorMessage": "", "functionName": "visible", "line": 289}, {"codegenSuccessful": true, "column": 47, "durationMicroseconds": 23, "errorMessage": "", "functionName": "centerIn", "line": 284}, {"codegenSuccessful": false, "column": 36, "durationMicroseconds": 71, "errorMessage": "Cannot access value for name modelData", "functionName": "x", "line": 286}, {"codegenSuccessful": false, "column": 36, "durationMicroseconds": 52, "errorMessage": "Cannot access value for name modelData", "functionName": "y", "line": 287}, {"codegenSuccessful": false, "column": 40, "durationMicroseconds": 114, "errorMessage": "Cannot access value for name modelData", "functionName": "onClicked", "line": 297}, {"codegenSuccessful": true, "column": 43, "durationMicroseconds": 24, "errorMessage": "", "functionName": "fill", "line": 296}, {"codegenSuccessful": true, "column": 24, "durationMicroseconds": 121, "errorMessage": "", "functionName": "width", "line": 307}, {"codegenSuccessful": true, "column": 89, "durationMicroseconds": 30, "errorMessage": "", "functionName": "height", "line": 307}, {"codegenSuccessful": true, "column": 35, "durationMicroseconds": 19, "errorMessage": "", "functionName": "centerIn", "line": 306}, {"codegenSuccessful": false, "column": 27, "durationMicroseconds": 232, "errorMessage": "Cannot access value for name isRemoteMode", "functionName": "text", "line": 312}, {"codegenSuccessful": true, "column": 42, "durationMicroseconds": 32, "errorMessage": "", "functionName": "horizontalAlignment", "line": 314}, {"codegenSuccessful": true, "column": 80, "durationMicroseconds": 28, "errorMessage": "", "functionName": "verticalAlignment", "line": 314}, {"codegenSuccessful": true, "column": 106, "durationMicroseconds": 25, "errorMessage": "", "functionName": "elide", "line": 314}, {"codegenSuccessful": true, "column": 30, "durationMicroseconds": 61, "errorMessage": "", "functionName": "opacity", "line": 315}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 20, "errorMessage": "", "functionName": "centerIn", "line": 311}, {"codegenSuccessful": true, "column": 68, "durationMicroseconds": 45, "errorMessage": "", "functionName": "visible", "line": 320}, {"codegenSuccessful": true, "column": 30, "durationMicroseconds": 54, "errorMessage": "", "functionName": "opacity", "line": 321}, {"codegenSuccessful": true, "column": 35, "durationMicroseconds": 20, "errorMessage": "", "functionName": "fill", "line": 320}, {"codegenSuccessful": true, "column": 58, "durationMicroseconds": 34, "errorMessage": "", "functionName": "fill", "line": 327}, {"codegenSuccessful": true, "column": 24, "durationMicroseconds": 94, "errorMessage": "", "functionName": "width", "line": 337}, {"codegenSuccessful": false, "column": 23, "durationMicroseconds": 404, "errorMessage": "Cannot access value for name isRemoteMode", "functionName": "text", "line": 340}, {"codegenSuccessful": true, "column": 19, "durationMicroseconds": 102, "errorMessage": "", "functionName": "y", "line": 342}, {"codegenSuccessful": true, "column": 28, "durationMicroseconds": 43, "errorMessage": "", "functionName": "right", "line": 334}, {"codegenSuccessful": false, "column": 29, "durationMicroseconds": 28, "errorMessage": "Cannot access value for name isRemoteMode", "functionName": "target", "line": 346}, {"codegenSuccessful": false, "column": 21, "durationMicroseconds": 65, "errorMessage": "method getRemoteImageInfo cannot be resolved.", "functionName": "onStatusChanged", "line": 347}, {"codegenSuccessful": true, "column": 20, "durationMicroseconds": 29, "errorMessage": "", "functionName": "width", "line": 360}, {"codegenSuccessful": false, "column": 20, "durationMicroseconds": 97, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 361}, {"codegenSuccessful": true, "column": 26, "durationMicroseconds": 42, "errorMessage": "", "functionName": "top", "line": 359}, {"codegenSuccessful": true, "column": 20, "durationMicroseconds": 83, "errorMessage": "", "functionName": "width", "line": 372}, {"codegenSuccessful": true, "column": 27, "durationMicroseconds": 72, "errorMessage": "", "functionName": "contentWidth", "line": 373}, {"codegenSuccessful": true, "column": 61, "durationMicroseconds": 29, "errorMessage": "", "functionName": "contentHeight", "line": 373}, {"codegenSuccessful": true, "column": 22, "durationMicroseconds": 68, "errorMessage": "", "functionName": "top", "line": 368}, {"codegenSuccessful": true, "column": 25, "durationMicroseconds": 37, "errorMessage": "", "functionName": "bottom", "line": 369}, {"codegenSuccessful": true, "column": 35, "durationMicroseconds": 36, "errorMessage": "", "functionName": "horizontalCenter", "line": 370}, {"codegenSuccessful": true, "column": 25, "durationMicroseconds": 36, "errorMessage": "", "functionName": "policy", "line": 382}, {"codegenSuccessful": true, "column": 26, "durationMicroseconds": 67, "errorMessage": "", "functionName": "visible", "line": 383}, {"codegenSuccessful": true, "column": 26, "durationMicroseconds": 62, "errorMessage": "", "functionName": "opacity", "line": 385}, {"codegenSuccessful": true, "column": 85, "durationMicroseconds": 28, "errorMessage": "", "functionName": "type", "line": 387}, {"codegenSuccessful": true, "column": 47, "durationMicroseconds": 40, "errorMessage": "", "functionName": "radius", "line": 390}, {"codegenSuccessful": false, "column": 28, "durationMicroseconds": 59, "errorMessage": "Cannot load property pressed from (component in C:/Qt/file/palyer/compoment/ImagePreviewWindow.qml)::parent with type QQuickItem.", "functionName": "color", "line": 391}, {"codegenSuccessful": true, "column": 54, "durationMicroseconds": 47, "errorMessage": "", "functionName": "acceptedButtons", "line": 399}, {"codegenSuccessful": true, "column": 82, "durationMicroseconds": 29, "errorMessage": "", "functionName": "cursor<PERSON><PERSON>pe", "line": 399}, {"codegenSuccessful": true, "column": 17, "durationMicroseconds": 55, "errorMessage": "", "functionName": "showCursor", "line": 403}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 1022, "errorMessage": "", "functionName": "onPositionChanged", "line": 409}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 20, "errorMessage": "", "functionName": "onPositionChanged", "line": 409}, {"codegenSuccessful": true, "column": 28, "durationMicroseconds": 21, "errorMessage": "", "functionName": "onEntered", "line": 424}, {"codegenSuccessful": true, "column": 27, "durationMicroseconds": 70, "errorMessage": "", "functionName": "onExited", "line": 425}, {"codegenSuccessful": true, "column": 28, "durationMicroseconds": 101, "errorMessage": "", "functionName": "onPressed", "line": 426}, {"codegenSuccessful": true, "column": 28, "durationMicroseconds": 17, "errorMessage": "", "functionName": "onPressed", "line": 426}, {"codegenSuccessful": true, "column": 29, "durationMicroseconds": 29, "errorMessage": "", "functionName": "onReleased", "line": 427}, {"codegenSuccessful": true, "column": 26, "durationMicroseconds": 2923, "errorMessage": "", "functionName": "onWheel", "line": 430}, {"codegenSuccessful": true, "column": 26, "durationMicroseconds": 22, "errorMessage": "", "functionName": "onWheel", "line": 430}, {"codegenSuccessful": true, "column": 31, "durationMicroseconds": 26, "errorMessage": "", "functionName": "fill", "line": 398}, {"codegenSuccessful": true, "column": 27, "durationMicroseconds": 34, "errorMessage": "", "functionName": "fillMode", "line": 468}, {"codegenSuccessful": true, "column": 26, "durationMicroseconds": 85, "errorMessage": "", "functionName": "opacity", "line": 471}, {"codegenSuccessful": false, "column": 34, "durationMicroseconds": 103, "errorMessage": "Cannot generate efficient code for incomparable types QString and (component in C:/Qt/file/palyer/compoment/ImagePreviewWindow.qml)::source with type QUrl", "functionName": "onSourceChanged", "line": 475}, {"codegenSuccessful": false, "column": 34, "durationMicroseconds": 186, "errorMessage": "method updateImageSize cannot be resolved.", "functionName": "onStatusChanged", "line": 476}, {"codegenSuccessful": true, "column": 85, "durationMicroseconds": 40, "errorMessage": "", "functionName": "type", "line": 473}, {"codegenSuccessful": true, "column": 30, "durationMicroseconds": 60, "errorMessage": "", "functionName": "running", "line": 489}, {"codegenSuccessful": true, "column": 30, "durationMicroseconds": 22, "errorMessage": "", "functionName": "visible", "line": 490}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 20, "errorMessage": "", "functionName": "centerIn", "line": 488}, {"codegenSuccessful": false, "column": 30, "durationMicroseconds": 40, "errorMessage": "Cannot load property _showErrorOverlay from (component in C:/Qt/file/palyer/compoment/ImagePreviewWindow.qml)::parent with type QQuickItem.", "functionName": "visible", "line": 495}, {"codegenSuccessful": true, "column": 35, "durationMicroseconds": 19, "errorMessage": "", "functionName": "fill", "line": 496}, {"codegenSuccessful": true, "column": 43, "durationMicroseconds": 20, "errorMessage": "", "functionName": "centerIn", "line": 500}, {"codegenSuccessful": true, "column": 55, "durationMicroseconds": 40, "errorMessage": "", "functionName": "horizontalCenter", "line": 504}, {"codegenSuccessful": false, "column": 35, "durationMicroseconds": 231, "errorMessage": "Cannot access value for name isRemoteMode", "functionName": "text", "line": 513}, {"codegenSuccessful": true, "column": 55, "durationMicroseconds": 40, "errorMessage": "", "functionName": "horizontalCenter", "line": 512}, {"codegenSuccessful": true, "column": 55, "durationMicroseconds": 45, "errorMessage": "", "functionName": "horizontalCenter", "line": 520}], "filePath": "C:/Qt/file/palyer/compoment/ImagePreviewWindow.qml"}], "moduleId": "palyer(palyer)"}]