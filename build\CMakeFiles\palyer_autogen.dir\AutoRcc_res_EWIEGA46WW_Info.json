{"BUILD_DIR": "C:/Qt/file/palyer/build/palyer_autogen", "CMAKE_BINARY_DIR": "C:/Qt/file/palyer/build", "CMAKE_CURRENT_BINARY_DIR": "C:/Qt/file/palyer/build", "CMAKE_CURRENT_SOURCE_DIR": "C:/Qt/file/palyer", "CMAKE_SOURCE_DIR": "C:/Qt/file/palyer", "CROSS_CONFIG": false, "GENERATOR": "Visual Studio 17 2022", "INCLUDE_DIR": "C:/Qt/file/palyer/build/palyer_autogen/include", "INCLUDE_DIR_Debug": "C:/Qt/file/palyer/build/palyer_autogen/include_Debug", "INCLUDE_DIR_MinSizeRel": "C:/Qt/file/palyer/build/palyer_autogen/include_MinSizeRel", "INCLUDE_DIR_RelWithDebInfo": "C:/Qt/file/palyer/build/palyer_autogen/include_RelWithDebInfo", "INCLUDE_DIR_Release": "C:/Qt/file/palyer/build/palyer_autogen/include_Release", "INPUTS": [], "INPUTS_Debug": ["C:/Qt/file/palyer/source/7btrrd.mp4", "C:/Qt/file/palyer/source/img/music.svg", "C:/Qt/file/palyer/source/img/seethings.svg", "C:/Qt/file/palyer/source/img/network.svg", "C:/Qt/file/palyer/source/img/movie.svg", "C:/Qt/file/palyer/source/img/picture.svg", "C:/Qt/file/palyer/source/img/symble.svg", "C:/Qt/file/palyer/compoment/ImagePreviewWindow.qml", "C:/Qt/file/palyer/compoment/imagePage.qml", "C:/Qt/file/palyer/compoment/AppState.qml", "C:/Qt/file/palyer/compoment/SettingsPage.qml", "C:/Qt/file/palyer/compoment/RemoteImagePage.qml", "C:/Qt/file/palyer/compoment/template/BaseWindowTemplate.qml", "C:/Qt/file/palyer/compoment/toolbar/ConversionWindow.qml"], "INPUTS_MinSizeRel": ["C:/Qt/file/palyer/source/7btrrd.mp4", "C:/Qt/file/palyer/source/img/music.svg", "C:/Qt/file/palyer/source/img/seethings.svg", "C:/Qt/file/palyer/source/img/network.svg", "C:/Qt/file/palyer/source/img/movie.svg", "C:/Qt/file/palyer/source/img/picture.svg", "C:/Qt/file/palyer/source/img/symble.svg", "C:/Qt/file/palyer/compoment/ImagePreviewWindow.qml", "C:/Qt/file/palyer/compoment/imagePage.qml", "C:/Qt/file/palyer/compoment/AppState.qml", "C:/Qt/file/palyer/compoment/SettingsPage.qml", "C:/Qt/file/palyer/compoment/RemoteImagePage.qml", "C:/Qt/file/palyer/compoment/template/BaseWindowTemplate.qml", "C:/Qt/file/palyer/compoment/toolbar/ConversionWindow.qml"], "INPUTS_RelWithDebInfo": ["C:/Qt/file/palyer/source/7btrrd.mp4", "C:/Qt/file/palyer/source/img/music.svg", "C:/Qt/file/palyer/source/img/seethings.svg", "C:/Qt/file/palyer/source/img/network.svg", "C:/Qt/file/palyer/source/img/movie.svg", "C:/Qt/file/palyer/source/img/picture.svg", "C:/Qt/file/palyer/source/img/symble.svg", "C:/Qt/file/palyer/compoment/ImagePreviewWindow.qml", "C:/Qt/file/palyer/compoment/imagePage.qml", "C:/Qt/file/palyer/compoment/AppState.qml", "C:/Qt/file/palyer/compoment/SettingsPage.qml", "C:/Qt/file/palyer/compoment/RemoteImagePage.qml", "C:/Qt/file/palyer/compoment/template/BaseWindowTemplate.qml", "C:/Qt/file/palyer/compoment/toolbar/ConversionWindow.qml"], "INPUTS_Release": ["C:/Qt/file/palyer/source/7btrrd.mp4", "C:/Qt/file/palyer/source/img/music.svg", "C:/Qt/file/palyer/source/img/seethings.svg", "C:/Qt/file/palyer/source/img/network.svg", "C:/Qt/file/palyer/source/img/movie.svg", "C:/Qt/file/palyer/source/img/picture.svg", "C:/Qt/file/palyer/source/img/symble.svg", "C:/Qt/file/palyer/compoment/ImagePreviewWindow.qml", "C:/Qt/file/palyer/compoment/imagePage.qml", "C:/Qt/file/palyer/compoment/AppState.qml", "C:/Qt/file/palyer/compoment/SettingsPage.qml", "C:/Qt/file/palyer/compoment/RemoteImagePage.qml", "C:/Qt/file/palyer/compoment/template/BaseWindowTemplate.qml", "C:/Qt/file/palyer/compoment/toolbar/ConversionWindow.qml"], "LOCK_FILE": "C:/Qt/file/palyer/build/CMakeFiles/palyer_autogen.dir/AutoRcc_res_EWIEGA46WW_Lock.lock", "MULTI_CONFIG": true, "OPTIONS": ["--no-zstd", "-name", "res"], "OUTPUT_CHECKSUM": "EWIEGA46WW", "OUTPUT_NAME": "qrc_res.cpp", "RCC_EXECUTABLE": "", "RCC_EXECUTABLE_Debug": "C:/Qt/6.9.1/mingw_64/bin/rcc.exe", "RCC_EXECUTABLE_MinSizeRel": "C:/Qt/6.9.1/mingw_64/bin/rcc.exe", "RCC_EXECUTABLE_RelWithDebInfo": "C:/Qt/6.9.1/mingw_64/bin/rcc.exe", "RCC_EXECUTABLE_Release": "C:/Qt/6.9.1/mingw_64/bin/rcc.exe", "RCC_LIST_OPTIONS": [], "RCC_LIST_OPTIONS_Debug": ["--list"], "RCC_LIST_OPTIONS_MinSizeRel": ["--list"], "RCC_LIST_OPTIONS_RelWithDebInfo": ["--list"], "RCC_LIST_OPTIONS_Release": ["--list"], "SETTINGS_FILE": "C:/Qt/file/palyer/build/CMakeFiles/palyer_autogen.dir/AutoRcc_res_EWIEGA46WW_Used.txt", "SETTINGS_FILE_Debug": "C:/Qt/file/palyer/build/CMakeFiles/palyer_autogen.dir/AutoRcc_res_EWIEGA46WW_Used_Debug.txt", "SETTINGS_FILE_MinSizeRel": "C:/Qt/file/palyer/build/CMakeFiles/palyer_autogen.dir/AutoRcc_res_EWIEGA46WW_Used_MinSizeRel.txt", "SETTINGS_FILE_RelWithDebInfo": "C:/Qt/file/palyer/build/CMakeFiles/palyer_autogen.dir/AutoRcc_res_EWIEGA46WW_Used_RelWithDebInfo.txt", "SETTINGS_FILE_Release": "C:/Qt/file/palyer/build/CMakeFiles/palyer_autogen.dir/AutoRcc_res_EWIEGA46WW_Used_Release.txt", "SOURCE": "C:/Qt/file/palyer/res.qrc", "USE_BETTER_GRAPH": true, "VERBOSITY": 0}