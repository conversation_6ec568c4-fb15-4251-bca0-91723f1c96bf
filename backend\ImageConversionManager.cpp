﻿#include "ImageConversionManager.h"
#include "ConversionTaskRunnable.h"
#include <QImage>
#include <QDir>
#include <QDirIterator>
#include <QFileInfo>
#include <QDebug>
#include <QProcess>
#include <QCoreApplication>
#include <QRunnable>
#include <QFuture>
#include <QFutureWatcher>
#include <QtConcurrent>
#include <QFile>
#include <QTimer>
#include <QSet>
#include <QImageReader>

// ===================================================================
// ImageConversionManager: Manages the queue and thread pool.
// ===================================================================

ImageConversionManager::ImageConversionManager(QObject *parent)
    : QObject(parent), m_isPaused(false), m_activeTasks(0),
      m_isProcessingFailedFiles(false), m_isScanning(false), m_isScanCancelled(false)
{ m_isCancelled = 0;
    // 独立线程池配置
    m_conversionPool.setMaxThreadCount(m_defaultThreadCount); // 转换专用，动态调整
    m_thumbnailPool.setMaxThreadCount(4);  // 缩略图专用4线程
    m_threadPool.setMaxThreadCount(2);     // 保持兼容性
    
    // 连接信号以跟踪文件大小增加的情况
    connect(this, &ImageConversionManager::fileSizeIncreasedWithSize,
            this, &ImageConversionManager::onFileSizeIncreasedWithSize);
    
    // 初始化扫描监视器
    m_scanWatcher = new QFutureWatcher<FileScanResult>(this);
    connect(m_scanWatcher, &QFutureWatcher<FileScanResult>::finished,
            this, &ImageConversionManager::onScanningFinished);
}

ImageConversionManager::~ImageConversionManager()
{
    cancelConversion();
    cancelScanning();
    m_threadPool.waitForDone();
    m_sizeIncreasedFiles.clear(); // 清空文件大小增加的源文件列表
    delete m_scanWatcher;
}

bool ImageConversionManager::isRunning() const { return m_activeTasks > 0 || !m_taskQueue.isEmpty(); }
bool ImageConversionManager::isPaused() const { return m_isPaused; }
int ImageConversionManager::maxConcurrentTasks() const { return m_conversionPool.maxThreadCount(); }
void ImageConversionManager::setMaxConcurrentTasks(int count) {
    if (count > 0) { m_conversionPool.setMaxThreadCount(count); emit maxConcurrentTasksChanged(); }
}

bool ImageConversionManager::isScanning() const 
{
    return m_isScanning;
}

void ImageConversionManager::cancelScanning()
{
    if (!m_isScanning) return;
    
    m_isScanCancelled = true;
    if (m_scanWatcher->isRunning()) {
        m_scanWatcher->cancel();
        m_scanWatcher->waitForFinished();
    }
    
    m_isScanning = false;
    emit isScanningChanged();
    emit scanningCancelled();
}

void ImageConversionManager::scanConvertibleFiles(const QString &folderPath, bool includeSubfolders, const QString &targetFormat)
{
    // 如果已经在扫描，取消之前的扫描
    if (m_isScanning) cancelScanning();

    // 存储目标格式
    m_currentTargetFormat = targetFormat;

    m_isScanning = true;
    m_isScanCancelled = false;
    emit isScanningChanged();
    emit scanningStarted(folderPath);

    //使用QtConcurrent在全局线程池中运行扫描操作
    QFuture<FileScanResult> future = QtConcurrent::run(&ImageConversionManager::scanFilesWorker, folderPath, includeSubfolders, this, targetFormat);
    m_scanWatcher->setFuture(future);
}

void ImageConversionManager::onScanningFinished()
{
    if (m_isScanCancelled) {
        m_isScanning = false;
        emit isScanningChanged();
        return;
    }
    
    FileScanResult result = m_scanWatcher->result();
    
    if (!result.success) {
        emit scanningError(result.errorMessage);
    } else {
        emit scanningFinished(result.fileList);
    }
    
    m_isScanning = false;
    emit isScanningChanged();
}

// 在工作线程中执行文件扫描的静态方法
// 新增第三个参数：指向ImageConversionManager的指针
// 新增第四个参数：目标格式，用于确定扫描文件类型
FileScanResult ImageConversionManager::scanFilesWorker(const QString &folderPath, bool includeSubfolders, ImageConversionManager* manager, const QString &targetFormat)
{
    FileScanResult result;
    result.folderPath = folderPath;
    result.includeSubfolders = includeSubfolders;
    result.success = true;

    // 使用通用函数规范化路径
    QString normalizedPath = QDir::toNativeSeparators(folderPath);
    if (normalizedPath.startsWith("file:///")) {
        #ifdef Q_OS_WIN
        normalizedPath = normalizedPath.mid(8); // 移除 "file:///"
        #else
        normalizedPath = normalizedPath.mid(7); // 移除 "file://"，保留一个斜杠
        #endif
    } else if (normalizedPath.startsWith("file://")) {
        normalizedPath = normalizedPath.mid(7); // 移除 "file://"
    }

    #ifdef Q_OS_WIN
    if (normalizedPath.startsWith("/") && normalizedPath.length() >= 2 &&
        normalizedPath.at(1).isLetter() && normalizedPath.at(2) == ':') {
        normalizedPath = normalizedPath.mid(1); // 移除前导斜杠
    }
    #endif

    // 验证路径有效性
    QDir dir(normalizedPath);
    if (!dir.exists()) {
        result.success = false;
        result.errorMessage = "目录不存在: " + normalizedPath;
        return result;
    }

    // 强制刷新文件系统缓存 (Windows特定)
    #ifdef Q_OS_WIN
    // 在Windows上，我们可以通过重新创建QDir对象来刷新缓存
    dir = QDir(normalizedPath);
    dir.refresh();
    #endif

    // 根据目标格式确定要扫描的文件类型
    QVariantList fileList;
    QStringList supportedFormats = {
        "*.jpg", "*.jpeg", "*.png", "*.bmp", "*.tif", "*.tiff",
        "*.webp", "*.avif", "*.heic", "*.heif"
    };
    
    // 创建新的迭代器对象，确保获取最新的文件列表
    QDirIterator it(normalizedPath, supportedFormats, 
                    QDir::Files | QDir::NoDotAndDotDot, 
                    includeSubfolders ? QDirIterator::Subdirectories : QDirIterator::NoIteratorFlags);
    
    int count = 0;
    while(it.hasNext()) {
        QString filePath = it.next();
        QFileInfo fileInfo(filePath);
        
        // 确保文件确实存在且可读
        if (!fileInfo.exists() || !fileInfo.isReadable()) continue;

        QString suffix = fileInfo.suffix().toLower();

        // WebP转换：跳过webp、gif、avif格式
        if (targetFormat == "webp") {
            if (suffix == "webp" || suffix == "gif" || suffix == "avif") {
                continue;
            }
        }

        QVariantMap fileMap;
        fileMap["name"] = fileInfo.fileName();
        fileMap["path"] = fileInfo.filePath();
        fileMap["size"] = fileInfo.size();
        fileMap["suffix"] = suffix;
        fileList.append(fileMap);
        count++;
        
        // 每找到10个文件发送一次进度更新
        if (count % 10 == 0 && manager) {
            // 使用QMetaObject::invokeMethod从工作线程安全调用主线程的方法
            QMetaObject::invokeMethod(manager, [manager, count]() {
                // 在主线程中安全地发送信号
                emit manager->scanningProgress(count);
            }, Qt::QueuedConnection);
        }
        
        // 检查是否被取消
        if (QThread::currentThread()->isInterruptionRequested()) {
            result.success = false;
            result.errorMessage = "扫描被取消";
            return result;
        }
    }
    
    // 如果第一种方式没有找到任何文件，尝试第二种方式
    if (count == 0) {
        // 2. 第二种方式：扫描所有文件，然后手动过滤
        QDirIterator allFilesIt(normalizedPath, 
                          QStringList() << "*.*", 
                          QDir::Files | QDir::NoDotAndDotDot, 
                          includeSubfolders ? QDirIterator::Subdirectories : QDirIterator::NoIteratorFlags);
        
        QStringList supportedExtensions;
        for (const QString &format : std::as_const(supportedFormats)) {
            supportedExtensions << format.mid(2); // 移除"*."前缀
        }
        
        while(allFilesIt.hasNext()) {
            QString filePath = allFilesIt.next();
            QFileInfo fileInfo(filePath);
            
            // 确保文件确实存在且可读
            if (!fileInfo.exists() || !fileInfo.isReadable()) continue;
            
            QString suffix = fileInfo.suffix().toLower();
            
            // 检查是否为支持的图像格式
            if (supportedExtensions.contains(suffix)) {
                // WebP转换：跳过webp、gif、avif格式
                if (targetFormat == "webp") {
                    if (suffix == "webp" || suffix == "gif" || suffix == "avif") {
                        continue;
                    }
                }

                QVariantMap fileMap;
                fileMap["name"] = fileInfo.fileName();
                fileMap["path"] = fileInfo.filePath();
                fileMap["size"] = fileInfo.size();
                fileMap["suffix"] = suffix;
                fileList.append(fileMap);
                count++;
                
                // 每找到10个文件发送一次进度更新
                if (count % 10 == 0 && manager) {
                    // 使用QMetaObject::invokeMethod从工作线程安全调用主线程的方法
                    QMetaObject::invokeMethod(manager, [manager, count]() {
                        // 在主线程中安全地发送信号
                        emit manager->scanningProgress(count);
                    }, Qt::QueuedConnection);
                }
                
                // 检查是否被取消
                if (QThread::currentThread()->isInterruptionRequested()) {
                    result.success = false;
                    result.errorMessage = "扫描被取消";
                    return result;
                }
            }
        }
    }
    
    result.fileList = fileList;
    return result;
}

void ImageConversionManager::deleteFilesAsync(const QString &folderPath, const QVariantList &filePatterns, bool includeSubfolders)
{
    emit deleteFilesStarted();
    
    // 使用QtConcurrent在子线程中执行删除操作
    QFuture<QVariantMap> future = QtConcurrent::run([=]() {
        return deleteFiles(folderPath, filePatterns, includeSubfolders);
    });
    
    // 监听结果并发送信号
    auto *watcher = new QFutureWatcher<QVariantMap>(this);
    connect(watcher, &QFutureWatcher<QVariantMap>::finished, this, [=]() {
        QVariantMap result = watcher->result();
        emit deleteFilesCompleted(result);
        watcher->deleteLater();
    });
    watcher->setFuture(future);
}

// 通用路径处理函数的实现
QString ImageConversionManager::normalizePath(const QString &path) const
{
    if (path.isEmpty()) {
        return QString();
    }
    // 修复可能的URL路径格式问题
    QString normalizedPath = path;
    
    // 处理file:///格式的URL
    if (normalizedPath.startsWith("file:///")) {
        // 在Windows上，我们需要去掉file:///，但保留驱动器号
        #ifdef Q_OS_WIN
        normalizedPath = normalizedPath.mid(8); // 移除 "file:///"
        #else
        // 在Unix系统上，我们需要保留前导斜杠
        normalizedPath = normalizedPath.mid(7); // 移除 "file://"，保留一个斜杠
        #endif
    } else if (normalizedPath.startsWith("file://")) {
        normalizedPath = normalizedPath.mid(7); // 移除 "file://"
    }
    
    // Windows路径特殊处理（如果以/C:/开头）
    #ifdef Q_OS_WIN
    if (normalizedPath.startsWith("/") && normalizedPath.length() >= 2 && 
        normalizedPath.at(1).isLetter() && normalizedPath.at(2) == ':') {
        normalizedPath = normalizedPath.mid(1); // 移除前导斜杠，使路径变为如 "C:/"
    }
    #endif
    
    // 确保路径以斜杠结尾，但只有当它是一个目录时
    QFileInfo fileInfo(normalizedPath);
    if (fileInfo.isDir() && !normalizedPath.endsWith('/') && !normalizedPath.endsWith('\\')) {
        normalizedPath += QDir::separator();
    }
    
    // 转换路径分隔符为系统分隔符
    normalizedPath = QDir::toNativeSeparators(normalizedPath);
    return normalizedPath;
}

QVariantList ImageConversionManager::getConvertibleFiles(const QString &folderPath, bool includeSubfolders)
{
    // 为了保持兼容性，调用工作函数但在主线程中同步执行
    FileScanResult result = scanFilesWorker(folderPath, includeSubfolders, nullptr, "");

    if (!result.success) {
        return QVariantList(); // 返回空列表
    }

    return result.fileList;
}

void ImageConversionManager::startConversion(const QVariant &fileListModel, const QString &targetFormat, int quality, const QString &outputDir)
{
    // 首先，取消所有正在进行的任务
    cancelConversion();

    // 等待所有任务真正结束
    m_conversionPool.waitForDone();

    QMutexLocker locker(&m_mutex);
    if (isRunning()) return;

    m_taskQueue.clear();
    m_isCancelled = 0; // 重置取消标志

    QVariantList modelList = fileListModel.toList();
    
    bool useSourceDir = outputDir.isEmpty();
    QString normalizedOutputDir;
    QDir outDir;
    
    if (!useSourceDir) {
        normalizedOutputDir = normalizePath(outputDir);
        outDir = QDir(normalizedOutputDir);
        if (!outDir.exists()) outDir.mkpath(".");
    } else {
    }

    // 记录已添加的目标文件路径，用于检测重复
    QSet<QString> destPathSet;

    for (int i = 0; i < modelList.size(); ++i) {
        QVariantMap item = modelList[i].toMap();
        
        ConversionTask task;
        task.index = i;
        task.sourcePath = item["filePath"].toString();
        
        if (task.sourcePath.isEmpty()) {
            // 尝试其他可能的键名
            task.sourcePath = item["path"].toString();
        }
        
        if (task.sourcePath.isEmpty()) continue;
        
        // 规范化源文件路径
        task.sourcePath = normalizePath(task.sourcePath);
        
        // 检查源文件是否存在
        if (!QFileInfo::exists(task.sourcePath)) continue;

        // 支持智能压缩：每个文件可以有不同的目标格式
        QString realTargetFormat = item.contains("targetFormat") ? item["targetFormat"].toString() : targetFormat;
        if (realTargetFormat.isEmpty()) realTargetFormat = targetFormat;

        QFileInfo sourceInfo(task.sourcePath);
        QString newFileName;

        // AV1是编码格式，需要兼容的容器格式
        if (realTargetFormat == "av1") {
            QString originalSuffix = sourceInfo.suffix().toLower();
            QString targetSuffix;

            // MOV不支持AV1，转换为MP4；其他格式保持原容器
            if (originalSuffix == "mov") {
                targetSuffix = "mp4";
            } else {
                targetSuffix = originalSuffix;
            }

            newFileName = sourceInfo.completeBaseName() + "_av1." + targetSuffix;
        } else {
            // 图片格式：avif/webp是容器格式
            newFileName = sourceInfo.completeBaseName() + "." + realTargetFormat.toLower();
        }
        
        if (useSourceDir) {
            task.destPath = sourceInfo.dir().filePath(newFileName);
        } else {
            task.destPath = outDir.filePath(newFileName);
        }

        // 检查是否已经添加过相同的目标路径
        if (destPathSet.contains(task.destPath)) continue;
        
        // 将目标路径添加到集合中
        destPathSet.insert(task.destPath);

        task.targetFormat = realTargetFormat;
        task.quality = quality;
        m_taskQueue.enqueue(task);
        
        // 每添加100个任务后，处理一下事件循环，防止界面完全卡死
        if (i > 0 && i % 100 == 0) {
            QCoreApplication::processEvents();
        }
    }

    if (!m_taskQueue.isEmpty()) {
        emit isRunningChanged();
        processQueue();
    } else {
        qDebug() << "转换队列为空，没有任务需要处理";
    }
}

void ImageConversionManager::processQueue()
{
    // 注意：这个方法可能会从多个地方调用，包括线程信号
    // 使用QMetaObject::invokeMethod确保在主线程上执行任务分配
    QMetaObject::invokeMethod(this, [this]() {
        QMutexLocker locker(&m_mutex);
        if (m_isPaused || m_isCancelled) return;

        // 检查并启动尽可能多的任务，直到达到最大并发任务数
        while (m_activeTasks < m_conversionPool.maxThreadCount() && !m_taskQueue.isEmpty()) {
            ConversionTask task = m_taskQueue.dequeue();
            m_activeTasks++;

            // 检测视频任务并调整线程池
            if (task.targetFormat.toLower() == "av1") {
                m_activeVideoTasks.fetchAndAddRelaxed(1);
                updateThreadPoolSize();
            }

            // 创建新的任务并连接信号
            auto *runnable = new ConversionTaskRunnable(task, this, &m_isCancelled);
            
            // 使用Qt::QueuedConnection确保信号处理在正确的线程上
            connect(runnable, &ConversionTaskRunnable::taskFinished, this, [this, runnable, task]() {
                // 使用QueuedConnection确保在主线程上处理任务完成
                QMetaObject::invokeMethod(this, [this, runnable, task]() {
                    QMutexLocker locker(&m_mutex);
                    m_activeTasks--;

                    // 如果是视频任务完成，减少计数并调整线程池
                    if (task.targetFormat.toLower() == "av1") {
                        m_activeVideoTasks.fetchAndSubRelaxed(1);
                        updateThreadPoolSize();
                    }
                    
                    // 立即删除runnable，避免内存累积
                    runnable->deleteLater();
                    
                    if (m_isCancelled) {
                        if (m_activeTasks == 0) {
                            m_taskQueue.clear();
                            emit isRunningChanged();
                        }
                        return;
                    }
                    
                    if (m_activeTasks == 0 && m_taskQueue.isEmpty()) {
                        // 所有队列都为空，转换完成
                        locker.unlock();
                        emit isRunningChanged();
                        emit conversionFinished();
                    } else {
                        // 继续处理队列中的下一个任务
                        locker.unlock();
                        processQueue();
                    }
                }, Qt::QueuedConnection);
            }, Qt::QueuedConnection);
            
            // 使用QueuedConnection确保信号在主线程上处理
            connect(runnable, &ConversionTaskRunnable::taskCompleted, this, 
                    &ImageConversionManager::fileConverted, Qt::QueuedConnection);
            connect(runnable, &ConversionTaskRunnable::taskCompletedWithSize, this, 
                    &ImageConversionManager::fileConvertedWithSize, Qt::QueuedConnection);
            connect(runnable, &ConversionTaskRunnable::taskFailed, this, 
                    &ImageConversionManager::fileFailed, Qt::QueuedConnection);
            connect(runnable, &ConversionTaskRunnable::fileSizeIncreasedAfterConversion, this,
                    &ImageConversionManager::fileSizeIncreasedAfterConversion, Qt::QueuedConnection);
            connect(runnable, &ConversionTaskRunnable::fileSizeIncreasedWithSize, this,
                    &ImageConversionManager::fileSizeIncreasedWithSize, Qt::QueuedConnection);
            connect(runnable, &ConversionTaskRunnable::fileCorrupted, this,
                    &ImageConversionManager::fileCorrupted, Qt::QueuedConnection);
            
            // 在转换专用线程池中启动任务
            m_conversionPool.start(runnable);
        }
    }, Qt::QueuedConnection);
}

void ImageConversionManager::handleTaskFinished()
{
    // 此方法保留以兼容现有代码，但内部逻辑已移至processQueue中的lambda
    // 继续处理队列中的下一个任务
    processQueue();
}

void ImageConversionManager::pauseConversion()
{
    QMutexLocker locker(&m_mutex);
    if (!isRunning() || m_isPaused) return;
    m_isPaused = true;
    emit isPausedChanged();
}

void ImageConversionManager::resumeConversion()
{
    QMutexLocker locker(&m_mutex);
    if (!m_isPaused) return;
    m_isPaused = false;
    m_pauseCondition.wakeAll();
    emit isPausedChanged();

    locker.unlock();
    processQueue();
}

void ImageConversionManager::cancelConversion()
{
    QMutexLocker locker(&m_mutex);
    if (!isRunning()) return;

    m_isCancelled = 1;
    m_taskQueue.clear();

    if(m_isPaused) {
        m_isPaused = false;
        m_pauseCondition.wakeAll();
        emit isPausedChanged();
    }

    // 清空失败文件队列
    QMutexLocker failedLocker(&m_failedFilesMutex);
    m_failedFilesQueue.clear();
    m_isProcessingFailedFiles = false;
    failedLocker.unlock();

    // Note: This doesn't stop already running tasks, just clears the queue.
    // A more complex implementation would involve trying to kill the QProcess.
    if (m_activeTasks == 0) {
        emit isRunningChanged();
    }
}

void ImageConversionManager::onFileSizeIncreasedWithSize(int index, const QString &path, qint64 originalSize, qint64 newSize)
{
    QMutexLocker locker(&m_mutex);
    // 记录文件大小增加的源文件路径，用于后续删除操作
    m_sizeIncreasedFiles.insert(normalizePath(path));
}

QVariantMap ImageConversionManager::deleteFiles(const QString &folderPath, const QVariantList &filePatterns, bool includeSubfolders)
{
    QVariantMap result;
    result["deletedCount"] = 0;
    result["errorCount"] = 0;
    result["success"] = false;
    result["error"] = "";
    result["txtUrlDeletedCount"] = 0; // 专门统计txt/url文件删除数量
    
    // 使用通用函数规范化路径
    QString normalizedPath = normalizePath(folderPath);
    
    // 简化日志输出
    
    // 验证路径有效性
    QDir dir(normalizedPath);
    if (!dir.exists()) {
        result["error"] = "文件夹不存在: " + normalizedPath;
        qDebug() << "删除文件 - 错误:" << result["error"].toString();
        return result;
    }
    
    // 将QVariantList转换为QStringList，同时处理每个路径
    QStringList processedPatterns;
    QStringList skippedFiles; // 存储被跳过的文件路径
    
    // 检查是否是删除源文件的操作（通过检查是否有完整文件路径而非通配符）
    bool isDeletingSourceFiles = false;
    
    for (const QVariant &pattern : std::as_const(filePatterns)) {
        QString patternStr = pattern.toString();
        
        // 如果是完整路径（而不是通配符模式），也需要规范化
        if (!patternStr.contains('*')) {
            isDeletingSourceFiles = true; // 标记为删除源文件操作
            QString normalizedFilePath = normalizePath(patternStr);
            
            // 检查该文件是否因文件大小增加而需要保留
            if (m_sizeIncreasedFiles.contains(normalizedFilePath)) {
                skippedFiles << normalizedFilePath;
                continue; // 跳过这个文件
            }
            
            processedPatterns << normalizedFilePath;
        } else {
            // 如果是通配符模式，直接添加
            processedPatterns << patternStr;
        }
    }
    
    // 创建QDirIterator以遍历文件
    QDirIterator::IteratorFlags iterFlags = includeSubfolders ? QDirIterator::Subdirectories : QDirIterator::NoIteratorFlags;
    
    int deletedCount = 0;
    int errorCount = 0;
    QStringList failedFiles;
    QStringList filesToRetry; // 存储第一次删除失败的文件，稍后重试
    
    // 删除匹配的文件
    for (const QString &patternStr : std::as_const(processedPatterns)) {
        // 如果是通配符模式，使用迭代器删除匹配的文件
        if (patternStr.contains('*')) {
            QDirIterator it(normalizedPath, QStringList() << patternStr, QDir::Files | QDir::NoDotAndDotDot, iterFlags);
            
            while (it.hasNext()) {
                QString filePath = it.next();
                QString normalizedFilePath = normalizePath(filePath);
                
                // 检查该文件是否因文件大小增加而需要保留
                if (m_sizeIncreasedFiles.contains(normalizedFilePath)) {
                    skippedFiles << normalizedFilePath;
                    continue; // 跳过这个文件
                }
                // 新增：只删除实际转换生成的目标文件，跳过未转换或因分辨率超限未生成的文件
                QFileInfo info(filePath);
                if (!info.exists() || info.size() == 0) {
                    // 跳过空文件或不存在的文件
                    continue;
                }
                // 尝试修改文件属性，移除只读标志
                QFile file(filePath);
                file.setPermissions(QFile::ReadOwner | QFile::WriteOwner | QFile::ReadUser | QFile::WriteUser);
                if (file.remove()) {
                    deletedCount++;
                    qDebug() << "删除文件 - 成功删除:" << filePath;
                } else {
                    // 添加到重试列表
                    filesToRetry << filePath;
                }
            }
        }
        // 如果是完整路径，直接删除
        QFileInfo info(patternStr);
        if (!info.exists() || info.size() == 0) {
            // 跳过空文件或不存在的文件
            continue;
        }
        QFile file(patternStr);
        if (file.exists()) {
            // 尝试修改文件属性，移除只读标志
            file.setPermissions(QFile::ReadOwner | QFile::WriteOwner | QFile::ReadUser | QFile::WriteUser);
            if (file.remove()) {
                deletedCount++;
                qDebug() << "删除文件 - 成功删除:" << patternStr;
            } else {
                // 添加到重试列表
                filesToRetry << patternStr;
            }
        } else {
            errorCount++;
            failedFiles.append(patternStr);
        }
    }
    
    // 处理需要重试的文件
    if (!filesToRetry.isEmpty()) {
        // 先等待一段时间，让系统有机会释放文件句柄
        QThread::msleep(500);
        
        // 尝试再次删除
        for (const QString &filePath : std::as_const(filesToRetry)) {
            QFile file(filePath);
            
            // 再次尝试修改文件权限
            file.setPermissions(QFile::ReadOwner | QFile::WriteOwner | QFile::ReadUser | QFile::WriteUser);
            
            if (file.remove()) {
                deletedCount++;
                qDebug() << "删除文件 - 成功删除:" << filePath;
            } else {
                errorCount++;
                failedFiles.append(filePath);
            }
        }
    }
    
    // 专门用于清理txt/url文件的逻辑
    int txtUrlDeletedCount = 0;
    
    // 仅在删除源文件的操作中，才执行清理txt/url文件
    if (isDeletingSourceFiles) {
        // 使用单独的函数来清理txt/url文件，不依赖于源文件路径
        txtUrlDeletedCount = cleanupTxtUrlFiles(normalizedPath, includeSubfolders);
    }
    
    // 构建结果
    result["deletedCount"] = deletedCount;
    result["txtUrlDeletedCount"] = txtUrlDeletedCount;
    result["errorCount"] = errorCount;
    result["success"] = true;
    if (errorCount > 0) {
        result["failedFiles"] = QVariant::fromValue(failedFiles);
    }
    if (!skippedFiles.isEmpty()) {
        result["skippedFiles"] = QVariant::fromValue(skippedFiles);
        result["skippedCount"] = skippedFiles.size();
    }
    
    qDebug() << "删除文件 - 结果: 成功删除" << deletedCount << "个文件, 失败" << errorCount << "个文件";
    
    return result;
}

// 新增专门用于清理txt/url文件的方法
int ImageConversionManager::cleanupTxtUrlFiles(const QString &folderPath, bool includeSubfolders)
{
    int deletedCount = 0;
    
    // 先处理当前目录
    QDir dir(folderPath);
    QStringList txtUrlFilter;
    txtUrlFilter << "*.txt" << "*.url";
    QStringList txtUrlFiles = dir.entryList(txtUrlFilter, QDir::Files);
    
    // 删除当前目录的txt/url文件
    for (const QString &fileName : std::as_const(txtUrlFiles)) {
        QFile file(dir.filePath(fileName));
        if (file.exists() && file.remove()) {
            deletedCount++;
        }
    }
    
    // 如果需要包含子目录，递归处理
    if (includeSubfolders) {
        QStringList subDirs = dir.entryList(QDir::Dirs | QDir::NoDotAndDotDot);
        for (const QString &subDir : std::as_const(subDirs)) {
            QString subDirPath = dir.filePath(subDir);
            deletedCount += cleanupTxtUrlFiles(subDirPath, true);
        }
    }
    
    return deletedCount;
}

bool ImageConversionManager::clearTaskList()
{
    QMutexLocker locker(&m_mutex);
    
    // 清空待处理的任务队列
    m_taskQueue.clear();
    
    // 不再清空文件大小增加的源文件列表，保留这些记录
    // m_sizeIncreasedFiles.clear();
    
    // 不会中断已经在运行的任务，但会通知UI可以清空列表
    // 注意：已经在运行的ffmpeg进程会继续运行，直到完成或失败
    
    // 如果没有正在运行的任务，则更新状态
    if (m_activeTasks == 0) emit isRunningChanged();
    
    // 发送任务列表已清空的信号
    emit taskListCleared();
    
    return true;
}

QVariantMap ImageConversionManager::getFileInfo(const QString &filePath) const
{
    QVariantMap result;
    
    // 规范化路径
    QString normalizedPath = normalizePath(filePath);
    
    QFileInfo fileInfo(normalizedPath);
    if (!fileInfo.exists()) {
        return result;
    }
    
    result["name"] = fileInfo.fileName();
    result["path"] = fileInfo.filePath();
    result["size"] = fileInfo.size();
    result["suffix"] = fileInfo.suffix().toLower();
    result["lastModified"] = fileInfo.lastModified();
    result["isDir"] = fileInfo.isDir();
    result["isFile"] = fileInfo.isFile();
    result["isReadable"] = fileInfo.isReadable();
    result["isWritable"] = fileInfo.isWritable();
    
    return result;
}

// 添加处理失败文件队列的新方法
void ImageConversionManager::processFailedFilesQueue()
{
    // 确保在主线程上执行
    QMetaObject::invokeMethod(this, [this]() {
        QMutexLocker failedLocker(&m_failedFilesMutex);

        // 如果已经在处理失败队列或队列为空，直接返回
        if (m_isProcessingFailedFiles || m_failedFilesQueue.isEmpty()) return;
        
        // 检查转换队列是否空闲
        QMutexLocker locker(&m_mutex);
        if (m_activeTasks > 0 || !m_taskQueue.isEmpty()) return;
        
        // 标记为正在处理失败队列
        m_isProcessingFailedFiles = true;
        
        // 将失败队列中的任务移动到转换队列
        qDebug() << "开始处理失败文件队列，数量:" << m_failedFilesQueue.size();
        while (!m_failedFilesQueue.isEmpty()) {
            ConversionTask task = m_failedFilesQueue.dequeue();
            
            // 检查重试次数是否超过最大限制
            if (task.retryCount >= m_maxRetryCount) {
                qDebug() << "文件达到最大重试次数，放弃处理:" << task.destPath;
                emit fileCorrupted(task.index, task.destPath, "达到最大重试次数");
                continue;
            }
            
            // 增加重试计数
            task.retryCount++;
            
            qDebug() << "重新添加失败的文件到转换队列:" << task.destPath << "重试次数:" << task.retryCount;
            m_taskQueue.enqueue(task);
        }
        
        // 重置处理标志
        m_isProcessingFailedFiles = false;
        
        // 如果添加了任务，开始处理队列
        if (!m_taskQueue.isEmpty()) {
            locker.unlock();
            processQueue();
        }
    }, Qt::QueuedConnection);
}

void ImageConversionManager::retryConversion(const ConversionTask &task)
{
    QMutexLocker locker(&m_mutex);
    
    // 检查重试次数是否超过最大限制
    if (task.retryCount >= m_maxRetryCount) {
        emit fileCorrupted(task.index, task.destPath, "达到最大重试次数");
        return;
    }
    
    // 创建新的任务，增加重试计数
    ConversionTask retryTask = task;
    retryTask.retryCount++;
    
    // 对于AVIF格式，如果重试，可以考虑降低质量或更改编码参数
    if (retryTask.targetFormat.toLower() == "avif") {
        // 降低质量以提高兼容性
        retryTask.quality = qMax(70, retryTask.quality - 10);
    }
    
    // 将任务添加到队列前端，优先处理
    m_taskQueue.prepend(retryTask);
    
    // 如果当前没有活动任务，启动队列处理
    if (m_activeTasks == 0) {
        locker.unlock();
        processQueue();
    }
}

void ImageConversionManager::setThreadCount(int n) {
    m_threadPool.setMaxThreadCount(n);
}

void ImageConversionManager::setConversionThreads(int n) {
    m_conversionPool.setMaxThreadCount(n);
}

void ImageConversionManager::setThumbnailThreads(int n) {
    m_thumbnailPool.setMaxThreadCount(n);
}

void ImageConversionManager::updateThreadPoolSize() {
    // 只有正在执行视频任务时才限制为单线程
    int targetThreadCount = (m_activeVideoTasks > 0) ? m_videoThreadCount : m_defaultThreadCount;

    if (m_conversionPool.maxThreadCount() != targetThreadCount) {
        qDebug() << "调整线程池大小:" << m_conversionPool.maxThreadCount() << "→" << targetThreadCount
                 << "活跃视频任务:" << m_activeVideoTasks.loadRelaxed();

        // 异步调整线程池，避免阻塞主线程
        QFuture<void> future = QtConcurrent::run([this, targetThreadCount]() -> void {
            m_conversionPool.setMaxThreadCount(targetThreadCount);
        });
        Q_UNUSED(future)
    }
}

void ImageConversionManager::scanSmartCompress(const QString &folderPath, bool includeSubfolders) {
    // 如果已经在扫描，取消之前的扫描
    if (m_isScanning) cancelScanning();

    // 存储智能压缩的目标格式标识
    m_currentTargetFormat = "smart";

    m_isScanning = true;
    m_isScanCancelled = false;
    emit isScanningChanged();
    emit scanningStarted(folderPath);

    // 使用QtConcurrent在全局线程池中运行扫描操作
    QFuture<FileScanResult> future = QtConcurrent::run([=]() -> FileScanResult {
        FileScanResult result;
        result.folderPath = folderPath;
        result.includeSubfolders = includeSubfolders;
        result.success = true;

        QDir dir(normalizePath(folderPath));
        if (!dir.exists()) {
            result.success = false;
            result.errorMessage = "文件夹不存在";
            return result;
        }

        // 使用和AVIF模式相同的简单快速扫描逻辑
        QStringList supportedFormats = {
            "*.jpg", "*.jpeg", "*.png", "*.bmp", "*.tif", "*.tiff", "*.gif",
            "*.mp4", "*.avi", "*.mkv", "*.mov", "*.wmv", "*.flv", "*.webm", "*.m4v"
        };

        QDirIterator::IteratorFlags flags = includeSubfolders ? QDirIterator::Subdirectories : QDirIterator::NoIteratorFlags;
        QDirIterator it(dir.absolutePath(), supportedFormats, QDir::Files, flags);

        while (it.hasNext() && !m_isScanCancelled) {
            QString filePath = it.next();
            QFileInfo fileInfo(filePath);
            QString suffix = fileInfo.suffix().toLower();

            // 跳过已压缩格式和gif
            if (suffix == "avif" || suffix == "webp" || suffix == "gif") continue;

            // 跳过已经是AV1编码的视频文件
            if (fileInfo.fileName().contains("_av1.")) continue;

            // 简单判断目标格式，不做复杂检查
            QString targetFormat;
            QStringList imgFormats = {"jpg", "jpeg", "png", "bmp", "tif", "tiff", "gif"};
            QStringList vidFormats = {"mp4", "avi", "mkv", "mov", "wmv", "flv", "webm", "m4v"};

            if (imgFormats.contains(suffix)) {
                targetFormat = "avif";
            } else if (vidFormats.contains(suffix)) {
                targetFormat = "av1";
            } else {
                continue;
            }

            QVariantMap fileData;
            fileData["name"] = fileInfo.fileName();
            fileData["path"] = filePath;
            fileData["size"] = fileInfo.size();
            fileData["suffix"] = suffix;
            fileData["targetFormat"] = targetFormat;
            result.fileList.append(fileData);

            // 每处理一个文件就更新进度
            emit scanningProgress(result.fileList.size());
        }

        return result;
    });

    m_scanWatcher->setFuture(future);
}

void ImageConversionManager::scanAV1Videos(const QString &folderPath, bool includeSubfolders)
{
    // 如果已经在扫描，取消之前的扫描
    if (m_isScanning) cancelScanning();

    m_currentTargetFormat = "av1";
    m_isScanning = true;
    m_isScanCancelled = false;
    emit isScanningChanged();
    emit scanningStarted(folderPath);

    // 使用QtConcurrent在全局线程池中运行扫描操作
    QFuture<FileScanResult> future = QtConcurrent::run([=]() -> FileScanResult {
        FileScanResult result;
        result.folderPath = folderPath;
        result.includeSubfolders = includeSubfolders;
        result.success = true;

        QDir dir(normalizePath(folderPath));
        if (!dir.exists()) {
            result.success = false;
            result.errorMessage = "文件夹不存在";
            return result;
        }

        // 只扫描视频文件
        QStringList vidFilters = {"*.mp4", "*.avi", "*.mkv", "*.mov", "*.wmv", "*.flv", "*.webm", "*.m4v"};

        QDirIterator::IteratorFlags flags = includeSubfolders ? QDirIterator::Subdirectories : QDirIterator::NoIteratorFlags;
        QDirIterator it(dir.absolutePath(), vidFilters, QDir::Files, flags);

        while (it.hasNext() && !m_isScanCancelled) {
            QString filePath = it.next();
            QFileInfo fileInfo(filePath);
            QString suffix = fileInfo.suffix().toLower();

            // 跳过已经是AV1编码的视频文件
            if (fileInfo.fileName().contains("_av1.")) continue;

            // 检查是否已存在对应的AV1文件
            QString baseName = fileInfo.completeBaseName();
            QString targetSuffix = (suffix == "mov") ? "mp4" : suffix;
            QString av1FileName = baseName + "_av1." + targetSuffix;
            QString av1FilePath = fileInfo.dir().filePath(av1FileName);

            // 如果AV1文件已存在，跳过
            if (QFileInfo::exists(av1FilePath)) continue;

            QVariantMap fileData;
            fileData["name"] = fileInfo.fileName();
            fileData["path"] = filePath;
            fileData["size"] = fileInfo.size();
            fileData["suffix"] = suffix;
            fileData["targetFormat"] = "av1";
            result.fileList.append(fileData);

            // 每处理一个文件就更新进度
            emit scanningProgress(result.fileList.size());
        }

        return result;
    });
    m_scanWatcher->setFuture(future);
}

// 性能优化的数据处理函数
QString ImageConversionManager::formatBytes(qint64 bytes) {
    if (bytes <= 0) return "0 B";
    const QStringList units = {"B", "KB", "MB", "GB", "TB"};
    int unitIndex = 0;
    double size = bytes;
    while (size >= 1024 && unitIndex < units.size() - 1) {
        size /= 1024;
        unitIndex++;
    }
    return QString::number(size, 'f', unitIndex == 0 ? 0 : 1) + " " + units[unitIndex];
}

qreal ImageConversionManager::extractSizeValue(const QString& sizeString) {
    if (sizeString.isEmpty()) return 0.0;
    QString cleanString = sizeString;
    cleanString.remove(QRegularExpression("[^0-9.]"));
    return cleanString.toDouble();
}
