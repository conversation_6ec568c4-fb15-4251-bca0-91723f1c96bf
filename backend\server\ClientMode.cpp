#include "ClientMode.h"
#include <QNetworkRequest>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonParseError>
#include <QUrl>
#include <QUrlQuery>
#include <QDebug>
#include <QDir>
#include <QStandardPaths>
#include <QFileInfo>
#include <QCryptographicHash>
#include <QtConcurrent>
#include <QDirIterator>
#include <QDateTime>

ClientMode::ClientMode(QObject *parent)
    : QObject(parent)
    , m_networkManager(new QNetworkAccessManager(this))
    , m_connected(false)
    , m_autoReconnect(false)
    , m_currentTestReply(nullptr)
    , m_currentImageListReply(nullptr)
{
    m_connectionStatus = "未连接";
    initializeCacheDirectories();
}

ClientMode::~ClientMode()
{
    disconnectFromServer();
}

void ClientMode::connectToServer(const QString &host, int port)
{
    if (host.isEmpty()) {
        setConnectionStatus("错误：服务器地址为空");
        emit connectionError("服务器地址不能为空");
        return;
    }

    m_serverUrl = formatServerUrl(host, port);
    setConnectionStatus("正在连接...");

    qDebug() << "尝试连接到服务器:" << m_serverUrl;
    testConnection();
}

void ClientMode::disconnectFromServer()
{
    if (m_currentTestReply) {
        m_currentTestReply->abort();
        m_currentTestReply = nullptr;
    }

    if (m_currentImageListReply) {
        m_currentImageListReply->abort();
        m_currentImageListReply = nullptr;
    }

    setConnected(false);
    setConnectionStatus("已断开连接");
    m_imageList = QJsonArray();
    emit imageListChanged();
}

void ClientMode::testConnection()
{
    if (m_currentTestReply) {
        m_currentTestReply->abort();
    }

    // 添加时间戳防止缓存
    QString timestamp = QString::number(QDateTime::currentMSecsSinceEpoch());
    QUrl testUrl(m_serverUrl + "/api/images?path=/&t=" + timestamp);
    QNetworkRequest request(testUrl);
    request.setRawHeader("User-Agent", "ImageViewer-Client/1.0");
    request.setRawHeader("Cache-Control", "no-cache");
    request.setAttribute(QNetworkRequest::RedirectPolicyAttribute, QNetworkRequest::NoLessSafeRedirectPolicy);

    // 添加令牌验证头
    QString token = m_accessToken.isEmpty() ? "imageviewer2025" : m_accessToken;
    request.setRawHeader("Authorization", QString("Bearer %1").arg(token).toUtf8());

    m_currentTestReply = m_networkManager->get(request);
    connect(m_currentTestReply, &QNetworkReply::finished, this, &ClientMode::onTestConnectionFinished);
    connect(m_currentTestReply, QOverload<QNetworkReply::NetworkError>::of(&QNetworkReply::errorOccurred),
            this, &ClientMode::onNetworkError);

    qDebug() << "发送测试连接请求到:" << testUrl.toString();
}

void ClientMode::loadImageList(const QString &path)
{
    if (!m_connected) {
        qWarning() << "未连接到服务器，无法加载图片列表";
        return;
    }

    if (m_currentImageListReply) {
        m_currentImageListReply->abort();
    }

    QString apiPath = path.isEmpty() ? "/" : path;
    // 添加时间戳防止缓存
    QString timestamp = QString::number(QDateTime::currentMSecsSinceEpoch());
    QUrl listUrl(m_serverUrl + "/api/images?path=" + QUrl::toPercentEncoding(apiPath) + "&t=" + timestamp);
    QNetworkRequest request(listUrl);
    request.setRawHeader("User-Agent", "ImageViewer-Client/1.0");
    request.setRawHeader("Cache-Control", "no-cache");

    // 添加令牌验证头
    QString token = m_accessToken.isEmpty() ? "imageviewer2025" : m_accessToken;
    request.setRawHeader("Authorization", QString("Bearer %1").arg(token).toUtf8());

    m_currentImageListReply = m_networkManager->get(request);
    connect(m_currentImageListReply, &QNetworkReply::finished, this, &ClientMode::onImageListFinished);
    connect(m_currentImageListReply, QOverload<QNetworkReply::NetworkError>::of(&QNetworkReply::errorOccurred),
            this, &ClientMode::onNetworkError);

    qDebug() << "加载图片列表:" << listUrl.toString();
}

QString ClientMode::getImageUrl(const QString &imagePath)
{
    if (imagePath.isEmpty() || !m_connected) return QString();

    QString cleanPath = imagePath;
    if (cleanPath.startsWith("/")) cleanPath = cleanPath.mid(1); // 移除开头的斜杠
    return m_serverUrl + "/api/image/" + cleanPath;
}

QString ClientMode::getThumbnailUrl(const QString &imagePath)
{
    if (imagePath.isEmpty() || !m_connected) return QString();

    QString cleanPath = imagePath;
    if (cleanPath.startsWith("/")) cleanPath = cleanPath.mid(1); // 移除开头的斜杠
    return m_serverUrl + "/api/thumbnail/" + cleanPath;
}

void ClientMode::onTestConnectionFinished()
{
    if (!m_currentTestReply) return;

    QNetworkReply::NetworkError error = m_currentTestReply->error();
    if (error == QNetworkReply::NoError) {
        QByteArray data = m_currentTestReply->readAll();
        QJsonParseError parseError;
        QJsonDocument doc = QJsonDocument::fromJson(data, &parseError);

        if (parseError.error == QJsonParseError::NoError && doc.isObject()) {
            setConnected(true);
            setConnectionStatus("已连接");
            qDebug() << "成功连接到服务器";

            // 自动加载根目录图片列表
            loadImageList("");
        } else {
            setConnected(false);
            setConnectionStatus("服务器响应格式错误");
            emit connectionError("服务器响应格式不正确");
        }
    } else {
        setConnected(false);
        QString errorMsg = QString("连接失败: %1").arg(m_currentTestReply->errorString());
        setConnectionStatus(errorMsg);
        emit connectionError(errorMsg);
        qWarning() << "连接测试失败:" << errorMsg;
    }

    m_currentTestReply->deleteLater();
    m_currentTestReply = nullptr;
}

void ClientMode::onImageListFinished()
{
    if (!m_currentImageListReply) {
        return;
    }

    QNetworkReply::NetworkError error = m_currentImageListReply->error();
    if (error == QNetworkReply::NoError) {
        QByteArray data = m_currentImageListReply->readAll();
        QJsonParseError parseError;
        QJsonDocument doc = QJsonDocument::fromJson(data, &parseError);

        if (parseError.error == QJsonParseError::NoError && doc.isObject()) {
            QJsonObject obj = doc.object();

            // 合并images和folders数组
            QJsonArray allItems;
            if (obj.contains("folders") && obj["folders"].isArray()) {
                QJsonArray folders = obj["folders"].toArray();
                for (int i = 0; i < folders.size(); ++i) {
                    allItems.append(folders[i]);
                }
            }
            if (obj.contains("images") && obj["images"].isArray()) {
                QJsonArray images = obj["images"].toArray();
                for (int i = 0; i < images.size(); ++i) allItems.append(images[i]);
            }

            updateImageList(allItems);
            emit imageListLoaded(allItems);
            qDebug() << "成功加载图片列表，共" << allItems.size() << "项 (文件夹:" <<
                        (obj.contains("folderCount") ? obj["folderCount"].toInt() : 0) <<
                        ", 图片:" << (obj.contains("imageCount") ? obj["imageCount"].toInt() : 0) << ")";
        } else {
            qWarning() << "图片列表JSON解析失败:" << parseError.errorString();
        }
    } else {
        qWarning() << "加载图片列表失败:" << m_currentImageListReply->errorString();
    }

    m_currentImageListReply->deleteLater();
    m_currentImageListReply = nullptr;
}

void ClientMode::onNetworkError(QNetworkReply::NetworkError error)
{
    QNetworkReply *reply = qobject_cast<QNetworkReply*>(sender());
    if (!reply) return;

    QString errorMsg = QString("网络错误 (%1): %2").arg(static_cast<int>(error)).arg(reply->errorString());
    qWarning() << errorMsg;

    if (reply == m_currentTestReply) {
        setConnected(false);
        setConnectionStatus(errorMsg);
        emit connectionError(errorMsg);
    }
}

void ClientMode::setConnected(bool connected)
{
    if (m_connected != connected) {
        m_connected = connected;
        emit connectedChanged();
    }
}

void ClientMode::setConnectionStatus(const QString &status)
{
    if (m_connectionStatus != status) {
        m_connectionStatus = status;
        emit connectionStatusChanged();
    }
}

void ClientMode::updateImageList(const QJsonArray &images)
{
    m_imageList = images;
    emit imageListChanged();
}

QString ClientMode::formatServerUrl(const QString &host, int port)
{
    QString cleanHost = host.trimmed();

    // 如果已经包含协议，直接使用
    if (cleanHost.startsWith("http://") || cleanHost.startsWith("https://")) {
        return cleanHost;
    }

    // 检查是否是IPv6地址（包含冒号且不是IPv4:port格式）
    if (cleanHost.contains(':') && cleanHost.count(':') > 1) {
        // IPv6地址需要用方括号包围
        if (!cleanHost.startsWith('[')) {
            cleanHost = QString("[%1]").arg(cleanHost);
        }
    }

    // 添加http协议和端口
    return QString("http://%1:%2").arg(cleanHost).arg(port);
}

void ClientMode::setAccessToken(const QString &token)
{
    m_accessToken = token;
    qDebug() << "设置访问令牌:" << token;
}

// 兼容性方法实现
void ClientMode::connectToServer(const std::string& address, int port)
{
    connectToServer(QString::fromStdString(address), port);
}

void ClientMode::enableAutoReconnect(bool enable)
{
    m_autoReconnect = enable;
    // TODO: 实现自动重连逻辑
}

void ClientMode::discoverServers(std::function<void(const std::vector<std::string>&)> handler)
{
    m_discoveryHandler = handler;
    // TODO: 实现服务发现逻辑
    // 暂时返回空列表
    if (handler) {
        handler(std::vector<std::string>());
    }
}

// 缓存相关方法实现
void ClientMode::initializeCacheDirectories() {
    // 获取应用程序缓存目录
    QString appCacheDir = QStandardPaths::writableLocation(QStandardPaths::CacheLocation);
    m_cacheDir = appCacheDir + "/RemoteImages";
    m_thumbnailCacheDir = m_cacheDir + "/thumbnails";
    m_imageCacheDir = m_cacheDir + "/images";

    // 创建缓存目录
    QDir().mkpath(m_thumbnailCacheDir);
    QDir().mkpath(m_imageCacheDir);
}

QString ClientMode::getCachedThumbnailPath(const QString &imagePath) {
    QString cleanPath = imagePath;
    if (cleanPath.startsWith("/")) {
        cleanPath = cleanPath.mid(1); // 移除开头的斜杠
    }

    // 构建缩略图路径，保持文件夹结构但扩展名改为.jpg
    QFileInfo fileInfo(cleanPath);
    QString thumbnailPath = fileInfo.path() + "/" + fileInfo.baseName() + ".jpg";
    QString fullThumbnailPath = m_thumbnailCacheDir + "/" + thumbnailPath;

    // 确保目录存在
    QFileInfo thumbnailFileInfo(fullThumbnailPath);
    QDir dir = thumbnailFileInfo.absoluteDir();
    if (!dir.exists()) {
        dir.mkpath(".");
    }

    return fullThumbnailPath;
}

QString ClientMode::getCachedImagePath(const QString &imagePath) {
    // 保持原始文件夹结构和文件名
    QString cleanPath = imagePath;
    if (cleanPath.startsWith("/")) {
        cleanPath = cleanPath.mid(1); // 移除开头的斜杠
    }

    // 构建完整的缓存路径，保持文件夹结构
    QString fullCachePath = m_imageCacheDir + "/" + cleanPath;

    // 确保目录存在
    QFileInfo fileInfo(fullCachePath);
    QDir dir = fileInfo.absoluteDir();
    if (!dir.exists()) {
        dir.mkpath(".");
    }

    return fullCachePath;
}

bool ClientMode::isThumbnailCached(const QString &imagePath) {
    QString cachedPath = getCachedThumbnailPath(imagePath);
    return QFileInfo::exists(cachedPath);
}

bool ClientMode::isImageCached(const QString &imagePath) {
    QString cachedPath = getCachedImagePath(imagePath);
    return QFileInfo::exists(cachedPath);
}

void ClientMode::downloadThumbnailToCache(const QString &imagePath) {
    if (!m_connected || imagePath.isEmpty() || isThumbnailCached(imagePath)) {
        return;
    }

    // 避免重复下载
    static QSet<QString> downloadingThumbnails;
    if (downloadingThumbnails.contains(imagePath)) {
        return;
    }
    downloadingThumbnails.insert(imagePath);

    QString cleanPath = imagePath;
    if (cleanPath.startsWith("/")) {
        cleanPath = cleanPath.mid(1);
    }

    QString url = m_serverUrl + "/api/thumbnail/" + cleanPath;
    QNetworkRequest request(url);
    request.setRawHeader("User-Agent", "RemoteImageBrowser/1.0");

    // 添加令牌验证头
    QString token = m_accessToken.isEmpty() ? "imageviewer2025" : m_accessToken;
    request.setRawHeader("Authorization", QString("Bearer %1").arg(token).toUtf8());

    QNetworkReply *reply = m_networkManager->get(request);

    // 使用lambda捕获imagePath，在下载完成时保存到缓存
    connect(reply, &QNetworkReply::finished, [this, reply, imagePath]() {
        static QSet<QString> downloadingThumbnails;
        downloadingThumbnails.remove(imagePath);
        if (reply->error() == QNetworkReply::NoError) {
            QByteArray data = reply->readAll();
            if (!data.isEmpty()) {
                QString cachedPath = getCachedThumbnailPath(imagePath);
                QFile file(cachedPath);
                if (file.open(QIODevice::WriteOnly)) {
                    file.write(data);
                    file.close();
                    emit thumbnailCached(imagePath);
                } else {
                    qWarning() << "无法写入缓存文件:" << cachedPath;
                }
            }
        } else {
            qWarning() << "下载缩略图失败:" << reply->errorString();
        }
        reply->deleteLater();
    });
}

void ClientMode::downloadImageToCache(const QString &imagePath) {
    if (!m_connected || imagePath.isEmpty() || isImageCached(imagePath)) {
        return;
    }

    // 避免重复下载
    static QSet<QString> downloadingImages;
    if (downloadingImages.contains(imagePath)) {
        return;
    }
    downloadingImages.insert(imagePath);

    QString cleanPath = imagePath;
    if (cleanPath.startsWith("/")) {
        cleanPath = cleanPath.mid(1);
    }

    QString url = m_serverUrl + "/api/image/" + cleanPath;
    QNetworkRequest request(url);
    request.setRawHeader("User-Agent", "RemoteImageBrowser/1.0");

    // 添加令牌验证头
    QString token = m_accessToken.isEmpty() ? "imageviewer2025" : m_accessToken;
    request.setRawHeader("Authorization", QString("Bearer %1").arg(token).toUtf8());

    QNetworkReply *reply = m_networkManager->get(request);

    // 使用lambda捕获imagePath，在下载完成时保存到缓存
    connect(reply, &QNetworkReply::finished, [this, reply, imagePath]() {
        static QSet<QString> downloadingImages;
        downloadingImages.remove(imagePath);
        if (reply->error() == QNetworkReply::NoError) {
            QByteArray data = reply->readAll();
            if (!data.isEmpty()) {
                QString cachedPath = getCachedImagePath(imagePath);
                QFile file(cachedPath);
                if (file.open(QIODevice::WriteOnly)) {
                    file.write(data);
                    file.close();
                    emit imageCached(imagePath);
                } else {
                    qWarning() << "无法写入缓存文件:" << cachedPath;
                }
            }
        } else {
            qWarning() << "下载图片失败:" << reply->errorString();
        }
        reply->deleteLater();
    });
}

void ClientMode::clearRemoteCache() {
    // 异步清理远程缓存
    QFuture<void> future = QtConcurrent::run([this]() -> void {
        int deletedCount = 0;

        // 清理缩略图缓存
        QDir thumbnailDir(m_thumbnailCacheDir);
        if (thumbnailDir.exists()) {
            QStringList files = thumbnailDir.entryList(QDir::Files | QDir::NoDotAndDotDot, QDir::NoSort);
            for (const QString &fileName : files) {
                if (QFile::remove(thumbnailDir.filePath(fileName))) {
                    deletedCount++;
                }
            }
            // 递归删除子目录
            QStringList subDirs = thumbnailDir.entryList(QDir::Dirs | QDir::NoDotAndDotDot, QDir::NoSort);
            for (const QString &subDir : subDirs) {
                QDir dir(thumbnailDir.filePath(subDir));
                if (dir.removeRecursively()) {
                    deletedCount++;
                }
            }
        }

        // 清理图片缓存
        QDir imageDir(m_imageCacheDir);
        if (imageDir.exists()) {
            QStringList files = imageDir.entryList(QDir::Files | QDir::NoDotAndDotDot, QDir::NoSort);
            for (const QString &fileName : files) {
                if (QFile::remove(imageDir.filePath(fileName))) {
                    deletedCount++;
                }
            }
            // 递归删除子目录
            QStringList subDirs = imageDir.entryList(QDir::Dirs | QDir::NoDotAndDotDot, QDir::NoSort);
            for (const QString &subDir : subDirs) {
                QDir dir(imageDir.filePath(subDir));
                if (dir.removeRecursively()) {
                    deletedCount++;
                }
            }
        }

        qDebug() << "远程缓存清理完成，删除了" << deletedCount << "个文件/目录";
    });
    Q_UNUSED(future)
}

qint64 ClientMode::getRemoteCacheSize() const {
    qint64 totalSize = 0;

    // 计算缩略图缓存大小
    QDir thumbnailDir(m_thumbnailCacheDir);
    if (thumbnailDir.exists()) {
        QDirIterator it(m_thumbnailCacheDir, QDir::Files, QDirIterator::Subdirectories);
        while (it.hasNext()) {
            it.next();
            totalSize += it.fileInfo().size();
        }
    }

    // 计算图片缓存大小
    QDir imageDir(m_imageCacheDir);
    if (imageDir.exists()) {
        QDirIterator it(m_imageCacheDir, QDir::Files, QDirIterator::Subdirectories);
        while (it.hasNext()) {
            it.next();
            totalSize += it.fileInfo().size();
        }
    }

    return totalSize;
}
