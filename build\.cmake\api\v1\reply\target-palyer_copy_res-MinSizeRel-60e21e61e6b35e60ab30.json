{"backtrace": 5, "backtraceGraph": {"commands": ["add_custom_target", "_qt_internal_qml_copy_files_to_build_dir", "qt6_target_qml_sources", "qt6_add_qml_module", "qt_add_qml_module"], "files": ["C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Qml/Qt6QmlMacros.cmake", "CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 4, "file": 1, "line": 34, "parent": 0}, {"command": 3, "file": 0, "line": 1252, "parent": 1}, {"command": 2, "file": 0, "line": 916, "parent": 2}, {"command": 1, "file": 0, "line": 3503, "parent": 3}, {"command": 0, "file": 0, "line": 2841, "parent": 4}]}, "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "palyer_copy_res::@6890427a1f51a3e7e1df", "name": "palyer_copy_res", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 5, "isGenerated": true, "path": "build/CMakeFiles/palyer_copy_res", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/60efe0b784417ad25bcf545a28e72a55/palyer_copy_res.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/d473abdc113deac55e77a347ea967924/palyer_res.txt.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}