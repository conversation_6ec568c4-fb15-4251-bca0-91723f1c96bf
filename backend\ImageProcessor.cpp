#include "ImageProcessor.h"
#include <QDir>
#include <QFileInfo>
#include <QImageReader>
#include <QDebug>
#include <QtConcurrent>
#include <algorithm>
#include <QThreadPool>
#include <QGuiApplication>
#include <QScreen>
#include <QTemporaryFile>
#include <QPainter>
#include <QCryptographicHash>
#include <QFile>
#include <QStandardPaths>
#include <QCoreApplication>
#include <QProcess>
#include <QPixmap>
#include <QQmlEngine>
#include <QQmlContext>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonDocument>
#include <QBuffer>
#include <QDateTime>
#include <QRunnable>
#include <QAtomicInteger>
#include <QPointer>

// 构造函数
ImageProcessor::ImageProcessor(QObject *parent) : QAbstractListModel(parent)
{
    // 初始化支持的图片格式和角色名称
    // 注意：包含GIF格式用于浏览，但GIF不会被转换管理器处理
    m_supportedFormats = {"jpg", "jpeg", "png", "gif", "bmp", "webp", "tiff", "tif", "avif"};
    m_roleNames = {{FilePathRole, "filePath"}, {FileNameRole, "fileName"},
                  {FileSizeRole, "fileSize"}, {LastModifiedRole, "lastModified"},
                  {ThumbnailRole, "thumbnail"}, {IsLoadingRole, "isLoading"},
                  {IsFolderRole, "isFolder"}};

    // 设置缓存大小并连接信号
    m_thumbnailCache.setMaxCost(50 * 1024 * 1024); // 50MB 缓存
    connect(this, &ImageProcessor::thumbnailReady, this, &ImageProcessor::handleThumbnailReady);

    // 初始化最大图像尺寸
    m_maxImageSize = getOptimalImageSize();

    // 设置缩略图线程池的最大线程数为2
    m_thumbnailThreadPool.setMaxThreadCount(2);

    // 添加WebP格式支持（通过命令行工具处理）
    m_supportedFormats.insert("webp");

    // 确保AVIF格式被明确添加
    m_supportedFormats.insert("avif");

    // 初始化缩略图缓存目录（创建在可执行文件同级目录）
    QString exePath = QCoreApplication::applicationDirPath();
    m_thumbCachePath = exePath + "/thumbnails";
    QDir cacheDir(m_thumbCachePath);
    if (!cacheDir.exists()) cacheDir.mkpath(".");
}

ImageProcessor::~ImageProcessor() {
    { QMutexLocker locker(&m_mutex); m_thumbnailTaskQueue.clear(); m_processingIndices.clear(); }
    m_thumbnailThreadPool.waitForDone();
    m_thumbnailCache.clear();
}

// QAbstractListModel接口实现
int ImageProcessor::rowCount(const QModelIndex &parent) const
{
    return parent.isValid() ? 0 : m_fileInfoList.count();
}

QVariant ImageProcessor::data(const QModelIndex &index, int role) const
{
    if (!index.isValid() || index.row() < 0 || index.row() >= m_fileInfoList.count())
        return QVariant();

    const QFileInfo &fileInfo = m_fileInfoList.at(index.row());

    switch (role) {
    case FilePathRole: return fileInfo.absoluteFilePath();
    case FileNameRole: return fileInfo.fileName();
    case FileSizeRole: return fileInfo.size();
    case LastModifiedRole: return fileInfo.lastModified();
    case ThumbnailRole: {
        QMutexLocker locker(&m_mutex);
        if (QImage* cachedImage = m_thumbnailCache.object(fileInfo.absoluteFilePath())) {
            return *cachedImage;
        }
        return QVariant();
    }
    case IsFolderRole: return fileInfo.isDir();
    case IsLoadingRole: {
        QMutexLocker locker(&m_mutex);
        return m_processingIndices.contains(index.row());
    }
    default: return QVariant();
    }
}

QHash<int, QByteArray> ImageProcessor::roleNames() const
{
    return m_roleNames;
}

// 设置当前文件夹
void ImageProcessor::setCurrentFolder(const QString &folder)
{
    if (m_currentFolder != folder) {
        m_currentFolder = folder;
        emit currentFolderChanged();
        // 自动加载新文件夹内容，包括子文件夹
        loadImagesFromFolder(folder, true);
    }
}

// 设置排序模式
void ImageProcessor::setSortMode(int mode)
{
    if (m_sortMode != mode) {
        m_sortMode = mode;
        emit sortModeChanged();
        sortImageList(mode); // 应用新的排序模式
    }
}

// 静态私有辅助函数，用于自然排序字符串（例如："file2.txt" < "file10.txt"）
static bool naturalSortComparator(const QString &s1, const QString &s2)
{
    int len1 = s1.length();
    int len2 = s2.length();
    int i = 0, j = 0;

    while (i < len1 && j < len2) {
        if (s1[i].isDigit() && s2[j].isDigit()) {
            // 比较数字部分
            QString num1, num2;
            while (i < len1 && s1[i].isDigit()) num1 += s1[i++];
            while (j < len2 && s2[j].isDigit()) num2 += s2[j++];

            if (num1.length() == num2.length()) {
                if (num1 != num2) return num1 < num2;
                // 数字相同，继续比较后续部分 (如果数字长度不同，长度短的优先)
            } else {
                return num1.length() < num2.length();
            }
        } else {
            // 比较非数字部分 (不区分大小写)
            QChar c1 = s1[i].toLower();
            QChar c2 = s2[j].toLower();
            if (c1 != c2) return c1 < c2;
            i++; j++;
        }
    }
    // 如果一个字符串是另一个的前缀，则较短的优先
    return len1 < len2;
}

// 私有辅助函数：从指定目录收集文件和文件夹信息
QList<QFileInfo> ImageProcessor::collectEntriesFromPath(const QString &folderPath, bool includeFolders) {
    QList<QFileInfo> entries;
    QDir dir(folderPath);

    if (!dir.exists()) {
        setLastError("文件夹不存在: " + folderPath);
        return entries; // 返回空列表
    }

    // 1. 获取文件夹 (如果需要)
    if (includeFolders) {
        dir.setFilter(QDir::Dirs | QDir::NoDotAndDotDot | QDir::Readable);
        QFileInfoList folders = dir.entryInfoList();
        entries.append(folders);
    }

    // 2. 获取符合支持格式的图片文件
    QStringList nameFilters;
    for (const QString &format : std::as_const(m_supportedFormats)) nameFilters << "*." + format;
    dir.setNameFilters(nameFilters);
    dir.setFilter(QDir::Files | QDir::NoDotAndDotDot | QDir::Readable);
    QFileInfoList imageFiles = dir.entryInfoList();
    entries.append(imageFiles);

    // 对文件部分进行自然排序，文件夹部分保持原顺序
    QList<QFileInfo> folders, files;
    for (const QFileInfo &info : std::as_const(entries)) {
        if (info.isDir()) folders.append(info);
        else files.append(info);
    }
    std::sort(files.begin(), files.end(), [](const QFileInfo &a, const QFileInfo &b) {
        return naturalSortComparator(a.fileName(), b.fileName());
    });
    entries = folders + files;

    return entries;
}

class ImageProcessor::ThumbnailTask : public QRunnable {
public:
    ThumbnailTask(int index, ImageProcessor* processor, QAtomicInteger<bool>& stopFlag)
        : m_index(index), m_processor(processor), m_stopFlag(stopFlag) {}
    void run() override {
        if (m_stopFlag) return;
        m_processor->processThumbnailTask(m_index, m_stopFlag);
    }
private:
    int m_index;
    ImageProcessor* m_processor;
    QAtomicInteger<bool>& m_stopFlag;
};

// 简化的缩略图生成任务
class ImageProcessor::ThumbnailGenerationTask : public QRunnable {
public:
    ThumbnailGenerationTask(int index, ImageProcessor* processor, const QString& filePath, const QString& thumbPath)
        : m_index(index), m_processor(processor), m_filePath(filePath), m_thumbPath(thumbPath) {}
    void run() override {
        QPointer<ImageProcessor> safeThis(m_processor);
        if (!safeThis) return;
        if (QFile::exists(m_thumbPath)) {
            QMetaObject::invokeMethod(safeThis, [safeThis, this]() {
                if (safeThis) { QMutexLocker locker(&safeThis->m_mutex); safeThis->m_processingIndices.remove(m_index); }
                if (safeThis) emit safeThis->thumbnailReady(m_index);
            }, Qt::QueuedConnection);
            return;
        }
        QImage thumbnail = safeThis->generateThumbnail(m_filePath);
        if (!thumbnail.isNull()) { QMutexLocker locker(&safeThis->m_mutex); safeThis->m_thumbnailCache.insert(m_filePath, new QImage(thumbnail)); }
        QMetaObject::invokeMethod(safeThis, [safeThis, this]() {
            if (safeThis) { QMutexLocker locker(&safeThis->m_mutex); safeThis->m_processingIndices.remove(m_index); }
            if (safeThis) emit safeThis->thumbnailReady(m_index);
        }, Qt::QueuedConnection);
    }
private:
    int m_index; ImageProcessor* m_processor; QString m_filePath, m_thumbPath;
};

void ImageProcessor::cancelAllTasks() {
    QMutexLocker locker(&m_mutex);
    m_thumbnailTaskQueue.clear();
    m_processingIndices.clear();
}

void ImageProcessor::resetForNewFolder() {
    QMutexLocker locker(&m_mutex);
    m_thumbnailTaskQueue.clear();
    m_processingIndices.clear();
    m_fileInfoList.clear();
    m_thumbnailCache.clear();
    locker.unlock();
    m_thumbnailThreadPool.clear();
}

void ImageProcessor::loadImagesFromFolder(const QString &folderPath, bool includeFolders)
{
    cancelAllTasks();
    m_stopFlag = false;
    {
        QMutexLocker locker(&m_mutex);
        m_thumbnailTaskQueue.clear();
        m_processingIndices.clear();
    }
    beginResetModel();
    m_fileInfoList.clear();
    endResetModel();
    emit countChanged();

    QList<QFileInfo> newFiles = collectEntriesFromPath(folderPath, includeFolders);
    beginResetModel();
    m_fileInfoList = newFiles;
    endResetModel();
    emit countChanged();

    // 分发新任务
    for (int i = 0; i < m_fileInfoList.size(); ++i) {
        if (!m_fileInfoList[i].isDir()) {
            m_thumbnailThreadPool.start(new ThumbnailTask(i, this, m_stopFlag));
        }
    }
}

void ImageProcessor::processThumbnailTask(int index, QAtomicInteger<bool>& stopFlag) {
    if (stopFlag) return;
    if (index < 0 || index >= m_fileInfoList.size()) return;
    const QFileInfo &fileInfo = m_fileInfoList.at(index);
    if (fileInfo.isDir()) return;
    const QString filePath = fileInfo.absoluteFilePath();
    const QString thumbPath = thumbPathForFile(filePath);
    if (QFile::exists(thumbPath)) {
        QImage testImage(thumbPath);
        if (!testImage.isNull()) {
            QMetaObject::invokeMethod(this, [this, index]() {
                emit thumbnailReady(index);
            }, Qt::QueuedConnection);
            return;
        } else {
            QFile::remove(thumbPath);
        }
    }
    if (stopFlag) return;
    QImage thumbnail = generateThumbnail(filePath);
    if (stopFlag) return;
    if (!thumbnail.isNull()) {
        QMutexLocker locker(&m_mutex);
        m_thumbnailCache.insert(filePath, new QImage(thumbnail));
    }
    QMetaObject::invokeMethod(this, [this, index]() {
        emit thumbnailReady(index);
    }, Qt::QueuedConnection);
}

// 根据排序模式排序图片列表
void ImageProcessor::sortImageList(int sortMode)
{
    if (m_fileInfoList.isEmpty()) return;

    beginResetModel();

    // 分离文件夹和文件，以便文件夹总是在前面（或根据特定需求）
    QList<QFileInfo> folders;
    QList<QFileInfo> files;
    for (const QFileInfo &info : std::as_const(m_fileInfoList)) {
        if (info.isDir()) {
            folders.append(info);
        } else {
            files.append(info);
        }
    }

    // 对文件夹和文件分别排序
    switch (sortMode) {
        case 0: // 名称 (自然排序)
        default:
            std::sort(folders.begin(), folders.end(), [](const QFileInfo &a, const QFileInfo &b) {
                return naturalSortComparator(a.fileName(), b.fileName());
            });
            std::sort(files.begin(), files.end(), [](const QFileInfo &a, const QFileInfo &b) {
                return naturalSortComparator(a.fileName(), b.fileName());
            });
            break;
        case 1: // 名称 (字母顺序)
            std::sort(folders.begin(), folders.end(), [](const QFileInfo &a, const QFileInfo &b) {
                return a.fileName().compare(b.fileName(), Qt::CaseInsensitive) < 0;
            });
            std::sort(files.begin(), files.end(), [](const QFileInfo &a, const QFileInfo &b) {
                return a.fileName().compare(b.fileName(), Qt::CaseInsensitive) < 0;
            });
            break;
        case 2: // 时间 (降序，新的在前)
            std::sort(folders.begin(), folders.end(), [](const QFileInfo &a, const QFileInfo &b) {
                return a.lastModified() > b.lastModified();
            });
            std::sort(files.begin(), files.end(), [](const QFileInfo &a, const QFileInfo &b) {
                return a.lastModified() > b.lastModified();
            });
            break;
        case 3: // 大小 (升序，小的在前) - 文件夹大小通常为0或固定值，主要对文件有意义
            std::sort(folders.begin(), folders.end(), [](const QFileInfo &a, const QFileInfo &b) {
                return naturalSortComparator(a.fileName(), b.fileName()); // 文件夹按名称排序
            });
            std::sort(files.begin(), files.end(), [](const QFileInfo &a, const QFileInfo &b) {
                return a.size() < b.size();
            });
            break;
        case 4: // 大小 (降序，大的在前)
             std::sort(folders.begin(), folders.end(), [](const QFileInfo &a, const QFileInfo &b) {
                return naturalSortComparator(a.fileName(), b.fileName()); // 文件夹按名称排序
            });
            std::sort(files.begin(), files.end(), [](const QFileInfo &a, const QFileInfo &b) {
                return a.size() > b.size();
            });
            break;
        case 5: // 类型 (按扩展名排序)
            std::sort(folders.begin(), folders.end(), [](const QFileInfo &a, const QFileInfo &b) {
                return naturalSortComparator(a.fileName(), b.fileName()); // 文件夹按名称排序
            });
            std::sort(files.begin(), files.end(), [](const QFileInfo &a, const QFileInfo &b) {
                QString suffixA = a.suffix().toLower();
                QString suffixB = b.suffix().toLower();
                if (suffixA != suffixB) return suffixA < suffixB;
                return naturalSortComparator(a.fileName(), b.fileName());
            });
            break;
        case 6: // 创建时间（降序，新的在前）
            std::sort(folders.begin(), folders.end(), [](const QFileInfo &a, const QFileInfo &b) {
                return a.birthTime() > b.birthTime();
            });
            std::sort(files.begin(), files.end(), [](const QFileInfo &a, const QFileInfo &b) {
                return a.birthTime() > b.birthTime();
            });
            break;
        case 7: // 自然排序倒序
            std::sort(folders.begin(), folders.end(), [](const QFileInfo &a, const QFileInfo &b) {
                return !naturalSortComparator(a.fileName(), b.fileName());
            });
            std::sort(files.begin(), files.end(), [](const QFileInfo &a, const QFileInfo &b) {
                return !naturalSortComparator(a.fileName(), b.fileName());
            });
            break;
    }

    // 合并排序后的列表，文件夹在前
    m_fileInfoList.clear();
    m_fileInfoList.append(folders);
    m_fileInfoList.append(files);

    endResetModel();
    emit countChanged(); // 确保在模型重置后更新计数

    // 排序后重新按顺序初始化缩略图生成队列
    startThumbnailGeneration();
}

// 刷新当前文件夹
void ImageProcessor::refreshCurrentFolder()
{
    // 重新加载当前文件夹，使用之前设定的 m_includeFolders 状态
    if (!m_currentFolder.isEmpty()) loadImagesFromFolder(m_currentFolder, m_includeFolders);
}

// 清除缓存
void ImageProcessor::clearCache()
{
    m_thumbnailCache.clear();
}

// 清除所有缓存文件
void ImageProcessor::clearAllCacheFiles()
{
    // 清除内存缓存
    m_thumbnailCache.clear();

    // 异步删除缓存文件夹中的所有文件
    QFuture<void> future = QtConcurrent::run([this]() -> void {
        QDir cacheDir(m_thumbCachePath);
        if (cacheDir.exists()) {
            QStringList files = cacheDir.entryList(QDir::Files);
            int deletedCount = 0;
            for (const QString &fileName : files) {
                if (QFile::remove(cacheDir.filePath(fileName))) {
                    deletedCount++;
                }
            }
            qDebug() << "清理缓存完成，删除了" << deletedCount << "个缓存文件";
        }
    });
    Q_UNUSED(future)
}

// 获取缓存大小
qint64 ImageProcessor::getCacheSize() const
{
    QDir cacheDir(m_thumbCachePath);
    qint64 totalSize = 0;
    if (cacheDir.exists()) {
        QStringList files = cacheDir.entryList(QDir::Files);
        for (const QString &fileName : files) {
            QFileInfo fileInfo(cacheDir.filePath(fileName));
            totalSize += fileInfo.size();
        }
    }
    return totalSize;
}

// 获取文件路径
QString ImageProcessor::getFilePath(int index) const
{
    if (index >= 0 && index < m_fileInfoList.count()) return m_fileInfoList.at(index).absoluteFilePath();
    return QString();
}

// 获取文件名
QString ImageProcessor::getFileName(int index) const
{
    if (index >= 0 && index < m_fileInfoList.count()) return m_fileInfoList.at(index).fileName();
    return QString();
}

// 检查文件是否为支持的图像格式 (基于文件后缀名)
bool ImageProcessor::isImageFile(const QString &filePath) const
{
    QFileInfo fileInfo(filePath);
    QString suffix = fileInfo.suffix().toLower();
    return m_supportedFormats.contains(suffix);
}

// 工具函数：获取缩略图路径
QString ImageProcessor::thumbPathForFile(const QString &filePath) const {
    QString fileHash = QCryptographicHash::hash(filePath.toUtf8(), QCryptographicHash::Md5).toHex();
    return m_thumbCachePath + "/" + fileHash + ".jpg";
}

// 工具函数：确保缩略图缓存目录存在
void ImageProcessor::ensureThumbCacheDir() const {
    QDir dir(m_thumbCachePath);
    if (!dir.exists()) dir.mkpath(".");
}

// 开始异步生成缩略图 (已修复线程安全和缩放问题)
void ImageProcessor::startThumbnailGeneration()
{
    QMutexLocker locker(&m_mutex);
    m_thumbnailTaskQueue.clear();
    m_processingIndices.clear();
    // 不再全量 enqueue，由 requestThumbnails 动态补充
}

// 为指定文件生成缩略图
QImage ImageProcessor::generateThumbnail(const QString &filePath) const
{
    QFileInfo fileInfo(filePath);
    QString suffix = fileInfo.suffix().toLower();
    bool isAvif = (suffix == "avif");

    QString thumbPath = thumbPathForFile(filePath);

    // 统一机制：先检查缩略图是否已存在
    if (QFile::exists(thumbPath)) {
        QImage thumbnail(thumbPath);
        if (!thumbnail.isNull()) {
            return thumbnail;
        }
    }

    ensureThumbCacheDir();

    if (isAvif) {
        // AVIF使用FFmpeg直接生成到目标路径
        QProcess ffmpegProcess;
        QStringList arguments;
        arguments << "-y" << "-i" << filePath << "-vf" << QString("scale=-1:%1:flags=lanczos").arg(m_thumbnailSize)
                  << "-qscale:v" << "2" << "-preset" << "fast" << thumbPath;
        ffmpegProcess.start("ffmpeg", arguments);
        if (ffmpegProcess.waitForFinished(5000)) {
            if (ffmpegProcess.exitCode() == 0) {
                QImage thumbnail(thumbPath);
                if (!thumbnail.isNull()) {
                    qDebug() << "使用FFmpeg成功生成AVIF缩略图:" << filePath;
                    return thumbnail;
                }
            } else {
                qWarning() << "FFmpeg缩略图生成失败:" << ffmpegProcess.readAllStandardError();
            }
        } else {
            qWarning() << "FFmpeg命令执行超时";
        }
        return QImage();
    } else {
        // 其他格式使用Qt内置方法，直接生成到目标路径
        QImageReader reader(filePath);
        reader.setAutoTransform(true);
        reader.setQuality(30);
        reader.setDecideFormatFromContent(true);
        reader.setAutoDetectImageFormat(true);
        QImage image = reader.read();
        if (image.isNull()) {
            qWarning() << "无法加载图像: " << filePath;
            return QImage();
        }

        // 缩放图像
        double aspectRatio = static_cast<double>(image.width()) / image.height();
        int targetHeight = m_thumbnailSize;
        int targetWidth = qRound(targetHeight * aspectRatio);
        QImage thumbnail = image.scaled(targetWidth, targetHeight, Qt::KeepAspectRatio, Qt::SmoothTransformation);

        // 直接保存到目标路径
        if (thumbnail.save(thumbPath, "JPEG", 90)) {
            QFile::setPermissions(thumbPath, QFile::ReadOwner | QFile::WriteOwner | QFile::ReadUser | QFile::ReadGroup | QFile::ReadOther);
            return thumbnail;
        } else {
            qWarning() << "缩略图保存失败:" << thumbPath;
            return QImage();
        }
    }
}

// 解码图像方法
QImage ImageProcessor::decodeImage(const QString &filePath, bool thumbnail) const
{
    QImage image;
    QFileInfo fileInfo(filePath);
    if (!fileInfo.exists() || !fileInfo.isReadable()) {
        qWarning() << "文件不存在或不可读: " << filePath;
        return image;
    }
    QString suffix = fileInfo.suffix().toLower();

    // 对AVIF文件，直接使用QImageReader尝试解码
    // 如果需要更好的AVIF支持，可以使用ffmpeg.exe命令行工具
    QImageReader reader(filePath);
    reader.setAutoTransform(true);
    reader.setQuality(thumbnail ? 30 : 75);
    reader.setDecideFormatFromContent(true);
    reader.setAutoDetectImageFormat(true);
    if (thumbnail) {
        reader.setScaledSize(QSize(m_thumbnailSize, m_thumbnailSize));
    }
    image = reader.read();
    if (image.isNull()) {
        reader.setQuality(10);
        image = reader.read();
        if (image.isNull()) {
            qWarning() << "读取图像失败: " << filePath << reader.errorString();
        }
    }
    return image;
}

// SLOTS

// 处理缩略图准备好的信号 (在主线程中调用)
void ImageProcessor::handleThumbnailReady(int index)
{
    if (index < 0 || index >= m_fileInfoList.count()) return;
    // 通知视图更新该项的缩略图和加载状态
    QModelIndex modelIndex = this->index(index, 0);
    emit dataChanged(modelIndex, modelIndex, {ThumbnailRole, IsLoadingRole});
}

// 设置错误信息
void ImageProcessor::setLastError(const QString &error)
{
    if (m_lastError != error) {
        m_lastError = error;
        emit lastErrorChanged();
    }
}

// 设置处理状态
void ImageProcessor::setProcessing(bool processing)
{
    if (m_processing != processing) {
        m_processing = processing;
        emit processingChanged();
    }
}

// 获取基于屏幕分辨率的最佳图像大小
QSize ImageProcessor::getOptimalImageSize() const
{
    // 获取主屏幕
    QScreen *screen = QGuiApplication::primaryScreen();
    if (!screen) return QSize(1920, 1080);

    // 获取屏幕物理尺寸
    QSize screenSize = screen->size();

    return QSize(screenSize.width(), screenSize.height());
}

// 获取文件大小
qint64 ImageProcessor::getFileSize(int index) const
{
    if (index >= 0 && index < m_fileInfoList.count())
        return m_fileInfoList.at(index).size();
    return 0;
}

// 获取图像信息
QString ImageProcessor::getImageInfo(int index) const
{
    if (index < 0 || index >= m_fileInfoList.count() || m_fileInfoList.at(index).isDir())
        return QString();

    const QFileInfo &fileInfo = m_fileInfoList.at(index);
    QString filePath = fileInfo.absoluteFilePath();
    qint64 fileSize = fileInfo.size();

    // 格式化文件大小
    QString fileSizeText = fileSize >= 1048576 ? QString::number(fileSize / 1048576.0, 'f', 1) + "MB"
        : QString::number(fileSize / 1024) + "KB";

    // 获取图像尺寸
    QSize imageSize = QImageReader(filePath).size();

    // 如果QImageReader无法获取尺寸且是WebP格式，尝试使用WebP解码器
    QString suffix = fileInfo.suffix().toLower();
    bool isPartiallyDecoded = false;
    QString validAreaInfo;

    // WebP文件尺寸检测现在通过Qt内置支持或命令行工具处理
    if (!imageSize.isValid() && suffix == "webp") {
        QImageReader reader(filePath);
        if (reader.canRead()) {
            imageSize = reader.size();
        }
    }

    // 组合信息
    if (imageSize.isValid()) {
        QString info = QString("%1×%2 [%3]").arg(imageSize.width()).arg(imageSize.height()).arg(fileSizeText);
        // 如果是部分解码，添加有效区域信息
        if (isPartiallyDecoded && !validAreaInfo.isEmpty()) {
            info += QString(" 有效区域:%1").arg(validAreaInfo);
        }
        return info;
    }

    return QString("[%1]").arg(fileSizeText);
}

QSize ImageProcessor::getOptimalSize() const
{
    return getOptimalImageSize();
}

// 设置解码器类型
void ImageProcessor::setDecoderType(int type)
{
    if (m_decoderType != type && type == 0) {
        m_decoderType = type;
        emit decoderTypeChanged();

        // 清除缓存，以便使用新解码器重新加载图像
        clearCache();


        // 如果当前有文件夹，刷新它以使用新解码器
        if (!m_currentFolder.isEmpty()) {
            refreshCurrentFolder();
        }
    }
}

// 切换解码器
void ImageProcessor::switchDecoder(int decoderType)
{
    setDecoderType(decoderType);
}

// 获取缩略图的源文件路径
Q_INVOKABLE QString ImageProcessor::getThumbnailSource(int index)
{
    if (index < 0 || index >= m_fileInfoList.count()) {
        return QString();
    }
    const QFileInfo &fileInfo = m_fileInfoList.at(index);
    if (fileInfo.isDir()) {
        return QString();
    }
    const QString filePath = fileInfo.absoluteFilePath();

    // 使用MD5哈希值作为缩略图文件名，确保唯一性
    QString thumbPath = thumbPathForFile(filePath);

    // 如果缩略图已存在，直接返回路径
    if (QFile::exists(thumbPath)) return "file:///" + QDir::fromNativeSeparators(thumbPath);

    // 检查是否已经在处理中
    {
        QMutexLocker locker(&m_mutex);
        if (m_processingIndices.contains(index)) return QString(); // 已在处理中，返回空字符串
        m_processingIndices.insert(index);
    }

    m_thumbnailThreadPool.start(new ThumbnailGenerationTask(index, this, filePath, thumbPath));

    // 返回空字符串，表示缩略图尚未准备好
    return QString();
}

// 新增QML可调用函数，用于从缓存中获取缩略图的URL
Q_INVOKABLE QString ImageProcessor::getThumbnailImage(int index) const
{
    if (index < 0 || index >= m_fileInfoList.count()) return QString();

    const QFileInfo &fileInfo = m_fileInfoList.at(index);
    if (fileInfo.isDir()) return QString();

    const QString filePath = fileInfo.absoluteFilePath();
    const QString suffix = fileInfo.suffix().toLower();
    const bool isAvif = (suffix == "avif");

    // 使用MD5哈希值作为缩略图文件名
    QString thumbPath = thumbPathForFile(filePath);

    // 优先检查已缓存的缩略图
    if (QFile::exists(thumbPath)) return "file:///" + QDir::fromNativeSeparators(thumbPath);

    // AVIF文件特殊处理 - 如果没有缩略图缓存，可以使用ffmpeg.exe命令行工具生成
    if (isAvif) return QString();

    // 如果内存中有缓存图像，保存并返回路径
    QImage* cachedImage = nullptr;
    {
        QMutexLocker locker(&m_mutex);
        cachedImage = m_thumbnailCache.object(filePath);
    }

    if (cachedImage && !cachedImage->isNull()) {
        ensureThumbCacheDir();

        // 尝试保存为JPEG格式（更兼容，文件会更小）
        if (thumbPath == filePath) { qWarning() << "禁止缩略图覆盖原图:" << filePath; return QString(); }
        if (cachedImage->save(thumbPath, "JPEG", 90)) {
            QFile::setPermissions(thumbPath, QFile::ReadOwner | QFile::WriteOwner | QFile::ReadUser | QFile::ReadGroup | QFile::ReadOther);
            return "file:///" + QDir::fromNativeSeparators(thumbPath);
        }
    }

    // 缩略图不在磁盘上也不在内存中
    return QString();
}

// 解码图像并返回QImage，用于预览窗口
Q_INVOKABLE QImage ImageProcessor::getPreviewImage(int index) const
{
    if (index < 0 || index >= m_fileInfoList.count() || m_fileInfoList.at(index).isDir()) { qWarning() << "getPreviewImage: 无效索引或是文件夹" << index; return QImage(); }
    const QString filePath = m_fileInfoList.at(index).absoluteFilePath();
    QFileInfo fileInfo(filePath);
    if (!fileInfo.exists() || !fileInfo.isReadable()) { qWarning() << "文件不存在或不可读: " << filePath; return QImage(); }
    const QString suffix = fileInfo.suffix().toLower();
    // 对于AVIF文件，直接使用QImageReader尝试解码
    // 如果需要更好的AVIF支持，可以使用ffmpeg.exe命令行工具
    QImageReader reader(filePath);
    reader.setAutoTransform(true);
    reader.setDecideFormatFromContent(true);
    reader.setAutoDetectImageFormat(true);
    QImage image = reader.read();
    if (image.isNull()) qWarning() << "图像读取失败:" << filePath << reader.errorString();
    return image;
}

void ImageProcessor::requestThumbnails(int startIndex, int endIndex) {
    QMutexLocker locker(&m_mutex);
    for (int i = startIndex; i <= endIndex; ++i) {
        if (i < 0 || i >= m_fileInfoList.size()) continue;
        if (m_processingIndices.contains(i)) continue;
        if (m_thumbnailTaskQueue.contains(i)) continue;
        const QFileInfo &fileInfo = m_fileInfoList.at(i);
        if (fileInfo.isDir()) continue;
        QString thumbPath = thumbPathForFile(fileInfo.absoluteFilePath());
        if (QFile::exists(thumbPath)) continue;
        m_thumbnailTaskQueue.enqueue(i);
    }
    if (!m_thumbnailWorkerRunning.load()) {
        m_thumbnailWorkerRunning.store(true);
        QFuture<void> future = QtConcurrent::run(&m_thumbnailThreadPool, [this]() -> void { thumbnailWorker(); });
        Q_UNUSED(future)
    }
}

void ImageProcessor::thumbnailWorker() {
    while (true) {
        int index = -1;
        {
            QMutexLocker locker(&m_mutex);
            if (m_thumbnailTaskQueue.isEmpty()) {
                m_thumbnailWorkerRunning.store(false);
                return;
            }
            index = m_thumbnailTaskQueue.dequeue();
            m_processingIndices.insert(index);
        }

        if (index < 0 || index >= m_fileInfoList.size()) {
            QMutexLocker locker(&m_mutex);
            m_processingIndices.remove(index);
            continue;
        }

        const QFileInfo &fileInfo = m_fileInfoList.at(index);
        if (fileInfo.isDir()) {
            QMutexLocker locker(&m_mutex);
            m_processingIndices.remove(index);
            continue;
        }
        const QString filePath = fileInfo.absoluteFilePath();
        const QString suffix = fileInfo.suffix().toLower();
        const bool isAvif = (suffix == "avif");

        // 统一缩略图保存路径为缓存目录下的hash文件名
        QString thumbPath = thumbPathForFile(filePath);

        // 检查是否已经有有效的缩略图或正在生成中，避免重复生成
        if (QFile::exists(thumbPath + ".tmp")) {
            // 如果临时文件存在，说明正在生成中，跳过
            QMetaObject::invokeMethod(this, [this, index]() {
                {
                    QMutexLocker locker(&m_mutex);
                    m_processingIndices.remove(index);
                }
            }, Qt::QueuedConnection);
            continue;
        }

        if (QFile::exists(thumbPath)) {
            // 验证现有缩略图是否有效
            QImage testImage(thumbPath);
            if (!testImage.isNull()) {
                // 缩略图有效，跳过生成
                QMetaObject::invokeMethod(this, [this, index]() {
                    {
                        QMutexLocker locker(&m_mutex);
                        m_processingIndices.remove(index);
                    }
                    emit thumbnailReady(index);
                }, Qt::QueuedConnection);
                continue;
            } else {
                // 缩略图损坏，删除并重新生成
                qWarning() << "检测到损坏的缩略图文件，将重新生成:" << thumbPath;
                QFile::remove(thumbPath);
            }
        }

        // 使用适当的方法生成缩略图
        QImage thumbnail = generateThumbnail(filePath);
        bool saveSuccess = !thumbnail.isNull();

        // generateThumbnail现在已经直接保存缩略图
        if (thumbnail.isNull()) {
            qWarning() << "缩略图生成失败，源文件:" << filePath;
        }

        // 将图像添加到内存缓存，以便快速访问
        if (saveSuccess) {
            QMutexLocker locker(&m_mutex);
            m_thumbnailCache.insert(filePath, new QImage(thumbnail));
        }

        // 通知UI更新
        QMetaObject::invokeMethod(this, [this, index, saveSuccess, filePath, thumbPath]() {
            {
                QMutexLocker locker(&m_mutex);
                m_processingIndices.remove(index);
            }
            if (saveSuccess) {
                emit thumbnailReady(index);
            } else {
                qDebug() << "缩略图保存失败:" << thumbPath << "(源文件:" << filePath << ")";
                emit thumbnailReady(index);
            }
        }, Qt::QueuedConnection);
        // 单线程，处理下一个任务
    }
}

// 解码图像并保存到临时文件以便QML使用
Q_INVOKABLE QString ImageProcessor::getImageUrl(int index) const
{
    if (index < 0 || index >= m_fileInfoList.count() || m_fileInfoList.at(index).isDir()) return QString();

    const QString filePath = m_fileInfoList.at(index).absoluteFilePath();

    // 检查文件是否存在
    QFileInfo fileInfo(filePath);
    if (!fileInfo.exists() || !fileInfo.isReadable()) return QString();

    // 对于所有文件，直接返回文件路径
    // 注意：预览窗口应该使用getPreviewImage来获取AVIF图像
    return "file:///" + QDir::fromNativeSeparators(filePath);
}

// 专门用于AVIF文件预览，返回URL
Q_INVOKABLE QString ImageProcessor::getAvifPreviewUrl(int index) const
{
    if (index < 0 || index >= m_fileInfoList.count() || m_fileInfoList.at(index).isDir()) {
        return QString();
    }

    const QString filePath = m_fileInfoList.at(index).absoluteFilePath();

    // 检查文件是否存在
    QFileInfo fileInfo(filePath);
    if (!fileInfo.exists() || !fileInfo.isReadable()) return QString();

    // 检查是否为AVIF文件
    const QString suffix = fileInfo.suffix().toLower();
    const bool isAvif = (suffix == "avif");

    // 如果不是AVIF文件，直接返回原始路径
    if (!isAvif) return "file:///" + QDir::fromNativeSeparators(filePath);

    try {
        // 使用文件哈希作为临时文件名
        QString thumbPath = thumbPathForFile(filePath);

        // 如果临时文件已存在且较新，直接返回
        QFileInfo tempInfo(thumbPath);
        if (tempInfo.exists() && tempInfo.lastModified() > fileInfo.lastModified()) return "file:///" + QDir::fromNativeSeparators(thumbPath);

        // 确保缓存目录存在
        ensureThumbCacheDir();

        // 尝试使用FFmpeg命令行工具
        QString ffmpegPath = QCoreApplication::applicationDirPath() + "/ffmpeg.exe";
        if (!QFileInfo::exists(ffmpegPath)) return QString(); // 如果找不到ffmpeg，返回空字符串

        QProcess ffmpegProcess;
        QStringList arguments;
        arguments << "-y" // 覆盖输出文件
                 << "-i" << filePath
                 << "-qscale:v" << "1" // 最高质量
                 << thumbPath;

        ffmpegProcess.start(ffmpegPath, arguments);
        if (ffmpegProcess.waitForFinished(30000)) {
            if (ffmpegProcess.exitCode() == 0) return "file:///" + QDir::fromNativeSeparators(thumbPath);
        }
        // 所有方法都失败，返回原始文件路径
        qWarning() << "所有AVIF转换方法都失败，返回原始路径";
        return "file:///" + QDir::fromNativeSeparators(filePath);
    } catch (const std::exception& e) {
        qWarning() << "处理AVIF文件时发生异常:" << e.what();
        return "file:///" + QDir::fromNativeSeparators(filePath);
    } catch (...) {
        qWarning() << "处理AVIF文件时发生未知异常";
        return "file:///" + QDir::fromNativeSeparators(filePath);
    }
}


// 专门用于AVIF文件预览，直接返回QImage，完全在内存中操作
Q_INVOKABLE QString ImageProcessor::getAvifFullImageUrl(int index) const
{
    if (index < 0 || index >= m_fileInfoList.count() || m_fileInfoList.at(index).isDir()) {
        return QString();
    }

    const QString filePath = m_fileInfoList.at(index).absoluteFilePath();
    QFileInfo fileInfo(filePath);
    if (!fileInfo.exists() || !fileInfo.isReadable()) return QString();

    const QString suffix = fileInfo.suffix().toLower();
    if (suffix != "avif") return "file:///" + QDir::fromNativeSeparators(filePath);// 非AVIF文件直接返回原始路径

    try {
        // 为原图创建不同的缓存路径（添加_full后缀）
        QString fileHash = QCryptographicHash::hash(filePath.toUtf8(), QCryptographicHash::Md5).toHex();
        QString fullImagePath = m_thumbCachePath + "/" + fileHash + "_full.jpg";

        // 如果原图缓存已存在且较新，直接返回
        QFileInfo fullImageInfo(fullImagePath);
        if (fullImageInfo.exists() && fullImageInfo.lastModified() > fileInfo.lastModified())
            return "file:///" + QDir::fromNativeSeparators(fullImagePath);

        // 确保缓存目录存在
        ensureThumbCacheDir();

        // 尝试使用avifdec命令行工具（优先选择）
        QString avifdecPath = QCoreApplication::applicationDirPath() + "/avifdec.exe";
        if (QFileInfo::exists(avifdecPath)) {
            QProcess avifdecProcess;
            QStringList arguments;
            arguments << filePath << fullImagePath;

            avifdecProcess.start(avifdecPath, arguments);
            if (avifdecProcess.waitForFinished(30000)) {
                if (avifdecProcess.exitCode() == 0)
                    return "file:///" + QDir::fromNativeSeparators(fullImagePath);
            }
        }

        // 备用方案：使用FFmpeg命令行工具
        QString ffmpegPath = QCoreApplication::applicationDirPath() + "/ffmpeg.exe";
        if (QFileInfo::exists(ffmpegPath)) {
            QProcess ffmpegProcess;
            QStringList arguments;
            arguments << "-y" // 覆盖输出文件
                     << "-i" << filePath
                     << "-qscale:v" << "1" // 最高质量
                     << fullImagePath;

            ffmpegProcess.start(ffmpegPath, arguments);
            if (ffmpegProcess.waitForFinished(30000)) {
                if (ffmpegProcess.exitCode() == 0) {
                    qDebug() << "使用FFmpeg成功生成AVIF原图:" << fullImagePath;
                    return "file:///" + QDir::fromNativeSeparators(fullImagePath);
                }
            }
        }
        qWarning() << "所有AVIF原图转换方法都失败，返回原始路径:" << filePath;
        return "file:///" + QDir::fromNativeSeparators(filePath);
    } catch (const std::exception& e) {
        qWarning() << "处理AVIF原图时发生异常:" << e.what();
        return "file:///" + QDir::fromNativeSeparators(filePath);
    } catch (...) {
        qWarning() << "处理AVIF原图时发生未知异常";
        return "file:///" + QDir::fromNativeSeparators(filePath);
    }
}

Q_INVOKABLE QImage ImageProcessor::getAvifImage(int index) const
{
    if (index < 0 || index >= m_fileInfoList.count() || m_fileInfoList.at(index).isDir()) return QImage();
    const QString filePath = m_fileInfoList.at(index).absoluteFilePath();
    QFileInfo fileInfo(filePath);
    if (!fileInfo.exists() || !fileInfo.isReadable()) return QImage();
    const QString suffix = fileInfo.suffix().toLower();
    if (suffix != "avif") return QImage();

    // 尝试使用QImageReader解码AVIF文件
    QImageReader reader(filePath);
    reader.setAutoTransform(true);
    reader.setDecideFormatFromContent(true);
    reader.setAutoDetectImageFormat(true);
    QImage image = reader.read();

    if (image.isNull()) qWarning() << "AVIF解码失败:" << filePath << reader.errorString();

    return image;
}

// 将QImage转换为QML可用的图像URL
Q_INVOKABLE QUrl ImageProcessor::imageToUrl(const QImage &image) const
{
    // AvifImageProvider 已不支持内存图像，直接返回空 QUrl 或警告
    return QUrl();
}

// 网络服务方法实现
QJsonObject ImageProcessor::getNetworkImageList(const QString &folderPath) {
    QJsonObject result;
    QJsonArray images;

    // 临时设置路径并加载（包含文件夹以支持浏览）
    QString originalPath = m_currentFolder;
    bool originalIncludeFolders = m_includeFolders;
    if (folderPath != m_currentFolder) {
        loadImagesFromFolder(folderPath, true); // 包含文件夹
    }

    // 构建图片列表JSON
    for (int i = 0; i < m_fileInfoList.count(); ++i) {
        const QFileInfo &fileInfo = m_fileInfoList.at(i);

        QJsonObject imageObj;
        imageObj["name"] = fileInfo.fileName();
        imageObj["path"] = fileInfo.absoluteFilePath();
        imageObj["size"] = fileInfo.size();
        imageObj["isFolder"] = fileInfo.isDir();
        imageObj["lastModified"] = fileInfo.lastModified().toString(Qt::ISODate);

        if (!fileInfo.isDir()) {
            imageObj["extension"] = fileInfo.suffix().toLower();
            // 检查缩略图是否存在
            QString thumbPath = thumbPathForFile(fileInfo.absoluteFilePath());
            imageObj["hasThumbnail"] = QFile::exists(thumbPath);
        }

        images.append(imageObj);
    }

    result["images"] = images;
    result["totalCount"] = images.size();
    result["currentPath"] = folderPath;
    result["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);

    // 恢复原路径和设置
    if (folderPath != originalPath && !originalPath.isEmpty()) {
        loadImagesFromFolder(originalPath, originalIncludeFolders);
    }

    return result;
}

QByteArray ImageProcessor::getNetworkThumbnail(const QString &imagePath) {
    if (!canServeNetworkRequest()) {
        return QByteArray();
    }

    // 检查缩略图缓存
    QString thumbPath = thumbPathForFile(imagePath);
    if (QFile::exists(thumbPath)) {
        QFile file(thumbPath);
        if (file.open(QIODevice::ReadOnly)) {
            return file.readAll();
        }
    }

    // 生成缩略图
    QImage thumbnail = generateThumbnail(imagePath);
    if (!thumbnail.isNull()) {
        QByteArray data;
        QBuffer buffer(&data);
        buffer.open(QIODevice::WriteOnly);

        if (thumbnail.save(&buffer, "JPEG", 85)) {
            // 异步保存到缓存
            QFuture<void> future = QtConcurrent::run([thumbPath, data]() -> void {
                QFile file(thumbPath);
                if (file.open(QIODevice::WriteOnly)) {
                    file.write(data);
                }
            });
            Q_UNUSED(future)

            return data;
        }
    }

    return QByteArray();
}

QByteArray ImageProcessor::getNetworkFullImage(const QString &imagePath) {
    if (!canServeNetworkRequest()) {
        return QByteArray();
    }

    QFile file(imagePath);
    if (!file.open(QIODevice::ReadOnly)) {
        return QByteArray();
    }

    return file.readAll();
}

bool ImageProcessor::canServeNetworkRequest(qint64 estimatedSize) {
    Q_UNUSED(estimatedSize)
    // 简单的并发控制，避免过载
    return !m_processing;
}
