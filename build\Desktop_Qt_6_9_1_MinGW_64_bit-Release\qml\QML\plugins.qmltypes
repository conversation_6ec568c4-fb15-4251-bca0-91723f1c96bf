import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qqmllocale_p.h"
        name: "QList<QQmlLocale::DayOfWeek>"
        accessSemantics: "sequence"
        valueType: "QQmlLocale::DayOfWeek"
    }
    Component {
        file: "private/qv4sequenceobject_p.h"
        name: "std::vector<bool>"
        accessSemantics: "sequence"
        valueType: "bool"
    }
    Component {
        file: "private/qv4sequenceobject_p.h"
        name: "std::vector<double>"
        accessSemantics: "sequence"
        valueType: "double"
    }
    Component {
        file: "private/qv4sequenceobject_p.h"
        name: "std::vector<float>"
        accessSemantics: "sequence"
        valueType: "float"
    }
    Component {
        file: "private/qv4sequenceobject_p.h"
        name: "std::vector<int>"
        accessSemantics: "sequence"
        valueType: "int"
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "QAnyStringView"
        accessSemantics: "value"
        extension: "String"
        extensionIsJavaScript: true
    }
    Component {
        file: "private/qqmlglobal_p.h"
        name: "QQmlApplication"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "arguments"
            type: "QStringList"
            read: "args"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "name"
            type: "QString"
            read: "name"
            write: "setName"
            notify: "nameChanged"
            index: 1
        }
        Property {
            name: "version"
            type: "QString"
            read: "version"
            write: "setVersion"
            notify: "versionChanged"
            index: 2
        }
        Property {
            name: "organization"
            type: "QString"
            read: "organization"
            write: "setOrganization"
            notify: "organizationChanged"
            index: 3
        }
        Property {
            name: "domain"
            type: "QString"
            read: "domain"
            write: "setDomain"
            notify: "domainChanged"
            index: 4
        }
        Signal { name: "aboutToQuit" }
        Signal { name: "nameChanged" }
        Signal { name: "versionChanged" }
        Signal { name: "organizationChanged" }
        Signal { name: "domainChanged" }
        Method {
            name: "setName"
            Parameter { name: "arg"; type: "QString" }
        }
        Method {
            name: "setVersion"
            Parameter { name: "arg"; type: "QString" }
        }
        Method {
            name: "setOrganization"
            Parameter { name: "arg"; type: "QString" }
        }
        Method {
            name: "setDomain"
            Parameter { name: "arg"; type: "QString" }
        }
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "bool"
        accessSemantics: "value"
        extension: "Boolean"
        extensionIsJavaScript: true
        exports: ["QML/bool 1.0"]
        isCreatable: false
        exportMetaObjectRevisions: [256]
    }
    Component {
        file: "private/qqmlcomponentattached_p.h"
        name: "QQmlComponentAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Signal { name: "completed" }
        Signal { name: "destruction" }
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "QQmlComponent"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QML/Component 1.0"]
        exportMetaObjectRevisions: [256]
        attachedType: "QQmlComponentAttached"
        Enum {
            name: "CompilationMode"
            values: ["PreferSynchronous", "Asynchronous"]
        }
        Enum {
            name: "Status"
            values: ["Null", "Ready", "Loading", "Error"]
        }
        Property {
            name: "progress"
            type: "double"
            read: "progress"
            notify: "progressChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "status"
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "url"
            type: "QUrl"
            read: "url"
            index: 2
            isReadonly: true
            isPropertyConstant: true
        }
        Signal {
            name: "statusChanged"
            Parameter { type: "QQmlComponent::Status" }
        }
        Signal {
            name: "progressChanged"
            Parameter { type: "double" }
        }
        Method {
            name: "loadUrl"
            Parameter { name: "url"; type: "QUrl" }
        }
        Method {
            name: "loadUrl"
            Parameter { name: "url"; type: "QUrl" }
            Parameter { name: "mode"; type: "CompilationMode" }
        }
        Method {
            name: "loadFromModule"
            Parameter { name: "uri"; type: "QAnyStringView" }
            Parameter { name: "typeName"; type: "QAnyStringView" }
            Parameter { name: "mode"; type: "QQmlComponent::CompilationMode" }
        }
        Method {
            name: "loadFromModule"
            isCloned: true
            Parameter { name: "uri"; type: "QAnyStringView" }
            Parameter { name: "typeName"; type: "QAnyStringView" }
        }
        Method {
            name: "setData"
            Parameter { type: "QByteArray" }
            Parameter { name: "baseUrl"; type: "QUrl" }
        }
        Method { name: "errorString"; type: "QString"; isMethodConstant: true }
        Method { name: "createObject"; isJavaScriptFunction: true }
        Method {
            name: "createObject"
            type: "QObject"
            isPointer: true
            Parameter { name: "parent"; type: "QObject"; isPointer: true }
            Parameter { name: "properties"; type: "QVariantMap" }
        }
        Method {
            name: "createObject"
            type: "QObject"
            isPointer: true
            isCloned: true
            Parameter { name: "parent"; type: "QObject"; isPointer: true }
        }
        Method { name: "createObject"; type: "QObject"; isPointer: true; isCloned: true }
        Method { name: "incubateObject"; isJavaScriptFunction: true }
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "QDateTime"
        accessSemantics: "value"
        extension: "Date"
        extensionIsJavaScript: true
        exports: ["QML/date 1.0"]
        isCreatable: false
        exportMetaObjectRevisions: [256]
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "double"
        accessSemantics: "value"
        extension: "Number"
        extensionIsJavaScript: true
        exports: ["QML/real 1.0", "QML/double 1.0"]
        isCreatable: false
        exportMetaObjectRevisions: [256]
    }
    Component {
        file: "private/qqmlvaluetype_p.h"
        name: "QQmlEasingEnums"
        accessSemantics: "none"
        exports: ["QML/Easing 1.0"]
        isCreatable: false
        exportMetaObjectRevisions: [256]
        Enum {
            name: "Type"
            values: [
                "Linear",
                "InQuad",
                "OutQuad",
                "InOutQuad",
                "OutInQuad",
                "InCubic",
                "OutCubic",
                "InOutCubic",
                "OutInCubic",
                "InQuart",
                "OutQuart",
                "InOutQuart",
                "OutInQuart",
                "InQuint",
                "OutQuint",
                "InOutQuint",
                "OutInQuint",
                "InSine",
                "OutSine",
                "InOutSine",
                "OutInSine",
                "InExpo",
                "OutExpo",
                "InOutExpo",
                "OutInExpo",
                "InCirc",
                "OutCirc",
                "InOutCirc",
                "OutInCirc",
                "InElastic",
                "OutElastic",
                "InOutElastic",
                "OutInElastic",
                "InBack",
                "OutBack",
                "InOutBack",
                "OutInBack",
                "InBounce",
                "OutBounce",
                "InOutBounce",
                "OutInBounce",
                "InCurve",
                "OutCurve",
                "SineCurve",
                "CosineCurve",
                "BezierSpline",
                "Bezier"
            ]
        }
    }
    Component {
        file: "private/qqmlvaluetype_p.h"
        name: "QEasingCurve"
        accessSemantics: "value"
        extension: "QQmlEasingValueType"
        Enum {
            name: "Type"
            values: [
                "Linear",
                "InQuad",
                "OutQuad",
                "InOutQuad",
                "OutInQuad",
                "InCubic",
                "OutCubic",
                "InOutCubic",
                "OutInCubic",
                "InQuart",
                "OutQuart",
                "InOutQuart",
                "OutInQuart",
                "InQuint",
                "OutQuint",
                "InOutQuint",
                "OutInQuint",
                "InSine",
                "OutSine",
                "InOutSine",
                "OutInSine",
                "InExpo",
                "OutExpo",
                "InOutExpo",
                "OutInExpo",
                "InCirc",
                "OutCirc",
                "InOutCirc",
                "OutInCirc",
                "InElastic",
                "OutElastic",
                "InOutElastic",
                "OutInElastic",
                "InBack",
                "OutBack",
                "InOutBack",
                "OutInBack",
                "InBounce",
                "OutBounce",
                "InOutBounce",
                "OutInBounce",
                "InCurve",
                "OutCurve",
                "SineCurve",
                "CosineCurve",
                "BezierSpline",
                "TCBSpline",
                "Custom",
                "NCurveTypes"
            ]
        }
    }
    Component {
        file: "private/qqmlvaluetype_p.h"
        name: "QQmlEasingValueType"
        accessSemantics: "value"
        prototype: "QEasingCurve"
        Property {
            name: "type"
            type: "QQmlEasingEnums::Type"
            read: "type"
            write: "setType"
            index: 0
            isFinal: true
        }
        Property {
            name: "amplitude"
            type: "double"
            read: "amplitude"
            write: "setAmplitude"
            index: 1
            isFinal: true
        }
        Property {
            name: "overshoot"
            type: "double"
            read: "overshoot"
            write: "setOvershoot"
            index: 2
            isFinal: true
        }
        Property {
            name: "period"
            type: "double"
            read: "period"
            write: "setPeriod"
            index: 3
            isFinal: true
        }
        Property {
            name: "bezierCurve"
            type: "double"
            isList: true
            read: "bezierCurve"
            write: "setBezierCurve"
            index: 4
            isFinal: true
        }
        Method { name: "QQmlEasingValueType"; isConstructor: true }
        Method {
            name: "QQmlEasingValueType"
            isConstructor: true
            Parameter { name: "easing"; type: "QEasingCurve" }
        }
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "float"
        accessSemantics: "value"
        extension: "Number"
        extensionIsJavaScript: true
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "int"
        accessSemantics: "value"
        extension: "Number"
        extensionIsJavaScript: true
        exports: ["QML/int 1.0"]
        isCreatable: false
        exportMetaObjectRevisions: [256]
    }
    Component {
        file: "private/qqmllocale_p.h"
        name: "QQmlLocale"
        accessSemantics: "value"
        prototype: "QLocale"
        Enum {
            name: "DayOfWeek"
            values: [
                "Sunday",
                "Monday",
                "Tuesday",
                "Wednesday",
                "Thursday",
                "Friday",
                "Saturday"
            ]
        }
    }
    Component {
        file: "private/qqmllocale_p.h"
        name: "QLocale"
        accessSemantics: "value"
        extension: "QQmlLocaleValueType"
        Enum {
            name: "Language"
            type: "ushort"
            values: [
                "AnyLanguage",
                "C",
                "Abkhazian",
                "Afar",
                "Afrikaans",
                "Aghem",
                "Akan",
                "Akkadian",
                "Akoose",
                "Albanian",
                "AmericanSignLanguage",
                "Amharic",
                "AncientEgyptian",
                "AncientGreek",
                "Arabic",
                "Aragonese",
                "Aramaic",
                "Armenian",
                "Assamese",
                "Asturian",
                "Asu",
                "Atsam",
                "Avaric",
                "Avestan",
                "Aymara",
                "Azerbaijani",
                "Bafia",
                "Balinese",
                "Bambara",
                "Bamun",
                "Bangla",
                "Basaa",
                "Bashkir",
                "Basque",
                "BatakToba",
                "Belarusian",
                "Bemba",
                "Bena",
                "Bhojpuri",
                "Bislama",
                "Blin",
                "Bodo",
                "Bosnian",
                "Breton",
                "Buginese",
                "Bulgarian",
                "Burmese",
                "Cantonese",
                "Catalan",
                "Cebuano",
                "CentralAtlasTamazight",
                "CentralKurdish",
                "Chakma",
                "Chamorro",
                "Chechen",
                "Cherokee",
                "Chickasaw",
                "Chiga",
                "Chinese",
                "Church",
                "Chuvash",
                "Colognian",
                "Coptic",
                "Cornish",
                "Corsican",
                "Cree",
                "Croatian",
                "Czech",
                "Danish",
                "Divehi",
                "Dogri",
                "Duala",
                "Dutch",
                "Dzongkha",
                "Embu",
                "English",
                "Erzya",
                "Esperanto",
                "Estonian",
                "Ewe",
                "Ewondo",
                "Faroese",
                "Fijian",
                "Filipino",
                "Finnish",
                "French",
                "Friulian",
                "Fulah",
                "Gaelic",
                "Ga",
                "Galician",
                "Ganda",
                "Geez",
                "Georgian",
                "German",
                "Gothic",
                "Greek",
                "Guarani",
                "Gujarati",
                "Gusii",
                "Haitian",
                "Hausa",
                "Hawaiian",
                "Hebrew",
                "Herero",
                "Hindi",
                "HiriMotu",
                "Hungarian",
                "Icelandic",
                "Ido",
                "Igbo",
                "InariSami",
                "Indonesian",
                "Ingush",
                "Interlingua",
                "Interlingue",
                "Inuktitut",
                "Inupiaq",
                "Irish",
                "Italian",
                "Japanese",
                "Javanese",
                "Jju",
                "JolaFonyi",
                "Kabuverdianu",
                "Kabyle",
                "Kako",
                "Kalaallisut",
                "Kalenjin",
                "Kamba",
                "Kannada",
                "Kanuri",
                "Kashmiri",
                "Kazakh",
                "Kenyang",
                "Khmer",
                "Kiche",
                "Kikuyu",
                "Kinyarwanda",
                "Komi",
                "Kongo",
                "Konkani",
                "Korean",
                "Koro",
                "KoyraboroSenni",
                "KoyraChiini",
                "Kpelle",
                "Kuanyama",
                "Kurdish",
                "Kwasio",
                "Kyrgyz",
                "Lakota",
                "Langi",
                "Lao",
                "Latin",
                "Latvian",
                "Lezghian",
                "Limburgish",
                "Lingala",
                "LiteraryChinese",
                "Lithuanian",
                "Lojban",
                "LowerSorbian",
                "LowGerman",
                "LubaKatanga",
                "LuleSami",
                "Luo",
                "Luxembourgish",
                "Luyia",
                "Macedonian",
                "Machame",
                "Maithili",
                "MakhuwaMeetto",
                "Makonde",
                "Malagasy",
                "Malayalam",
                "Malay",
                "Maltese",
                "Mandingo",
                "Manipuri",
                "Manx",
                "Maori",
                "Mapuche",
                "Marathi",
                "Marshallese",
                "Masai",
                "Mazanderani",
                "Mende",
                "Meru",
                "Meta",
                "Mohawk",
                "Mongolian",
                "Morisyen",
                "Mundang",
                "Muscogee",
                "Nama",
                "NauruLanguage",
                "Navajo",
                "Ndonga",
                "Nepali",
                "Newari",
                "Ngiemboon",
                "Ngomba",
                "NigerianPidgin",
                "Nko",
                "NorthernLuri",
                "NorthernSami",
                "NorthernSotho",
                "NorthNdebele",
                "NorwegianBokmal",
                "NorwegianNynorsk",
                "Nuer",
                "Nyanja",
                "Nyankole",
                "Occitan",
                "Odia",
                "Ojibwa",
                "OldIrish",
                "OldNorse",
                "OldPersian",
                "Oromo",
                "Osage",
                "Ossetic",
                "Pahlavi",
                "Palauan",
                "Pali",
                "Papiamento",
                "Pashto",
                "Persian",
                "Phoenician",
                "Polish",
                "Portuguese",
                "Prussian",
                "Punjabi",
                "Quechua",
                "Romanian",
                "Romansh",
                "Rombo",
                "Rundi",
                "Russian",
                "Rwa",
                "Saho",
                "Sakha",
                "Samburu",
                "Samoan",
                "Sango",
                "Sangu",
                "Sanskrit",
                "Santali",
                "Sardinian",
                "Saurashtra",
                "Sena",
                "Serbian",
                "Shambala",
                "Shona",
                "SichuanYi",
                "Sicilian",
                "Sidamo",
                "Silesian",
                "Sindhi",
                "Sinhala",
                "SkoltSami",
                "Slovak",
                "Slovenian",
                "Soga",
                "Somali",
                "SouthernKurdish",
                "SouthernSami",
                "SouthernSotho",
                "SouthNdebele",
                "Spanish",
                "StandardMoroccanTamazight",
                "Sundanese",
                "Swahili",
                "Swati",
                "Swedish",
                "SwissGerman",
                "Syriac",
                "Tachelhit",
                "Tahitian",
                "TaiDam",
                "Taita",
                "Tajik",
                "Tamil",
                "Taroko",
                "Tasawaq",
                "Tatar",
                "Telugu",
                "Teso",
                "Thai",
                "Tibetan",
                "Tigre",
                "Tigrinya",
                "TokelauLanguage",
                "TokPisin",
                "Tongan",
                "Tsonga",
                "Tswana",
                "Turkish",
                "Turkmen",
                "TuvaluLanguage",
                "Tyap",
                "Ugaritic",
                "Ukrainian",
                "UpperSorbian",
                "Urdu",
                "Uyghur",
                "Uzbek",
                "Vai",
                "Venda",
                "Vietnamese",
                "Volapuk",
                "Vunjo",
                "Walloon",
                "Walser",
                "Warlpiri",
                "Welsh",
                "WesternBalochi",
                "WesternFrisian",
                "Wolaytta",
                "Wolof",
                "Xhosa",
                "Yangben",
                "Yiddish",
                "Yoruba",
                "Zarma",
                "Zhuang",
                "Zulu",
                "Kaingang",
                "Nheengatu",
                "Haryanvi",
                "NorthernFrisian",
                "Rajasthani",
                "Moksha",
                "TokiPona",
                "Pijin",
                "Obolo",
                "Baluchi",
                "Ligurian",
                "Rohingya",
                "Torwali",
                "Anii",
                "Kangri",
                "Venetian",
                "Kuvi",
                "KaraKalpak",
                "SwampyCree",
                "Afan",
                "Bengali",
                "Bhutani",
                "Byelorussian",
                "Cambodian",
                "CentralMoroccoTamazight",
                "Chewa",
                "Frisian",
                "Greenlandic",
                "Inupiak",
                "Kirghiz",
                "Kurundi",
                "Kwanyama",
                "Navaho",
                "Oriya",
                "RhaetoRomance",
                "Uighur",
                "Uigur",
                "Walamo",
                "LastLanguage"
            ]
        }
        Enum {
            name: "Script"
            type: "ushort"
            values: [
                "AnyScript",
                "AdlamScript",
                "AhomScript",
                "AnatolianHieroglyphsScript",
                "ArabicScript",
                "ArmenianScript",
                "AvestanScript",
                "BalineseScript",
                "BamumScript",
                "BanglaScript",
                "BassaVahScript",
                "BatakScript",
                "BhaiksukiScript",
                "BopomofoScript",
                "BrahmiScript",
                "BrailleScript",
                "BugineseScript",
                "BuhidScript",
                "CanadianAboriginalScript",
                "CarianScript",
                "CaucasianAlbanianScript",
                "ChakmaScript",
                "ChamScript",
                "CherokeeScript",
                "CopticScript",
                "CuneiformScript",
                "CypriotScript",
                "CyrillicScript",
                "DeseretScript",
                "DevanagariScript",
                "DuployanScript",
                "EgyptianHieroglyphsScript",
                "ElbasanScript",
                "EthiopicScript",
                "FraserScript",
                "GeorgianScript",
                "GlagoliticScript",
                "GothicScript",
                "GranthaScript",
                "GreekScript",
                "GujaratiScript",
                "GurmukhiScript",
                "HangulScript",
                "HanScript",
                "HanunooScript",
                "HanWithBopomofoScript",
                "HatranScript",
                "HebrewScript",
                "HiraganaScript",
                "ImperialAramaicScript",
                "InscriptionalPahlaviScript",
                "InscriptionalParthianScript",
                "JamoScript",
                "JapaneseScript",
                "JavaneseScript",
                "KaithiScript",
                "KannadaScript",
                "KatakanaScript",
                "KayahLiScript",
                "KharoshthiScript",
                "KhmerScript",
                "KhojkiScript",
                "KhudawadiScript",
                "KoreanScript",
                "LannaScript",
                "LaoScript",
                "LatinScript",
                "LepchaScript",
                "LimbuScript",
                "LinearAScript",
                "LinearBScript",
                "LycianScript",
                "LydianScript",
                "MahajaniScript",
                "MalayalamScript",
                "MandaeanScript",
                "ManichaeanScript",
                "MarchenScript",
                "MeiteiMayekScript",
                "MendeScript",
                "MeroiticCursiveScript",
                "MeroiticScript",
                "ModiScript",
                "MongolianScript",
                "MroScript",
                "MultaniScript",
                "MyanmarScript",
                "NabataeanScript",
                "NewaScript",
                "NewTaiLueScript",
                "NkoScript",
                "OdiaScript",
                "OghamScript",
                "OlChikiScript",
                "OldHungarianScript",
                "OldItalicScript",
                "OldNorthArabianScript",
                "OldPermicScript",
                "OldPersianScript",
                "OldSouthArabianScript",
                "OrkhonScript",
                "OsageScript",
                "OsmanyaScript",
                "PahawhHmongScript",
                "PalmyreneScript",
                "PauCinHauScript",
                "PhagsPaScript",
                "PhoenicianScript",
                "PollardPhoneticScript",
                "PsalterPahlaviScript",
                "RejangScript",
                "RunicScript",
                "SamaritanScript",
                "SaurashtraScript",
                "SharadaScript",
                "ShavianScript",
                "SiddhamScript",
                "SignWritingScript",
                "SimplifiedHanScript",
                "SinhalaScript",
                "SoraSompengScript",
                "SundaneseScript",
                "SylotiNagriScript",
                "SyriacScript",
                "TagalogScript",
                "TagbanwaScript",
                "TaiLeScript",
                "TaiVietScript",
                "TakriScript",
                "TamilScript",
                "TangutScript",
                "TeluguScript",
                "ThaanaScript",
                "ThaiScript",
                "TibetanScript",
                "TifinaghScript",
                "TirhutaScript",
                "TraditionalHanScript",
                "UgariticScript",
                "VaiScript",
                "VarangKshitiScript",
                "YiScript",
                "HanifiScript",
                "BengaliScript",
                "MendeKikakuiScript",
                "OriyaScript",
                "SimplifiedChineseScript",
                "TraditionalChineseScript",
                "LastScript"
            ]
        }
        Enum {
            name: "Country"
            type: "ushort"
            values: [
                "AnyTerritory",
                "Afghanistan",
                "AlandIslands",
                "Albania",
                "Algeria",
                "AmericanSamoa",
                "Andorra",
                "Angola",
                "Anguilla",
                "Antarctica",
                "AntiguaAndBarbuda",
                "Argentina",
                "Armenia",
                "Aruba",
                "AscensionIsland",
                "Australia",
                "Austria",
                "Azerbaijan",
                "Bahamas",
                "Bahrain",
                "Bangladesh",
                "Barbados",
                "Belarus",
                "Belgium",
                "Belize",
                "Benin",
                "Bermuda",
                "Bhutan",
                "Bolivia",
                "BosniaAndHerzegovina",
                "Botswana",
                "BouvetIsland",
                "Brazil",
                "BritishIndianOceanTerritory",
                "BritishVirginIslands",
                "Brunei",
                "Bulgaria",
                "BurkinaFaso",
                "Burundi",
                "Cambodia",
                "Cameroon",
                "Canada",
                "CanaryIslands",
                "CapeVerde",
                "CaribbeanNetherlands",
                "CaymanIslands",
                "CentralAfricanRepublic",
                "CeutaAndMelilla",
                "Chad",
                "Chile",
                "China",
                "ChristmasIsland",
                "ClippertonIsland",
                "CocosIslands",
                "Colombia",
                "Comoros",
                "CongoBrazzaville",
                "CongoKinshasa",
                "CookIslands",
                "CostaRica",
                "Croatia",
                "Cuba",
                "Curacao",
                "Cyprus",
                "Czechia",
                "Denmark",
                "DiegoGarcia",
                "Djibouti",
                "Dominica",
                "DominicanRepublic",
                "Ecuador",
                "Egypt",
                "ElSalvador",
                "EquatorialGuinea",
                "Eritrea",
                "Estonia",
                "Eswatini",
                "Ethiopia",
                "Europe",
                "EuropeanUnion",
                "FalklandIslands",
                "FaroeIslands",
                "Fiji",
                "Finland",
                "France",
                "FrenchGuiana",
                "FrenchPolynesia",
                "FrenchSouthernTerritories",
                "Gabon",
                "Gambia",
                "Georgia",
                "Germany",
                "Ghana",
                "Gibraltar",
                "Greece",
                "Greenland",
                "Grenada",
                "Guadeloupe",
                "Guam",
                "Guatemala",
                "Guernsey",
                "GuineaBissau",
                "Guinea",
                "Guyana",
                "Haiti",
                "HeardAndMcDonaldIslands",
                "Honduras",
                "HongKong",
                "Hungary",
                "Iceland",
                "India",
                "Indonesia",
                "Iran",
                "Iraq",
                "Ireland",
                "IsleOfMan",
                "Israel",
                "Italy",
                "IvoryCoast",
                "Jamaica",
                "Japan",
                "Jersey",
                "Jordan",
                "Kazakhstan",
                "Kenya",
                "Kiribati",
                "Kosovo",
                "Kuwait",
                "Kyrgyzstan",
                "Laos",
                "LatinAmerica",
                "Latvia",
                "Lebanon",
                "Lesotho",
                "Liberia",
                "Libya",
                "Liechtenstein",
                "Lithuania",
                "Luxembourg",
                "Macao",
                "Macedonia",
                "Madagascar",
                "Malawi",
                "Malaysia",
                "Maldives",
                "Mali",
                "Malta",
                "MarshallIslands",
                "Martinique",
                "Mauritania",
                "Mauritius",
                "Mayotte",
                "Mexico",
                "Micronesia",
                "Moldova",
                "Monaco",
                "Mongolia",
                "Montenegro",
                "Montserrat",
                "Morocco",
                "Mozambique",
                "Myanmar",
                "Namibia",
                "NauruTerritory",
                "Nepal",
                "Netherlands",
                "NewCaledonia",
                "NewZealand",
                "Nicaragua",
                "Nigeria",
                "Niger",
                "Niue",
                "NorfolkIsland",
                "NorthernMarianaIslands",
                "NorthKorea",
                "Norway",
                "Oman",
                "OutlyingOceania",
                "Pakistan",
                "Palau",
                "PalestinianTerritories",
                "Panama",
                "PapuaNewGuinea",
                "Paraguay",
                "Peru",
                "Philippines",
                "Pitcairn",
                "Poland",
                "Portugal",
                "PuertoRico",
                "Qatar",
                "Reunion",
                "Romania",
                "Russia",
                "Rwanda",
                "SaintBarthelemy",
                "SaintHelena",
                "SaintKittsAndNevis",
                "SaintLucia",
                "SaintMartin",
                "SaintPierreAndMiquelon",
                "SaintVincentAndGrenadines",
                "Samoa",
                "SanMarino",
                "SaoTomeAndPrincipe",
                "SaudiArabia",
                "Senegal",
                "Serbia",
                "Seychelles",
                "SierraLeone",
                "Singapore",
                "SintMaarten",
                "Slovakia",
                "Slovenia",
                "SolomonIslands",
                "Somalia",
                "SouthAfrica",
                "SouthGeorgiaAndSouthSandwichIslands",
                "SouthKorea",
                "SouthSudan",
                "Spain",
                "SriLanka",
                "Sudan",
                "Suriname",
                "SvalbardAndJanMayen",
                "Sweden",
                "Switzerland",
                "Syria",
                "Taiwan",
                "Tajikistan",
                "Tanzania",
                "Thailand",
                "TimorLeste",
                "Togo",
                "TokelauTerritory",
                "Tonga",
                "TrinidadAndTobago",
                "TristanDaCunha",
                "Tunisia",
                "Turkey",
                "Turkmenistan",
                "TurksAndCaicosIslands",
                "TuvaluTerritory",
                "Uganda",
                "Ukraine",
                "UnitedArabEmirates",
                "UnitedKingdom",
                "UnitedStatesOutlyingIslands",
                "UnitedStates",
                "UnitedStatesVirginIslands",
                "Uruguay",
                "Uzbekistan",
                "Vanuatu",
                "VaticanCity",
                "Venezuela",
                "Vietnam",
                "WallisAndFutuna",
                "WesternSahara",
                "World",
                "Yemen",
                "Zambia",
                "Zimbabwe",
                "AnyCountry",
                "Bonaire",
                "BosniaAndHerzegowina",
                "CuraSao",
                "CzechRepublic",
                "DemocraticRepublicOfCongo",
                "DemocraticRepublicOfKorea",
                "EastTimor",
                "LatinAmericaAndTheCaribbean",
                "Macau",
                "NauruCountry",
                "PeoplesRepublicOfCongo",
                "RepublicOfKorea",
                "RussianFederation",
                "SaintVincentAndTheGrenadines",
                "SouthGeorgiaAndTheSouthSandwichIslands",
                "SvalbardAndJanMayenIslands",
                "Swaziland",
                "SyrianArabRepublic",
                "TokelauCountry",
                "TuvaluCountry",
                "UnitedStatesMinorOutlyingIslands",
                "VaticanCityState",
                "WallisAndFutunaIslands",
                "LastTerritory",
                "LastCountry"
            ]
        }
        Enum {
            name: "MeasurementSystem"
            values: [
                "MetricSystem",
                "ImperialUSSystem",
                "ImperialUKSystem",
                "ImperialSystem"
            ]
        }
        Enum {
            name: "FormatType"
            values: ["LongFormat", "ShortFormat", "NarrowFormat"]
        }
        Enum {
            name: "NumberOptions"
            alias: "NumberOption"
            isFlag: true
            values: [
                "DefaultNumberOptions",
                "OmitGroupSeparator",
                "RejectGroupSeparator",
                "OmitLeadingZeroInExponent",
                "RejectLeadingZeroInExponent",
                "IncludeTrailingZeroesAfterDot",
                "RejectTrailingZeroesAfterDot"
            ]
        }
        Enum {
            name: "TagSeparator"
            isScoped: true
            type: "qint8"
            values: ["Dash", "Underscore"]
        }
        Enum {
            name: "CurrencySymbolFormat"
            values: [
                "CurrencyIsoCode",
                "CurrencySymbol",
                "CurrencyDisplayName"
            ]
        }
        Enum {
            name: "DataSizeFormats"
            alias: "DataSizeFormat"
            isFlag: true
            values: [
                "DataSizeBase1000",
                "DataSizeSIQuantifiers",
                "DataSizeIecFormat",
                "DataSizeTraditionalFormat",
                "DataSizeSIFormat"
            ]
        }
        Enum {
            name: "LanguageCodeTypes"
            alias: "LanguageCodeType"
            isFlag: true
            values: [
                "ISO639Part1",
                "ISO639Part2B",
                "ISO639Part2T",
                "ISO639Part3",
                "LegacyLanguageCode",
                "ISO639Part2",
                "ISO639Alpha2",
                "ISO639Alpha3",
                "ISO639",
                "AnyLanguageCode"
            ]
        }
        Enum {
            name: "QuotationStyle"
            values: ["StandardQuotation", "AlternateQuotation"]
        }
    }
    Component {
        file: "private/qqmllocale_p.h"
        name: "QQmlLocaleValueType"
        accessSemantics: "value"
        Property {
            name: "firstDayOfWeek"
            type: "QQmlLocale::DayOfWeek"
            read: "firstDayOfWeek"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "measurementSystem"
            type: "QLocale::MeasurementSystem"
            read: "measurementSystem"
            index: 1
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "textDirection"
            type: "Qt::LayoutDirection"
            read: "textDirection"
            index: 2
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "weekDays"
            type: "QQmlLocale::DayOfWeek"
            isList: true
            read: "weekDays"
            index: 3
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "uiLanguages"
            type: "QStringList"
            read: "uiLanguages"
            index: 4
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "name"
            type: "QString"
            read: "name"
            index: 5
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "nativeLanguageName"
            type: "QString"
            read: "nativeLanguageName"
            index: 6
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "nativeCountryName"
            type: "QString"
            read: "nativeCountryName"
            index: 7
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "nativeTerritoryName"
            type: "QString"
            read: "nativeTerritoryName"
            index: 8
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "decimalPoint"
            type: "QString"
            read: "decimalPoint"
            index: 9
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "groupSeparator"
            type: "QString"
            read: "groupSeparator"
            index: 10
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "percent"
            type: "QString"
            read: "percent"
            index: 11
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "zeroDigit"
            type: "QString"
            read: "zeroDigit"
            index: 12
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "negativeSign"
            type: "QString"
            read: "negativeSign"
            index: 13
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "positiveSign"
            type: "QString"
            read: "positiveSign"
            index: 14
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "exponential"
            type: "QString"
            read: "exponential"
            index: 15
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "amText"
            type: "QString"
            read: "amText"
            index: 16
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "pmText"
            type: "QString"
            read: "pmText"
            index: 17
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "numberOptions"
            type: "QLocale::NumberOptions"
            read: "numberOptions"
            write: "setNumberOptions"
            index: 18
        }
        Method {
            name: "currencySymbol"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "format"; type: "QLocale::CurrencySymbolFormat" }
        }
        Method { name: "currencySymbol"; type: "QString"; isCloned: true; isMethodConstant: true }
        Method {
            name: "dateTimeFormat"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "format"; type: "QLocale::FormatType" }
        }
        Method { name: "dateTimeFormat"; type: "QString"; isCloned: true; isMethodConstant: true }
        Method {
            name: "timeFormat"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "format"; type: "QLocale::FormatType" }
        }
        Method { name: "timeFormat"; type: "QString"; isCloned: true; isMethodConstant: true }
        Method {
            name: "dateFormat"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "format"; type: "QLocale::FormatType" }
        }
        Method { name: "dateFormat"; type: "QString"; isCloned: true; isMethodConstant: true }
        Method {
            name: "monthName"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "index"; type: "int" }
            Parameter { name: "format"; type: "QLocale::FormatType" }
        }
        Method {
            name: "monthName"
            type: "QString"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "standaloneMonthName"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "index"; type: "int" }
            Parameter { name: "format"; type: "QLocale::FormatType" }
        }
        Method {
            name: "standaloneMonthName"
            type: "QString"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "dayName"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "index"; type: "int" }
            Parameter { name: "format"; type: "QLocale::FormatType" }
        }
        Method {
            name: "dayName"
            type: "QString"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "standaloneDayName"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "index"; type: "int" }
            Parameter { name: "format"; type: "QLocale::FormatType" }
        }
        Method {
            name: "standaloneDayName"
            type: "QString"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "index"; type: "int" }
        }
        Method { name: "formattedDataSize"; isJavaScriptFunction: true; isMethodConstant: true }
        Method {
            name: "formattedDataSize"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "bytes"; type: "double" }
            Parameter { name: "precision"; type: "int" }
            Parameter { name: "format"; type: "QLocale::DataSizeFormats" }
        }
        Method {
            name: "formattedDataSize"
            type: "QString"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "bytes"; type: "double" }
            Parameter { name: "precision"; type: "int" }
        }
        Method {
            name: "formattedDataSize"
            type: "QString"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "bytes"; type: "double" }
        }
        Method { name: "toString"; isJavaScriptFunction: true; isMethodConstant: true }
        Method { name: "toString"; type: "QString"; isMethodConstant: true }
        Method {
            name: "toString"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "i"; type: "int" }
        }
        Method {
            name: "toString"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "f"; type: "double" }
        }
        Method {
            name: "toString"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "f"; type: "double" }
            Parameter { name: "format"; type: "QString" }
            Parameter { name: "precision"; type: "int" }
        }
        Method {
            name: "toString"
            type: "QString"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "f"; type: "double" }
            Parameter { name: "format"; type: "QString" }
        }
        Method {
            name: "toString"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "dateTime"; type: "QDateTime" }
            Parameter { name: "format"; type: "QString" }
        }
        Method {
            name: "toString"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "dateTime"; type: "QDateTime" }
            Parameter { name: "format"; type: "QLocale::FormatType" }
        }
        Method {
            name: "toString"
            type: "QString"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "dateTime"; type: "QDateTime" }
        }
        Method {
            name: "QQmlLocaleValueType"
            isConstructor: true
            Parameter { name: "name"; type: "QString" }
        }
    }
    Component {
        file: "private/qqmlloggingcategorybase_p.h"
        name: "QQmlLoggingCategoryBase"
        accessSemantics: "reference"
        prototype: "QObject"
    }
    Component {
        file: "private/qqmlvaluetype_p.h"
        name: "QMarginsF"
        accessSemantics: "value"
        extension: "QQmlMarginsFValueType"
    }
    Component {
        file: "private/qqmlvaluetype_p.h"
        name: "QQmlMarginsFValueType"
        accessSemantics: "value"
        prototype: "QMarginsF"
        Property { name: "left"; type: "double"; read: "left"; write: "setLeft"; index: 0; isFinal: true }
        Property { name: "right"; type: "double"; read: "right"; write: "setRight"; index: 1; isFinal: true }
        Property { name: "top"; type: "double"; read: "top"; write: "setTop"; index: 2; isFinal: true }
        Property {
            name: "bottom"
            type: "double"
            read: "bottom"
            write: "setBottom"
            index: 3
            isFinal: true
        }
        Method { name: "toString"; type: "QString"; isMethodConstant: true }
        Method {
            name: "QQmlMarginsFValueType"
            isConstructor: true
            Parameter { name: "margins"; type: "QMarginsF" }
        }
        Method {
            name: "QQmlMarginsFValueType"
            isConstructor: true
            Parameter { name: "margins"; type: "QMargins" }
        }
    }
    Component {
        file: "private/qqmlvaluetype_p.h"
        name: "QMargins"
        accessSemantics: "value"
        extension: "QQmlMarginsValueType"
    }
    Component {
        file: "private/qqmlvaluetype_p.h"
        name: "QQmlMarginsValueType"
        accessSemantics: "value"
        prototype: "QMargins"
        Property { name: "left"; type: "int"; read: "left"; write: "setLeft"; index: 0; isFinal: true }
        Property { name: "right"; type: "int"; read: "right"; write: "setRight"; index: 1; isFinal: true }
        Property { name: "top"; type: "int"; read: "top"; write: "setTop"; index: 2; isFinal: true }
        Property { name: "bottom"; type: "int"; read: "bottom"; write: "setBottom"; index: 3; isFinal: true }
        Method { name: "toString"; type: "QString"; isMethodConstant: true }
        Method {
            name: "QQmlMarginsValueType"
            isConstructor: true
            Parameter { name: "margins"; type: "QMargins" }
        }
        Method {
            name: "QQmlMarginsValueType"
            isConstructor: true
            Parameter { name: "margins"; type: "QMarginsF" }
        }
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "std::nullptr_t"
        accessSemantics: "value"
        extension: "QQmlNullForeign"
    }
    Component { file: "private/qqmlbuiltins_p.h"; name: "QQmlNullForeign"; accessSemantics: "value" }
    Component {
        file: "private/qqmlplatform_p.h"
        name: "QQmlPlatform"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "os"
            type: "QString"
            read: "os"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "pluginName"
            type: "QString"
            read: "pluginName"
            index: 1
            isReadonly: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "private/qqmlvaluetype_p.h"
        name: "QPointF"
        accessSemantics: "value"
        extension: "QQmlPointFValueType"
        exports: ["QML/point 1.0"]
        isStructured: true
        exportMetaObjectRevisions: [256]
    }
    Component {
        file: "private/qqmlvaluetype_p.h"
        name: "QQmlPointFValueType"
        accessSemantics: "value"
        prototype: "QPointF"
        Property { name: "x"; type: "double"; read: "x"; write: "setX"; index: 0; isFinal: true }
        Property { name: "y"; type: "double"; read: "y"; write: "setY"; index: 1; isFinal: true }
        Method { name: "toString"; type: "QString"; isMethodConstant: true }
        Method { name: "QQmlPointFValueType"; isConstructor: true }
        Method {
            name: "QQmlPointFValueType"
            isConstructor: true
            Parameter { name: "point"; type: "QPointF" }
        }
        Method {
            name: "QQmlPointFValueType"
            isConstructor: true
            Parameter { name: "point"; type: "QPoint" }
        }
    }
    Component {
        file: "private/qqmlvaluetype_p.h"
        name: "QPoint"
        accessSemantics: "value"
        extension: "QQmlPointValueType"
    }
    Component {
        file: "private/qqmlvaluetype_p.h"
        name: "QQmlPointValueType"
        accessSemantics: "value"
        prototype: "QPoint"
        Property { name: "x"; type: "int"; read: "x"; write: "setX"; index: 0; isFinal: true }
        Property { name: "y"; type: "int"; read: "y"; write: "setY"; index: 1; isFinal: true }
        Method { name: "toString"; type: "QString"; isMethodConstant: true }
        Method {
            name: "QQmlPointValueType"
            isConstructor: true
            Parameter { name: "point"; type: "QPoint" }
        }
        Method {
            name: "QQmlPointValueType"
            isConstructor: true
            Parameter { name: "point"; type: "QPointF" }
        }
    }
    Component {
        file: "qqmlproperty.h"
        name: "QQmlProperty"
        accessSemantics: "value"
        Property {
            name: "object"
            type: "QObject"
            isPointer: true
            read: "object"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "name"
            type: "QString"
            read: "name"
            index: 1
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "QByteArray"
        accessSemantics: "value"
        extension: "ArrayBuffer"
        extensionIsJavaScript: true
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "QByteArrayList"
        accessSemantics: "sequence"
        valueType: "QByteArray"
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "QChar"
        accessSemantics: "value"
        extension: "String"
        extensionIsJavaScript: true
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "QDate"
        accessSemantics: "value"
        extension: "Date"
        extensionIsJavaScript: true
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "QJSValue"
        accessSemantics: "value"
        extension: "QQmlQJSValueForeign"
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "QQmlQJSValueForeign"
        accessSemantics: "value"
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "QJsonArray"
        accessSemantics: "sequence"
        valueType: "QJsonValue"
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "QJsonObject"
        accessSemantics: "value"
        extension: "Object"
        extensionIsJavaScript: true
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "QJsonValue"
        accessSemantics: "value"
        extension: "QQmlQJsonValueForeign"
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "QQmlQJsonValueForeign"
        accessSemantics: "value"
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "QObjectList"
        accessSemantics: "sequence"
        valueType: "QObject"
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "QStringList"
        accessSemantics: "sequence"
        valueType: "QString"
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "QTime"
        accessSemantics: "value"
        extension: "Date"
        extensionIsJavaScript: true
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "QVariantHash"
        accessSemantics: "value"
        extension: "Object"
        extensionIsJavaScript: true
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "QVariantList"
        accessSemantics: "sequence"
        valueType: "QVariant"
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "QVariantMap"
        accessSemantics: "value"
        extension: "Object"
        extensionIsJavaScript: true
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "qint8"
        accessSemantics: "value"
        extension: "Number"
        extensionIsJavaScript: true
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "qlonglong"
        aliases: ["qsizetype"]
        accessSemantics: "value"
        extension: "Number"
        extensionIsJavaScript: true
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "QObject"
        accessSemantics: "reference"
        extension: "Object"
        extensionIsJavaScript: true
        exports: ["QML/QtObject 1.0"]
        exportMetaObjectRevisions: [256]
        Property {
            name: "objectName"
            type: "QString"
            bindable: "bindableObjectName"
            read: "objectName"
            write: "setObjectName"
            notify: "objectNameChanged"
            index: 0
        }
        Signal {
            name: "objectNameChanged"
            Parameter { name: "objectName"; type: "QString" }
        }
        Method { name: "toString"; type: "QString"; isMethodConstant: true }
        Method {
            name: "destroy"
            Parameter { name: "delay"; type: "int" }
        }
        Method { name: "destroy"; isCloned: true }
        Method {
            name: "QObject"
            isConstructor: true
            Parameter { name: "parent"; type: "QObject"; isPointer: true }
        }
        Method { name: "QObject"; isCloned: true; isConstructor: true }
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "quint8"
        accessSemantics: "value"
        extension: "Number"
        extensionIsJavaScript: true
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "qulonglong"
        accessSemantics: "value"
        extension: "Number"
        extensionIsJavaScript: true
    }
    Component {
        file: "private/qqmlvaluetype_p.h"
        name: "QRectF"
        accessSemantics: "value"
        extension: "QQmlRectFValueType"
        exports: ["QML/rect 1.0"]
        isStructured: true
        exportMetaObjectRevisions: [256]
    }
    Component {
        file: "private/qqmlvaluetype_p.h"
        name: "QQmlRectFValueType"
        accessSemantics: "value"
        prototype: "QRectF"
        Property { name: "x"; type: "double"; read: "x"; write: "setX"; index: 0; isFinal: true }
        Property { name: "y"; type: "double"; read: "y"; write: "setY"; index: 1; isFinal: true }
        Property { name: "width"; type: "double"; read: "width"; write: "setWidth"; index: 2; isFinal: true }
        Property {
            name: "height"
            type: "double"
            read: "height"
            write: "setHeight"
            index: 3
            isFinal: true
        }
        Property { name: "left"; type: "double"; read: "left"; index: 4; isReadonly: true; isFinal: true }
        Property { name: "right"; type: "double"; read: "right"; index: 5; isReadonly: true; isFinal: true }
        Property { name: "top"; type: "double"; read: "top"; index: 6; isReadonly: true; isFinal: true }
        Property { name: "bottom"; type: "double"; read: "bottom"; index: 7; isReadonly: true; isFinal: true }
        Method { name: "toString"; type: "QString"; isMethodConstant: true }
        Method { name: "QQmlRectFValueType"; isConstructor: true }
        Method {
            name: "QQmlRectFValueType"
            isConstructor: true
            Parameter { name: "rect"; type: "QRectF" }
        }
        Method {
            name: "QQmlRectFValueType"
            isConstructor: true
            Parameter { name: "rect"; type: "QRect" }
        }
    }
    Component {
        file: "private/qqmlvaluetype_p.h"
        name: "QRect"
        accessSemantics: "value"
        extension: "QQmlRectValueType"
    }
    Component {
        file: "private/qqmlvaluetype_p.h"
        name: "QQmlRectValueType"
        accessSemantics: "value"
        prototype: "QRect"
        Property { name: "x"; type: "int"; read: "x"; write: "setX"; index: 0; isFinal: true }
        Property { name: "y"; type: "int"; read: "y"; write: "setY"; index: 1; isFinal: true }
        Property { name: "width"; type: "int"; read: "width"; write: "setWidth"; index: 2; isFinal: true }
        Property { name: "height"; type: "int"; read: "height"; write: "setHeight"; index: 3; isFinal: true }
        Property { name: "left"; type: "int"; read: "left"; index: 4; isReadonly: true; isFinal: true }
        Property { name: "right"; type: "int"; read: "right"; index: 5; isReadonly: true; isFinal: true }
        Property { name: "top"; type: "int"; read: "top"; index: 6; isReadonly: true; isFinal: true }
        Property { name: "bottom"; type: "int"; read: "bottom"; index: 7; isReadonly: true; isFinal: true }
        Method { name: "toString"; type: "QString"; isMethodConstant: true }
        Method {
            name: "QQmlRectValueType"
            isConstructor: true
            Parameter { name: "rect"; type: "QRect" }
        }
        Method {
            name: "QQmlRectValueType"
            isConstructor: true
            Parameter { name: "rect"; type: "QRectF" }
        }
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "QRegularExpression"
        accessSemantics: "value"
        extension: "RegExp"
        extensionIsJavaScript: true
        exports: ["QML/regexp 1.0"]
        isCreatable: false
        exportMetaObjectRevisions: [256]
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "QQmlScriptString"
        accessSemantics: "value"
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "short"
        accessSemantics: "value"
        extension: "Number"
        extensionIsJavaScript: true
    }
    Component {
        file: "private/qqmlvaluetype_p.h"
        name: "QSizeF"
        accessSemantics: "value"
        extension: "QQmlSizeFValueType"
        exports: ["QML/size 1.0"]
        isStructured: true
        exportMetaObjectRevisions: [256]
    }
    Component {
        file: "private/qqmlvaluetype_p.h"
        name: "QQmlSizeFValueType"
        accessSemantics: "value"
        prototype: "QSizeF"
        Property { name: "width"; type: "double"; read: "width"; write: "setWidth"; index: 0; isFinal: true }
        Property {
            name: "height"
            type: "double"
            read: "height"
            write: "setHeight"
            index: 1
            isFinal: true
        }
        Method { name: "toString"; type: "QString"; isMethodConstant: true }
        Method { name: "QQmlSizeFValueType"; isConstructor: true }
        Method {
            name: "QQmlSizeFValueType"
            isConstructor: true
            Parameter { name: "size"; type: "QSizeF" }
        }
        Method {
            name: "QQmlSizeFValueType"
            isConstructor: true
            Parameter { name: "size"; type: "QSize" }
        }
    }
    Component {
        file: "private/qqmlvaluetype_p.h"
        name: "QSize"
        accessSemantics: "value"
        extension: "QQmlSizeValueType"
    }
    Component {
        file: "private/qqmlvaluetype_p.h"
        name: "QQmlSizeValueType"
        accessSemantics: "value"
        prototype: "QSize"
        Property { name: "width"; type: "int"; read: "width"; write: "setWidth"; index: 0; isFinal: true }
        Property { name: "height"; type: "int"; read: "height"; write: "setHeight"; index: 1; isFinal: true }
        Method { name: "toString"; type: "QString"; isMethodConstant: true }
        Method {
            name: "QQmlSizeValueType"
            isConstructor: true
            Parameter { name: "size"; type: "QSize" }
        }
        Method {
            name: "QQmlSizeValueType"
            isConstructor: true
            Parameter { name: "size"; type: "QSizeF" }
        }
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "QString"
        accessSemantics: "value"
        extension: "String"
        extensionIsJavaScript: true
        exports: ["QML/string 1.0"]
        isCreatable: false
        exportMetaObjectRevisions: [256]
    }
    Component {
        file: "qqml.h"
        name: "QQmlTypeNotAvailable"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: []
        isCreatable: false
        exportMetaObjectRevisions: []
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "uint"
        accessSemantics: "value"
        extension: "Number"
        extensionIsJavaScript: true
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "QUrl"
        accessSemantics: "value"
        extension: "URL"
        extensionIsJavaScript: true
        exports: ["QML/url 1.0"]
        isCreatable: false
        exportMetaObjectRevisions: [256]
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "ushort"
        accessSemantics: "value"
        extension: "Number"
        extensionIsJavaScript: true
    }
    Component {
        file: "private/qqmlvaluetype_p.h"
        name: "QQmlV4ExecutionEnginePtr"
        accessSemantics: "value"
        extension: "QQmlV4ExecutionEnginePtrForeign"
    }
    Component {
        file: "private/qqmlvaluetype_p.h"
        name: "QQmlV4ExecutionEnginePtrForeign"
        accessSemantics: "value"
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "QQmlV4FunctionPtr"
        accessSemantics: "value"
        extension: "QQmlV4FunctionPtrForeign"
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "QQmlV4FunctionPtrForeign"
        accessSemantics: "value"
    }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "QVariant"
        accessSemantics: "value"
        extension: "QQmlVarForeign"
        exports: ["QML/var 1.0", "QML/variant 1.0"]
        isCreatable: false
        exportMetaObjectRevisions: [256]
    }
    Component { file: "private/qqmlbuiltins_p.h"; name: "QQmlVarForeign"; accessSemantics: "value" }
    Component {
        file: "private/qqmlbuiltins_p.h"
        name: "void"
        accessSemantics: "value"
        extension: "undefined"
        extensionIsJavaScript: true
        exports: ["QML/void 1.0"]
        isCreatable: false
        exportMetaObjectRevisions: [256]
    }
    Component {
        file: "private/qv4sequenceobject_p.h"
        name: "QList<qreal>"
        accessSemantics: "sequence"
        valueType: "double"
    }
    Component {
        file: "private/qv4sequenceobject_p.h"
        name: "std::vector<qreal>"
        accessSemantics: "sequence"
        valueType: "double"
    }
    Component {
        file: "private/qv4sequenceobject_p.h"
        name: "std::vector<QString>"
        accessSemantics: "sequence"
        valueType: "QString"
    }
    Component {
        file: "private/qv4sequenceobject_p.h"
        name: "std::vector<QUrl>"
        accessSemantics: "sequence"
        valueType: "QUrl"
    }
    Component {
        file: "qnamespace.h"
        name: "Qt"
        accessSemantics: "none"
        Enum {
            name: "GlobalColor"
            values: [
                "color0",
                "color1",
                "black",
                "white",
                "darkGray",
                "gray",
                "lightGray",
                "red",
                "green",
                "blue",
                "cyan",
                "magenta",
                "yellow",
                "darkRed",
                "darkGreen",
                "darkBlue",
                "darkCyan",
                "darkMagenta",
                "darkYellow",
                "transparent"
            ]
        }
        Enum {
            name: "ColorScheme"
            isScoped: true
            values: ["Unknown", "Light", "Dark"]
        }
        Enum {
            name: "MouseButtons"
            alias: "MouseButton"
            isFlag: true
            values: [
                "NoButton",
                "LeftButton",
                "RightButton",
                "MiddleButton",
                "BackButton",
                "XButton1",
                "ExtraButton1",
                "ForwardButton",
                "XButton2",
                "ExtraButton2",
                "TaskButton",
                "ExtraButton3",
                "ExtraButton4",
                "ExtraButton5",
                "ExtraButton6",
                "ExtraButton7",
                "ExtraButton8",
                "ExtraButton9",
                "ExtraButton10",
                "ExtraButton11",
                "ExtraButton12",
                "ExtraButton13",
                "ExtraButton14",
                "ExtraButton15",
                "ExtraButton16",
                "ExtraButton17",
                "ExtraButton18",
                "ExtraButton19",
                "ExtraButton20",
                "ExtraButton21",
                "ExtraButton22",
                "ExtraButton23",
                "ExtraButton24",
                "AllButtons",
                "MaxMouseButton",
                "MouseButtonMask"
            ]
        }
        Enum {
            name: "Orientation"
            values: ["Horizontal", "Vertical"]
        }
        Enum {
            name: "Orientations"
            alias: "Orientation"
            isFlag: true
            values: ["Horizontal", "Vertical"]
        }
        Enum {
            name: "FocusPolicy"
            values: [
                "NoFocus",
                "TabFocus",
                "ClickFocus",
                "StrongFocus",
                "WheelFocus"
            ]
        }
        Enum {
            name: "TabFocusBehavior"
            values: [
                "NoTabFocus",
                "TabFocusTextControls",
                "TabFocusListControls",
                "TabFocusAllControls"
            ]
        }
        Enum {
            name: "SortOrder"
            values: ["AscendingOrder", "DescendingOrder"]
        }
        Enum {
            name: "SplitBehavior"
            alias: "SplitBehaviorFlags"
            isFlag: true
            values: ["KeepEmptyParts", "SkipEmptyParts"]
        }
        Enum {
            name: "Alignment"
            alias: "AlignmentFlag"
            isFlag: true
            values: [
                "AlignLeft",
                "AlignLeading",
                "AlignRight",
                "AlignTrailing",
                "AlignHCenter",
                "AlignJustify",
                "AlignAbsolute",
                "AlignHorizontal_Mask",
                "AlignTop",
                "AlignBottom",
                "AlignVCenter",
                "AlignBaseline",
                "AlignVertical_Mask",
                "AlignCenter"
            ]
        }
        Enum {
            name: "TextFlag"
            values: [
                "TextSingleLine",
                "TextDontClip",
                "TextExpandTabs",
                "TextShowMnemonic",
                "TextWordWrap",
                "TextWrapAnywhere",
                "TextDontPrint",
                "TextIncludeTrailingSpaces",
                "TextHideMnemonic",
                "TextJustificationForced",
                "TextForceLeftToRight",
                "TextForceRightToLeft",
                "TextLongestVariant"
            ]
        }
        Enum {
            name: "TextElideMode"
            values: ["ElideLeft", "ElideRight", "ElideMiddle", "ElideNone"]
        }
        Enum {
            name: "WindowType"
            values: [
                "Widget",
                "Window",
                "Dialog",
                "Sheet",
                "Drawer",
                "Popup",
                "Tool",
                "ToolTip",
                "SplashScreen",
                "Desktop",
                "SubWindow",
                "ForeignWindow",
                "CoverWindow",
                "WindowType_Mask",
                "MSWindowsFixedSizeDialogHint",
                "MSWindowsOwnDC",
                "BypassWindowManagerHint",
                "X11BypassWindowManagerHint",
                "FramelessWindowHint",
                "WindowTitleHint",
                "WindowSystemMenuHint",
                "WindowMinimizeButtonHint",
                "WindowMaximizeButtonHint",
                "WindowMinMaxButtonsHint",
                "WindowContextHelpButtonHint",
                "WindowShadeButtonHint",
                "WindowStaysOnTopHint",
                "WindowTransparentForInput",
                "WindowOverridesSystemGestures",
                "WindowDoesNotAcceptFocus",
                "MaximizeUsingFullscreenGeometryHint",
                "ExpandedClientAreaHint",
                "NoTitleBarBackgroundHint",
                "CustomizeWindowHint",
                "WindowStaysOnBottomHint",
                "WindowCloseButtonHint",
                "MacWindowToolBarButtonHint",
                "BypassGraphicsProxyWidget",
                "NoDropShadowWindowHint",
                "WindowFullscreenButtonHint"
            ]
        }
        Enum {
            name: "WindowFlags"
            alias: "WindowType"
            isFlag: true
            values: [
                "Widget",
                "Window",
                "Dialog",
                "Sheet",
                "Drawer",
                "Popup",
                "Tool",
                "ToolTip",
                "SplashScreen",
                "Desktop",
                "SubWindow",
                "ForeignWindow",
                "CoverWindow",
                "WindowType_Mask",
                "MSWindowsFixedSizeDialogHint",
                "MSWindowsOwnDC",
                "BypassWindowManagerHint",
                "X11BypassWindowManagerHint",
                "FramelessWindowHint",
                "WindowTitleHint",
                "WindowSystemMenuHint",
                "WindowMinimizeButtonHint",
                "WindowMaximizeButtonHint",
                "WindowMinMaxButtonsHint",
                "WindowContextHelpButtonHint",
                "WindowShadeButtonHint",
                "WindowStaysOnTopHint",
                "WindowTransparentForInput",
                "WindowOverridesSystemGestures",
                "WindowDoesNotAcceptFocus",
                "MaximizeUsingFullscreenGeometryHint",
                "ExpandedClientAreaHint",
                "NoTitleBarBackgroundHint",
                "CustomizeWindowHint",
                "WindowStaysOnBottomHint",
                "WindowCloseButtonHint",
                "MacWindowToolBarButtonHint",
                "BypassGraphicsProxyWidget",
                "NoDropShadowWindowHint",
                "WindowFullscreenButtonHint"
            ]
        }
        Enum {
            name: "WindowState"
            values: [
                "WindowNoState",
                "WindowMinimized",
                "WindowMaximized",
                "WindowFullScreen",
                "WindowActive"
            ]
        }
        Enum {
            name: "WindowStates"
            alias: "WindowState"
            isFlag: true
            values: [
                "WindowNoState",
                "WindowMinimized",
                "WindowMaximized",
                "WindowFullScreen",
                "WindowActive"
            ]
        }
        Enum {
            name: "ApplicationState"
            values: [
                "ApplicationSuspended",
                "ApplicationHidden",
                "ApplicationInactive",
                "ApplicationActive"
            ]
        }
        Enum {
            name: "ScreenOrientation"
            values: [
                "PrimaryOrientation",
                "PortraitOrientation",
                "LandscapeOrientation",
                "InvertedPortraitOrientation",
                "InvertedLandscapeOrientation"
            ]
        }
        Enum {
            name: "ScreenOrientations"
            alias: "ScreenOrientation"
            isFlag: true
            values: [
                "PrimaryOrientation",
                "PortraitOrientation",
                "LandscapeOrientation",
                "InvertedPortraitOrientation",
                "InvertedLandscapeOrientation"
            ]
        }
        Enum {
            name: "WidgetAttribute"
            values: [
                "WA_Disabled",
                "WA_UnderMouse",
                "WA_MouseTracking",
                "WA_OpaquePaintEvent",
                "WA_StaticContents",
                "WA_LaidOut",
                "WA_PaintOnScreen",
                "WA_NoSystemBackground",
                "WA_UpdatesDisabled",
                "WA_Mapped",
                "WA_InputMethodEnabled",
                "WA_WState_Visible",
                "WA_WState_Hidden",
                "WA_ForceDisabled",
                "WA_KeyCompression",
                "WA_PendingMoveEvent",
                "WA_PendingResizeEvent",
                "WA_SetPalette",
                "WA_SetFont",
                "WA_SetCursor",
                "WA_NoChildEventsFromChildren",
                "WA_WindowModified",
                "WA_Resized",
                "WA_Moved",
                "WA_PendingUpdate",
                "WA_InvalidSize",
                "WA_CustomWhatsThis",
                "WA_LayoutOnEntireRect",
                "WA_OutsideWSRange",
                "WA_GrabbedShortcut",
                "WA_TransparentForMouseEvents",
                "WA_PaintUnclipped",
                "WA_SetWindowIcon",
                "WA_NoMouseReplay",
                "WA_DeleteOnClose",
                "WA_RightToLeft",
                "WA_SetLayoutDirection",
                "WA_NoChildEventsForParent",
                "WA_ForceUpdatesDisabled",
                "WA_WState_Created",
                "WA_WState_CompressKeys",
                "WA_WState_InPaintEvent",
                "WA_WState_Reparented",
                "WA_WState_ConfigPending",
                "WA_WState_Polished",
                "WA_WState_OwnSizePolicy",
                "WA_WState_ExplicitShowHide",
                "WA_ShowModal",
                "WA_MouseNoMask",
                "WA_NoMousePropagation",
                "WA_Hover",
                "WA_InputMethodTransparent",
                "WA_QuitOnClose",
                "WA_KeyboardFocusChange",
                "WA_AcceptDrops",
                "WA_DropSiteRegistered",
                "WA_WindowPropagation",
                "WA_NoX11EventCompression",
                "WA_TintedBackground",
                "WA_X11OpenGLOverlay",
                "WA_AlwaysShowToolTips",
                "WA_MacOpaqueSizeGrip",
                "WA_SetStyle",
                "WA_SetLocale",
                "WA_MacShowFocusRect",
                "WA_MacNormalSize",
                "WA_MacSmallSize",
                "WA_MacMiniSize",
                "WA_LayoutUsesWidgetRect",
                "WA_StyledBackground",
                "WA_CanHostQMdiSubWindowTitleBar",
                "WA_MacAlwaysShowToolWindow",
                "WA_StyleSheet",
                "WA_ShowWithoutActivating",
                "WA_X11BypassTransientForHint",
                "WA_NativeWindow",
                "WA_DontCreateNativeAncestors",
                "WA_DontShowOnScreen",
                "WA_X11NetWmWindowTypeDesktop",
                "WA_X11NetWmWindowTypeDock",
                "WA_X11NetWmWindowTypeToolBar",
                "WA_X11NetWmWindowTypeMenu",
                "WA_X11NetWmWindowTypeUtility",
                "WA_X11NetWmWindowTypeSplash",
                "WA_X11NetWmWindowTypeDialog",
                "WA_X11NetWmWindowTypeDropDownMenu",
                "WA_X11NetWmWindowTypePopupMenu",
                "WA_X11NetWmWindowTypeToolTip",
                "WA_X11NetWmWindowTypeNotification",
                "WA_X11NetWmWindowTypeCombo",
                "WA_X11NetWmWindowTypeDND",
                "WA_SetWindowModality",
                "WA_WState_WindowOpacitySet",
                "WA_TranslucentBackground",
                "WA_AcceptTouchEvents",
                "WA_WState_AcceptedTouchBeginEvent",
                "WA_TouchPadAcceptSingleTouchEvents",
                "WA_X11DoNotAcceptFocus",
                "WA_AlwaysStackOnTop",
                "WA_TabletTracking",
                "WA_ContentsMarginsRespectsSafeArea",
                "WA_StyleSheetTarget",
                "WA_AttributeCount"
            ]
        }
        Enum {
            name: "ApplicationAttribute"
            values: [
                "AA_QtQuickUseDefaultSizePolicy",
                "AA_DontShowIconsInMenus",
                "AA_NativeWindows",
                "AA_DontCreateNativeWidgetSiblings",
                "AA_PluginApplication",
                "AA_DontUseNativeMenuBar",
                "AA_MacDontSwapCtrlAndMeta",
                "AA_Use96Dpi",
                "AA_DisableNativeVirtualKeyboard",
                "AA_DontUseNativeMenuWindows",
                "AA_SynthesizeTouchForUnhandledMouseEvents",
                "AA_SynthesizeMouseForUnhandledTouchEvents",
                "AA_UseHighDpiPixmaps",
                "AA_ForceRasterWidgets",
                "AA_UseDesktopOpenGL",
                "AA_UseOpenGLES",
                "AA_UseSoftwareOpenGL",
                "AA_ShareOpenGLContexts",
                "AA_SetPalette",
                "AA_EnableHighDpiScaling",
                "AA_DisableHighDpiScaling",
                "AA_UseStyleSheetPropagationInWidgetStyles",
                "AA_DontUseNativeDialogs",
                "AA_SynthesizeMouseForUnhandledTabletEvents",
                "AA_CompressHighFrequencyEvents",
                "AA_DontCheckOpenGLContextThreadAffinity",
                "AA_DisableShaderDiskCache",
                "AA_DontShowShortcutsInContextMenus",
                "AA_CompressTabletEvents",
                "AA_DisableSessionManager",
                "AA_AttributeCount"
            ]
        }
        Enum {
            name: "ImageConversionFlags"
            alias: "ImageConversionFlag"
            isFlag: true
            values: [
                "ColorMode_Mask",
                "AutoColor",
                "ColorOnly",
                "MonoOnly",
                "AlphaDither_Mask",
                "ThresholdAlphaDither",
                "OrderedAlphaDither",
                "DiffuseAlphaDither",
                "NoAlpha",
                "Dither_Mask",
                "DiffuseDither",
                "OrderedDither",
                "ThresholdDither",
                "DitherMode_Mask",
                "AutoDither",
                "PreferDither",
                "AvoidDither",
                "NoOpaqueDetection",
                "NoFormatConversion"
            ]
        }
        Enum {
            name: "BGMode"
            values: ["TransparentMode", "OpaqueMode"]
        }
        Enum {
            name: "Key"
            values: [
                "Key_Space",
                "Key_Any",
                "Key_Exclam",
                "Key_QuoteDbl",
                "Key_NumberSign",
                "Key_Dollar",
                "Key_Percent",
                "Key_Ampersand",
                "Key_Apostrophe",
                "Key_ParenLeft",
                "Key_ParenRight",
                "Key_Asterisk",
                "Key_Plus",
                "Key_Comma",
                "Key_Minus",
                "Key_Period",
                "Key_Slash",
                "Key_0",
                "Key_1",
                "Key_2",
                "Key_3",
                "Key_4",
                "Key_5",
                "Key_6",
                "Key_7",
                "Key_8",
                "Key_9",
                "Key_Colon",
                "Key_Semicolon",
                "Key_Less",
                "Key_Equal",
                "Key_Greater",
                "Key_Question",
                "Key_At",
                "Key_A",
                "Key_B",
                "Key_C",
                "Key_D",
                "Key_E",
                "Key_F",
                "Key_G",
                "Key_H",
                "Key_I",
                "Key_J",
                "Key_K",
                "Key_L",
                "Key_M",
                "Key_N",
                "Key_O",
                "Key_P",
                "Key_Q",
                "Key_R",
                "Key_S",
                "Key_T",
                "Key_U",
                "Key_V",
                "Key_W",
                "Key_X",
                "Key_Y",
                "Key_Z",
                "Key_BracketLeft",
                "Key_Backslash",
                "Key_BracketRight",
                "Key_AsciiCircum",
                "Key_Underscore",
                "Key_QuoteLeft",
                "Key_BraceLeft",
                "Key_Bar",
                "Key_BraceRight",
                "Key_AsciiTilde",
                "Key_nobreakspace",
                "Key_exclamdown",
                "Key_cent",
                "Key_sterling",
                "Key_currency",
                "Key_yen",
                "Key_brokenbar",
                "Key_section",
                "Key_diaeresis",
                "Key_copyright",
                "Key_ordfeminine",
                "Key_guillemotleft",
                "Key_notsign",
                "Key_hyphen",
                "Key_registered",
                "Key_macron",
                "Key_degree",
                "Key_plusminus",
                "Key_twosuperior",
                "Key_threesuperior",
                "Key_acute",
                "Key_micro",
                "Key_mu",
                "Key_paragraph",
                "Key_periodcentered",
                "Key_cedilla",
                "Key_onesuperior",
                "Key_masculine",
                "Key_guillemotright",
                "Key_onequarter",
                "Key_onehalf",
                "Key_threequarters",
                "Key_questiondown",
                "Key_Agrave",
                "Key_Aacute",
                "Key_Acircumflex",
                "Key_Atilde",
                "Key_Adiaeresis",
                "Key_Aring",
                "Key_AE",
                "Key_Ccedilla",
                "Key_Egrave",
                "Key_Eacute",
                "Key_Ecircumflex",
                "Key_Ediaeresis",
                "Key_Igrave",
                "Key_Iacute",
                "Key_Icircumflex",
                "Key_Idiaeresis",
                "Key_ETH",
                "Key_Ntilde",
                "Key_Ograve",
                "Key_Oacute",
                "Key_Ocircumflex",
                "Key_Otilde",
                "Key_Odiaeresis",
                "Key_multiply",
                "Key_Ooblique",
                "Key_Ugrave",
                "Key_Uacute",
                "Key_Ucircumflex",
                "Key_Udiaeresis",
                "Key_Yacute",
                "Key_THORN",
                "Key_ssharp",
                "Key_division",
                "Key_ydiaeresis",
                "Key_Escape",
                "Key_Tab",
                "Key_Backtab",
                "Key_Backspace",
                "Key_Return",
                "Key_Enter",
                "Key_Insert",
                "Key_Delete",
                "Key_Pause",
                "Key_Print",
                "Key_SysReq",
                "Key_Clear",
                "Key_Home",
                "Key_End",
                "Key_Left",
                "Key_Up",
                "Key_Right",
                "Key_Down",
                "Key_PageUp",
                "Key_PageDown",
                "Key_Shift",
                "Key_Control",
                "Key_Meta",
                "Key_Alt",
                "Key_CapsLock",
                "Key_NumLock",
                "Key_ScrollLock",
                "Key_F1",
                "Key_F2",
                "Key_F3",
                "Key_F4",
                "Key_F5",
                "Key_F6",
                "Key_F7",
                "Key_F8",
                "Key_F9",
                "Key_F10",
                "Key_F11",
                "Key_F12",
                "Key_F13",
                "Key_F14",
                "Key_F15",
                "Key_F16",
                "Key_F17",
                "Key_F18",
                "Key_F19",
                "Key_F20",
                "Key_F21",
                "Key_F22",
                "Key_F23",
                "Key_F24",
                "Key_F25",
                "Key_F26",
                "Key_F27",
                "Key_F28",
                "Key_F29",
                "Key_F30",
                "Key_F31",
                "Key_F32",
                "Key_F33",
                "Key_F34",
                "Key_F35",
                "Key_Super_L",
                "Key_Super_R",
                "Key_Menu",
                "Key_Hyper_L",
                "Key_Hyper_R",
                "Key_Help",
                "Key_Direction_L",
                "Key_Direction_R",
                "Key_AltGr",
                "Key_Multi_key",
                "Key_Codeinput",
                "Key_SingleCandidate",
                "Key_MultipleCandidate",
                "Key_PreviousCandidate",
                "Key_Mode_switch",
                "Key_Kanji",
                "Key_Muhenkan",
                "Key_Henkan",
                "Key_Romaji",
                "Key_Hiragana",
                "Key_Katakana",
                "Key_Hiragana_Katakana",
                "Key_Zenkaku",
                "Key_Hankaku",
                "Key_Zenkaku_Hankaku",
                "Key_Touroku",
                "Key_Massyo",
                "Key_Kana_Lock",
                "Key_Kana_Shift",
                "Key_Eisu_Shift",
                "Key_Eisu_toggle",
                "Key_Hangul",
                "Key_Hangul_Start",
                "Key_Hangul_End",
                "Key_Hangul_Hanja",
                "Key_Hangul_Jamo",
                "Key_Hangul_Romaja",
                "Key_Hangul_Jeonja",
                "Key_Hangul_Banja",
                "Key_Hangul_PreHanja",
                "Key_Hangul_PostHanja",
                "Key_Hangul_Special",
                "Key_Dead_Grave",
                "Key_Dead_Acute",
                "Key_Dead_Circumflex",
                "Key_Dead_Tilde",
                "Key_Dead_Macron",
                "Key_Dead_Breve",
                "Key_Dead_Abovedot",
                "Key_Dead_Diaeresis",
                "Key_Dead_Abovering",
                "Key_Dead_Doubleacute",
                "Key_Dead_Caron",
                "Key_Dead_Cedilla",
                "Key_Dead_Ogonek",
                "Key_Dead_Iota",
                "Key_Dead_Voiced_Sound",
                "Key_Dead_Semivoiced_Sound",
                "Key_Dead_Belowdot",
                "Key_Dead_Hook",
                "Key_Dead_Horn",
                "Key_Dead_Stroke",
                "Key_Dead_Abovecomma",
                "Key_Dead_Abovereversedcomma",
                "Key_Dead_Doublegrave",
                "Key_Dead_Belowring",
                "Key_Dead_Belowmacron",
                "Key_Dead_Belowcircumflex",
                "Key_Dead_Belowtilde",
                "Key_Dead_Belowbreve",
                "Key_Dead_Belowdiaeresis",
                "Key_Dead_Invertedbreve",
                "Key_Dead_Belowcomma",
                "Key_Dead_Currency",
                "Key_Dead_a",
                "Key_Dead_A",
                "Key_Dead_e",
                "Key_Dead_E",
                "Key_Dead_i",
                "Key_Dead_I",
                "Key_Dead_o",
                "Key_Dead_O",
                "Key_Dead_u",
                "Key_Dead_U",
                "Key_Dead_Small_Schwa",
                "Key_Dead_Capital_Schwa",
                "Key_Dead_Greek",
                "Key_Dead_Lowline",
                "Key_Dead_Aboveverticalline",
                "Key_Dead_Belowverticalline",
                "Key_Dead_Longsolidusoverlay",
                "Key_Back",
                "Key_Forward",
                "Key_Stop",
                "Key_Refresh",
                "Key_VolumeDown",
                "Key_VolumeMute",
                "Key_VolumeUp",
                "Key_BassBoost",
                "Key_BassUp",
                "Key_BassDown",
                "Key_TrebleUp",
                "Key_TrebleDown",
                "Key_MediaPlay",
                "Key_MediaStop",
                "Key_MediaPrevious",
                "Key_MediaNext",
                "Key_MediaRecord",
                "Key_MediaPause",
                "Key_MediaTogglePlayPause",
                "Key_HomePage",
                "Key_Favorites",
                "Key_Search",
                "Key_Standby",
                "Key_OpenUrl",
                "Key_LaunchMail",
                "Key_LaunchMedia",
                "Key_Launch0",
                "Key_Launch1",
                "Key_Launch2",
                "Key_Launch3",
                "Key_Launch4",
                "Key_Launch5",
                "Key_Launch6",
                "Key_Launch7",
                "Key_Launch8",
                "Key_Launch9",
                "Key_LaunchA",
                "Key_LaunchB",
                "Key_LaunchC",
                "Key_LaunchD",
                "Key_LaunchE",
                "Key_LaunchF",
                "Key_MonBrightnessUp",
                "Key_MonBrightnessDown",
                "Key_KeyboardLightOnOff",
                "Key_KeyboardBrightnessUp",
                "Key_KeyboardBrightnessDown",
                "Key_PowerOff",
                "Key_WakeUp",
                "Key_Eject",
                "Key_ScreenSaver",
                "Key_WWW",
                "Key_Memo",
                "Key_LightBulb",
                "Key_Shop",
                "Key_History",
                "Key_AddFavorite",
                "Key_HotLinks",
                "Key_BrightnessAdjust",
                "Key_Finance",
                "Key_Community",
                "Key_AudioRewind",
                "Key_BackForward",
                "Key_ApplicationLeft",
                "Key_ApplicationRight",
                "Key_Book",
                "Key_CD",
                "Key_Calculator",
                "Key_ToDoList",
                "Key_ClearGrab",
                "Key_Close",
                "Key_Copy",
                "Key_Cut",
                "Key_Display",
                "Key_DOS",
                "Key_Documents",
                "Key_Excel",
                "Key_Explorer",
                "Key_Game",
                "Key_Go",
                "Key_iTouch",
                "Key_LogOff",
                "Key_Market",
                "Key_Meeting",
                "Key_MenuKB",
                "Key_MenuPB",
                "Key_MySites",
                "Key_News",
                "Key_OfficeHome",
                "Key_Option",
                "Key_Paste",
                "Key_Phone",
                "Key_Calendar",
                "Key_Reply",
                "Key_Reload",
                "Key_RotateWindows",
                "Key_RotationPB",
                "Key_RotationKB",
                "Key_Save",
                "Key_Send",
                "Key_Spell",
                "Key_SplitScreen",
                "Key_Support",
                "Key_TaskPane",
                "Key_Terminal",
                "Key_Tools",
                "Key_Travel",
                "Key_Video",
                "Key_Word",
                "Key_Xfer",
                "Key_ZoomIn",
                "Key_ZoomOut",
                "Key_Away",
                "Key_Messenger",
                "Key_WebCam",
                "Key_MailForward",
                "Key_Pictures",
                "Key_Music",
                "Key_Battery",
                "Key_Bluetooth",
                "Key_WLAN",
                "Key_UWB",
                "Key_AudioForward",
                "Key_AudioRepeat",
                "Key_AudioRandomPlay",
                "Key_Subtitle",
                "Key_AudioCycleTrack",
                "Key_Time",
                "Key_Hibernate",
                "Key_View",
                "Key_TopMenu",
                "Key_PowerDown",
                "Key_Suspend",
                "Key_ContrastAdjust",
                "Key_LaunchG",
                "Key_LaunchH",
                "Key_TouchpadToggle",
                "Key_TouchpadOn",
                "Key_TouchpadOff",
                "Key_MicMute",
                "Key_Red",
                "Key_Green",
                "Key_Yellow",
                "Key_Blue",
                "Key_ChannelUp",
                "Key_ChannelDown",
                "Key_Guide",
                "Key_Info",
                "Key_Settings",
                "Key_MicVolumeUp",
                "Key_MicVolumeDown",
                "Key_New",
                "Key_Open",
                "Key_Find",
                "Key_Undo",
                "Key_Redo",
                "Key_MediaLast",
                "Key_Select",
                "Key_Yes",
                "Key_No",
                "Key_Cancel",
                "Key_Printer",
                "Key_Execute",
                "Key_Sleep",
                "Key_Play",
                "Key_Zoom",
                "Key_Exit",
                "Key_Context1",
                "Key_Context2",
                "Key_Context3",
                "Key_Context4",
                "Key_Call",
                "Key_Hangup",
                "Key_Flip",
                "Key_ToggleCallHangup",
                "Key_VoiceDial",
                "Key_LastNumberRedial",
                "Key_Camera",
                "Key_CameraFocus",
                "Key_unknown"
            ]
        }
        Enum {
            name: "KeyboardModifier"
            values: [
                "NoModifier",
                "ShiftModifier",
                "ControlModifier",
                "AltModifier",
                "MetaModifier",
                "KeypadModifier",
                "GroupSwitchModifier",
                "KeyboardModifierMask"
            ]
        }
        Enum {
            name: "KeyboardModifiers"
            alias: "KeyboardModifier"
            isFlag: true
            values: [
                "NoModifier",
                "ShiftModifier",
                "ControlModifier",
                "AltModifier",
                "MetaModifier",
                "KeypadModifier",
                "GroupSwitchModifier",
                "KeyboardModifierMask"
            ]
        }
        Enum {
            name: "Modifier"
            values: ["META", "SHIFT", "CTRL", "ALT", "MODIFIER_MASK"]
        }
        Enum {
            name: "Modifiers"
            alias: "Modifier"
            isFlag: true
            values: ["META", "SHIFT", "CTRL", "ALT", "MODIFIER_MASK"]
        }
        Enum {
            name: "ArrowType"
            values: [
                "NoArrow",
                "UpArrow",
                "DownArrow",
                "LeftArrow",
                "RightArrow"
            ]
        }
        Enum {
            name: "PenStyle"
            values: [
                "NoPen",
                "SolidLine",
                "DashLine",
                "DotLine",
                "DashDotLine",
                "DashDotDotLine",
                "CustomDashLine"
            ]
        }
        Enum {
            name: "PenCapStyle"
            values: ["FlatCap", "SquareCap", "RoundCap", "MPenCapStyle"]
        }
        Enum {
            name: "PenJoinStyle"
            values: [
                "MiterJoin",
                "BevelJoin",
                "RoundJoin",
                "SvgMiterJoin",
                "MPenJoinStyle"
            ]
        }
        Enum {
            name: "BrushStyle"
            values: [
                "NoBrush",
                "SolidPattern",
                "Dense1Pattern",
                "Dense2Pattern",
                "Dense3Pattern",
                "Dense4Pattern",
                "Dense5Pattern",
                "Dense6Pattern",
                "Dense7Pattern",
                "HorPattern",
                "VerPattern",
                "CrossPattern",
                "BDiagPattern",
                "FDiagPattern",
                "DiagCrossPattern",
                "LinearGradientPattern",
                "RadialGradientPattern",
                "ConicalGradientPattern",
                "TexturePattern"
            ]
        }
        Enum {
            name: "SizeMode"
            values: ["AbsoluteSize", "RelativeSize"]
        }
        Enum {
            name: "CursorShape"
            values: [
                "ArrowCursor",
                "UpArrowCursor",
                "CrossCursor",
                "WaitCursor",
                "IBeamCursor",
                "SizeVerCursor",
                "SizeHorCursor",
                "SizeBDiagCursor",
                "SizeFDiagCursor",
                "SizeAllCursor",
                "BlankCursor",
                "SplitVCursor",
                "SplitHCursor",
                "PointingHandCursor",
                "ForbiddenCursor",
                "WhatsThisCursor",
                "BusyCursor",
                "OpenHandCursor",
                "ClosedHandCursor",
                "DragCopyCursor",
                "DragMoveCursor",
                "DragLinkCursor",
                "LastCursor",
                "BitmapCursor",
                "CustomCursor"
            ]
        }
        Enum {
            name: "TextFormat"
            values: ["PlainText", "RichText", "AutoText", "MarkdownText"]
        }
        Enum {
            name: "AspectRatioMode"
            values: [
                "IgnoreAspectRatio",
                "KeepAspectRatio",
                "KeepAspectRatioByExpanding"
            ]
        }
        Enum {
            name: "DockWidgetArea"
            values: [
                "LeftDockWidgetArea",
                "RightDockWidgetArea",
                "TopDockWidgetArea",
                "BottomDockWidgetArea",
                "DockWidgetArea_Mask",
                "AllDockWidgetAreas",
                "NoDockWidgetArea"
            ]
        }
        Enum {
            name: "DockWidgetAreas"
            alias: "DockWidgetArea"
            isFlag: true
            values: [
                "LeftDockWidgetArea",
                "RightDockWidgetArea",
                "TopDockWidgetArea",
                "BottomDockWidgetArea",
                "DockWidgetArea_Mask",
                "AllDockWidgetAreas",
                "NoDockWidgetArea"
            ]
        }
        Enum {
            name: "ToolBarArea"
            values: [
                "LeftToolBarArea",
                "RightToolBarArea",
                "TopToolBarArea",
                "BottomToolBarArea",
                "ToolBarArea_Mask",
                "AllToolBarAreas",
                "NoToolBarArea"
            ]
        }
        Enum {
            name: "ToolBarAreas"
            alias: "ToolBarArea"
            isFlag: true
            values: [
                "LeftToolBarArea",
                "RightToolBarArea",
                "TopToolBarArea",
                "BottomToolBarArea",
                "ToolBarArea_Mask",
                "AllToolBarAreas",
                "NoToolBarArea"
            ]
        }
        Enum {
            name: "DateFormat"
            values: ["TextDate", "ISODate", "RFC2822Date", "ISODateWithMs"]
        }
        Enum {
            name: "TimeSpec"
            values: ["LocalTime", "UTC", "OffsetFromUTC", "TimeZone"]
        }
        Enum {
            name: "DayOfWeek"
            values: [
                "Monday",
                "Tuesday",
                "Wednesday",
                "Thursday",
                "Friday",
                "Saturday",
                "Sunday"
            ]
        }
        Enum {
            name: "ScrollBarPolicy"
            values: [
                "ScrollBarAsNeeded",
                "ScrollBarAlwaysOff",
                "ScrollBarAlwaysOn"
            ]
        }
        Enum {
            name: "CaseSensitivity"
            values: ["CaseInsensitive", "CaseSensitive"]
        }
        Enum {
            name: "Corner"
            values: [
                "TopLeftCorner",
                "TopRightCorner",
                "BottomLeftCorner",
                "BottomRightCorner"
            ]
        }
        Enum {
            name: "Edge"
            values: ["TopEdge", "LeftEdge", "RightEdge", "BottomEdge"]
        }
        Enum {
            name: "Edges"
            alias: "Edge"
            isFlag: true
            values: ["TopEdge", "LeftEdge", "RightEdge", "BottomEdge"]
        }
        Enum {
            name: "ConnectionType"
            values: [
                "AutoConnection",
                "DirectConnection",
                "QueuedConnection",
                "BlockingQueuedConnection",
                "UniqueConnection",
                "SingleShotConnection"
            ]
        }
        Enum {
            name: "ShortcutContext"
            values: [
                "WidgetShortcut",
                "WindowShortcut",
                "ApplicationShortcut",
                "WidgetWithChildrenShortcut"
            ]
        }
        Enum {
            name: "FillRule"
            values: ["OddEvenFill", "WindingFill"]
        }
        Enum {
            name: "MaskMode"
            values: ["MaskInColor", "MaskOutColor"]
        }
        Enum {
            name: "ClipOperation"
            values: ["NoClip", "ReplaceClip", "IntersectClip"]
        }
        Enum {
            name: "ItemSelectionMode"
            values: [
                "ContainsItemShape",
                "IntersectsItemShape",
                "ContainsItemBoundingRect",
                "IntersectsItemBoundingRect"
            ]
        }
        Enum {
            name: "ItemSelectionOperation"
            values: ["ReplaceSelection", "AddToSelection"]
        }
        Enum {
            name: "TransformationMode"
            values: ["FastTransformation", "SmoothTransformation"]
        }
        Enum {
            name: "Axis"
            values: ["XAxis", "YAxis", "ZAxis"]
        }
        Enum {
            name: "FocusReason"
            values: [
                "MouseFocusReason",
                "TabFocusReason",
                "BacktabFocusReason",
                "ActiveWindowFocusReason",
                "PopupFocusReason",
                "ShortcutFocusReason",
                "MenuBarFocusReason",
                "OtherFocusReason",
                "NoFocusReason"
            ]
        }
        Enum {
            name: "ContextMenuPolicy"
            values: [
                "NoContextMenu",
                "DefaultContextMenu",
                "ActionsContextMenu",
                "CustomContextMenu",
                "PreventContextMenu"
            ]
        }
        Enum {
            name: "ContextMenuTrigger"
            isScoped: true
            values: ["Press", "Release"]
        }
        Enum {
            name: "InputMethodQuery"
            values: [
                "ImEnabled",
                "ImCursorRectangle",
                "ImFont",
                "ImCursorPosition",
                "ImSurroundingText",
                "ImCurrentSelection",
                "ImMaximumTextLength",
                "ImAnchorPosition",
                "ImHints",
                "ImPreferredLanguage",
                "ImAbsolutePosition",
                "ImTextBeforeCursor",
                "ImTextAfterCursor",
                "ImEnterKeyType",
                "ImAnchorRectangle",
                "ImInputItemClipRectangle",
                "ImReadOnly",
                "ImPlatformData",
                "ImQueryInput",
                "ImQueryAll"
            ]
        }
        Enum {
            name: "InputMethodQueries"
            alias: "InputMethodQuery"
            isFlag: true
            values: [
                "ImEnabled",
                "ImCursorRectangle",
                "ImFont",
                "ImCursorPosition",
                "ImSurroundingText",
                "ImCurrentSelection",
                "ImMaximumTextLength",
                "ImAnchorPosition",
                "ImHints",
                "ImPreferredLanguage",
                "ImAbsolutePosition",
                "ImTextBeforeCursor",
                "ImTextAfterCursor",
                "ImEnterKeyType",
                "ImAnchorRectangle",
                "ImInputItemClipRectangle",
                "ImReadOnly",
                "ImPlatformData",
                "ImQueryInput",
                "ImQueryAll"
            ]
        }
        Enum {
            name: "InputMethodHint"
            values: [
                "ImhNone",
                "ImhHiddenText",
                "ImhSensitiveData",
                "ImhNoAutoUppercase",
                "ImhPreferNumbers",
                "ImhPreferUppercase",
                "ImhPreferLowercase",
                "ImhNoPredictiveText",
                "ImhDate",
                "ImhTime",
                "ImhPreferLatin",
                "ImhMultiLine",
                "ImhNoEditMenu",
                "ImhNoTextHandles",
                "ImhDigitsOnly",
                "ImhFormattedNumbersOnly",
                "ImhUppercaseOnly",
                "ImhLowercaseOnly",
                "ImhDialableCharactersOnly",
                "ImhEmailCharactersOnly",
                "ImhUrlCharactersOnly",
                "ImhLatinOnly",
                "ImhExclusiveInputMask"
            ]
        }
        Enum {
            name: "InputMethodHints"
            alias: "InputMethodHint"
            isFlag: true
            values: [
                "ImhNone",
                "ImhHiddenText",
                "ImhSensitiveData",
                "ImhNoAutoUppercase",
                "ImhPreferNumbers",
                "ImhPreferUppercase",
                "ImhPreferLowercase",
                "ImhNoPredictiveText",
                "ImhDate",
                "ImhTime",
                "ImhPreferLatin",
                "ImhMultiLine",
                "ImhNoEditMenu",
                "ImhNoTextHandles",
                "ImhDigitsOnly",
                "ImhFormattedNumbersOnly",
                "ImhUppercaseOnly",
                "ImhLowercaseOnly",
                "ImhDialableCharactersOnly",
                "ImhEmailCharactersOnly",
                "ImhUrlCharactersOnly",
                "ImhLatinOnly",
                "ImhExclusiveInputMask"
            ]
        }
        Enum {
            name: "EnterKeyType"
            values: [
                "EnterKeyDefault",
                "EnterKeyReturn",
                "EnterKeyDone",
                "EnterKeyGo",
                "EnterKeySend",
                "EnterKeySearch",
                "EnterKeyNext",
                "EnterKeyPrevious"
            ]
        }
        Enum {
            name: "ToolButtonStyle"
            values: [
                "ToolButtonIconOnly",
                "ToolButtonTextOnly",
                "ToolButtonTextBesideIcon",
                "ToolButtonTextUnderIcon",
                "ToolButtonFollowStyle"
            ]
        }
        Enum {
            name: "LayoutDirection"
            values: ["LeftToRight", "RightToLeft", "LayoutDirectionAuto"]
        }
        Enum {
            name: "DropAction"
            values: [
                "CopyAction",
                "MoveAction",
                "LinkAction",
                "ActionMask",
                "TargetMoveAction",
                "IgnoreAction"
            ]
        }
        Enum {
            name: "DropActions"
            alias: "DropAction"
            isFlag: true
            values: [
                "CopyAction",
                "MoveAction",
                "LinkAction",
                "ActionMask",
                "TargetMoveAction",
                "IgnoreAction"
            ]
        }
        Enum {
            name: "CheckState"
            values: ["Unchecked", "PartiallyChecked", "Checked"]
        }
        Enum {
            name: "ItemDataRole"
            values: [
                "DisplayRole",
                "DecorationRole",
                "EditRole",
                "ToolTipRole",
                "StatusTipRole",
                "WhatsThisRole",
                "FontRole",
                "TextAlignmentRole",
                "BackgroundRole",
                "ForegroundRole",
                "CheckStateRole",
                "AccessibleTextRole",
                "AccessibleDescriptionRole",
                "SizeHintRole",
                "InitialSortOrderRole",
                "DisplayPropertyRole",
                "DecorationPropertyRole",
                "ToolTipPropertyRole",
                "StatusTipPropertyRole",
                "WhatsThisPropertyRole",
                "UserRole"
            ]
        }
        Enum {
            name: "ItemFlags"
            alias: "ItemFlag"
            isFlag: true
            values: [
                "NoItemFlags",
                "ItemIsSelectable",
                "ItemIsEditable",
                "ItemIsDragEnabled",
                "ItemIsDropEnabled",
                "ItemIsUserCheckable",
                "ItemIsEnabled",
                "ItemIsAutoTristate",
                "ItemNeverHasChildren",
                "ItemIsUserTristate"
            ]
        }
        Enum {
            name: "MatchFlags"
            alias: "MatchFlag"
            isFlag: true
            values: [
                "MatchExactly",
                "MatchContains",
                "MatchStartsWith",
                "MatchEndsWith",
                "MatchRegularExpression",
                "MatchWildcard",
                "MatchFixedString",
                "MatchTypeMask",
                "MatchCaseSensitive",
                "MatchWrap",
                "MatchRecursive"
            ]
        }
        Enum {
            name: "WindowModality"
            values: ["NonModal", "WindowModal", "ApplicationModal"]
        }
        Enum {
            name: "TextInteractionFlag"
            values: [
                "NoTextInteraction",
                "TextSelectableByMouse",
                "TextSelectableByKeyboard",
                "LinksAccessibleByMouse",
                "LinksAccessibleByKeyboard",
                "TextEditable",
                "TextEditorInteraction",
                "TextBrowserInteraction"
            ]
        }
        Enum {
            name: "TextInteractionFlags"
            alias: "TextInteractionFlag"
            isFlag: true
            values: [
                "NoTextInteraction",
                "TextSelectableByMouse",
                "TextSelectableByKeyboard",
                "LinksAccessibleByMouse",
                "LinksAccessibleByKeyboard",
                "TextEditable",
                "TextEditorInteraction",
                "TextBrowserInteraction"
            ]
        }
        Enum {
            name: "SizeHint"
            values: [
                "MinimumSize",
                "PreferredSize",
                "MaximumSize",
                "MinimumDescent",
                "NSizeHints"
            ]
        }
        Enum {
            name: "TouchPointStates"
            alias: "TouchPointState"
            isFlag: true
            values: [
                "TouchPointUnknownState",
                "TouchPointPressed",
                "TouchPointMoved",
                "TouchPointStationary",
                "TouchPointReleased"
            ]
        }
        Enum {
            name: "GestureState"
            values: [
                "NoGesture",
                "GestureStarted",
                "GestureUpdated",
                "GestureFinished",
                "GestureCanceled"
            ]
        }
        Enum {
            name: "GestureType"
            values: [
                "TapGesture",
                "TapAndHoldGesture",
                "PanGesture",
                "PinchGesture",
                "SwipeGesture",
                "CustomGesture",
                "LastGestureType"
            ]
        }
        Enum {
            name: "NativeGestureType"
            values: [
                "BeginNativeGesture",
                "EndNativeGesture",
                "PanNativeGesture",
                "ZoomNativeGesture",
                "SmartZoomNativeGesture",
                "RotateNativeGesture",
                "SwipeNativeGesture"
            ]
        }
        Enum {
            name: "CursorMoveStyle"
            values: ["LogicalMoveStyle", "VisualMoveStyle"]
        }
        Enum {
            name: "TimerType"
            values: ["PreciseTimer", "CoarseTimer", "VeryCoarseTimer"]
        }
        Enum {
            name: "TimerId"
            isScoped: true
            values: ["Invalid"]
        }
        Enum {
            name: "ScrollPhase"
            values: [
                "NoScrollPhase",
                "ScrollBegin",
                "ScrollUpdate",
                "ScrollEnd",
                "ScrollMomentum"
            ]
        }
        Enum {
            name: "MouseEventSource"
            values: [
                "MouseEventNotSynthesized",
                "MouseEventSynthesizedBySystem",
                "MouseEventSynthesizedByQt",
                "MouseEventSynthesizedByApplication"
            ]
        }
        Enum {
            name: "MouseEventFlags"
            alias: "MouseEventFlag"
            isFlag: true
            values: [
                "NoMouseEventFlag",
                "MouseEventCreatedDoubleClick",
                "MouseEventFlagMask"
            ]
        }
        Enum {
            name: "ChecksumType"
            values: ["ChecksumIso3309", "ChecksumItuV41"]
        }
        Enum {
            name: "HighDpiScaleFactorRoundingPolicy"
            isScoped: true
            values: [
                "Unset",
                "Round",
                "Ceil",
                "Floor",
                "RoundPreferFloor",
                "PassThrough"
            ]
        }
        Enum {
            name: "PermissionStatus"
            isScoped: true
            values: ["Undetermined", "Granted", "Denied"]
        }
    }
    Component {
        file: "private/qqmlbuiltinfunctions_p.h"
        name: "QtObject"
        accessSemantics: "reference"
        prototype: "QObject"
        extension: "Qt"
        extensionIsNamespace: true
        exports: ["QML/Qt 1.0"]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [256]
        Enum {
            name: "LoadingMode"
            values: ["Asynchronous", "Synchronous"]
        }
        Property {
            name: "application"
            type: "QQmlApplication"
            isPointer: true
            read: "application"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "platform"
            type: "QQmlPlatform"
            isPointer: true
            read: "platform"
            index: 1
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "inputMethod"
            type: "QObject"
            isPointer: true
            read: "inputMethod"
            index: 2
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "styleHints"
            type: "QObject"
            isPointer: true
            read: "styleHints"
            index: 3
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "uiLanguage"
            type: "QString"
            bindable: "uiLanguageBindable"
            read: "uiLanguage"
            write: "setUiLanguage"
            index: 4
        }
        Method {
            name: "include"
            type: "QJSValue"
            isMethodConstant: true
            Parameter { name: "url"; type: "QString" }
            Parameter { name: "callback"; type: "QJSValue" }
        }
        Method {
            name: "include"
            type: "QJSValue"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "url"; type: "QString" }
        }
        Method {
            name: "isQtObject"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "value"; type: "QJSValue" }
        }
        Method {
            name: "color"
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "name"; type: "QString" }
        }
        Method {
            name: "rgba"
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "r"; type: "double" }
            Parameter { name: "g"; type: "double" }
            Parameter { name: "b"; type: "double" }
            Parameter { name: "a"; type: "double" }
        }
        Method {
            name: "rgba"
            type: "QVariant"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "r"; type: "double" }
            Parameter { name: "g"; type: "double" }
            Parameter { name: "b"; type: "double" }
        }
        Method {
            name: "hsla"
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "h"; type: "double" }
            Parameter { name: "s"; type: "double" }
            Parameter { name: "l"; type: "double" }
            Parameter { name: "a"; type: "double" }
        }
        Method {
            name: "hsla"
            type: "QVariant"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "h"; type: "double" }
            Parameter { name: "s"; type: "double" }
            Parameter { name: "l"; type: "double" }
        }
        Method {
            name: "hsva"
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "h"; type: "double" }
            Parameter { name: "s"; type: "double" }
            Parameter { name: "v"; type: "double" }
            Parameter { name: "a"; type: "double" }
        }
        Method {
            name: "hsva"
            type: "QVariant"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "h"; type: "double" }
            Parameter { name: "s"; type: "double" }
            Parameter { name: "v"; type: "double" }
        }
        Method {
            name: "colorEqual"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "lhs"; type: "QVariant" }
            Parameter { name: "rhs"; type: "QVariant" }
        }
        Method {
            name: "rect"
            type: "QRectF"
            isMethodConstant: true
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
            Parameter { name: "width"; type: "double" }
            Parameter { name: "height"; type: "double" }
        }
        Method {
            name: "point"
            type: "QPointF"
            isMethodConstant: true
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method {
            name: "size"
            type: "QSizeF"
            isMethodConstant: true
            Parameter { name: "width"; type: "double" }
            Parameter { name: "height"; type: "double" }
        }
        Method {
            name: "vector2d"
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method {
            name: "vector3d"
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
            Parameter { name: "z"; type: "double" }
        }
        Method {
            name: "vector4d"
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
            Parameter { name: "z"; type: "double" }
            Parameter { name: "w"; type: "double" }
        }
        Method {
            name: "quaternion"
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "scalar"; type: "double" }
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
            Parameter { name: "z"; type: "double" }
        }
        Method { name: "matrix4x4"; type: "QVariant"; isMethodConstant: true }
        Method {
            name: "matrix4x4"
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "m11"; type: "double" }
            Parameter { name: "m12"; type: "double" }
            Parameter { name: "m13"; type: "double" }
            Parameter { name: "m14"; type: "double" }
            Parameter { name: "m21"; type: "double" }
            Parameter { name: "m22"; type: "double" }
            Parameter { name: "m23"; type: "double" }
            Parameter { name: "m24"; type: "double" }
            Parameter { name: "m31"; type: "double" }
            Parameter { name: "m32"; type: "double" }
            Parameter { name: "m33"; type: "double" }
            Parameter { name: "m34"; type: "double" }
            Parameter { name: "m41"; type: "double" }
            Parameter { name: "m42"; type: "double" }
            Parameter { name: "m43"; type: "double" }
            Parameter { name: "m44"; type: "double" }
        }
        Method {
            name: "matrix4x4"
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "value"; type: "QJSValue" }
        }
        Method {
            name: "lighter"
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "color"; type: "QJSValue" }
            Parameter { name: "factor"; type: "double" }
        }
        Method {
            name: "lighter"
            type: "QVariant"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "color"; type: "QJSValue" }
        }
        Method {
            name: "darker"
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "color"; type: "QJSValue" }
            Parameter { name: "factor"; type: "double" }
        }
        Method {
            name: "darker"
            type: "QVariant"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "color"; type: "QJSValue" }
        }
        Method {
            name: "alpha"
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "baseColor"; type: "QJSValue" }
            Parameter { name: "value"; type: "double" }
        }
        Method {
            name: "tint"
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "baseColor"; type: "QJSValue" }
            Parameter { name: "tintColor"; type: "QJSValue" }
        }
        Method {
            name: "formatDate"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "date"; type: "QDate" }
            Parameter { name: "format"; type: "QString" }
        }
        Method {
            name: "formatDate"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "dateTime"; type: "QDateTime" }
            Parameter { name: "format"; type: "QString" }
        }
        Method {
            name: "formatDate"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "string"; type: "QString" }
            Parameter { name: "format"; type: "QString" }
        }
        Method {
            name: "formatDate"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "date"; type: "QDate" }
            Parameter { name: "format"; type: "Qt::DateFormat" }
        }
        Method {
            name: "formatDate"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "dateTime"; type: "QDateTime" }
            Parameter { name: "format"; type: "Qt::DateFormat" }
        }
        Method {
            name: "formatDate"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "string"; type: "QString" }
            Parameter { name: "format"; type: "Qt::DateFormat" }
        }
        Method {
            name: "formatTime"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "time"; type: "QTime" }
            Parameter { name: "format"; type: "QString" }
        }
        Method {
            name: "formatTime"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "dateTime"; type: "QDateTime" }
            Parameter { name: "format"; type: "QString" }
        }
        Method {
            name: "formatTime"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "time"; type: "QString" }
            Parameter { name: "format"; type: "QString" }
        }
        Method {
            name: "formatTime"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "time"; type: "QTime" }
            Parameter { name: "format"; type: "Qt::DateFormat" }
        }
        Method {
            name: "formatTime"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "dateTime"; type: "QDateTime" }
            Parameter { name: "format"; type: "Qt::DateFormat" }
        }
        Method {
            name: "formatTime"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "time"; type: "QString" }
            Parameter { name: "format"; type: "Qt::DateFormat" }
        }
        Method {
            name: "formatDateTime"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "date"; type: "QDateTime" }
            Parameter { name: "format"; type: "QString" }
        }
        Method {
            name: "formatDateTime"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "string"; type: "QString" }
            Parameter { name: "format"; type: "QString" }
        }
        Method {
            name: "formatDateTime"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "date"; type: "QDateTime" }
            Parameter { name: "format"; type: "Qt::DateFormat" }
        }
        Method {
            name: "formatDateTime"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "string"; type: "QString" }
            Parameter { name: "format"; type: "Qt::DateFormat" }
        }
        Method {
            name: "formatDate"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "date"; type: "QDate" }
            Parameter { name: "locale"; type: "QLocale" }
            Parameter { name: "formatType"; type: "QLocale::FormatType" }
        }
        Method {
            name: "formatDate"
            type: "QString"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "date"; type: "QDate" }
            Parameter { name: "locale"; type: "QLocale" }
        }
        Method {
            name: "formatDate"
            type: "QString"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "date"; type: "QDate" }
        }
        Method {
            name: "formatDate"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "dateTime"; type: "QDateTime" }
            Parameter { name: "locale"; type: "QLocale" }
            Parameter { name: "formatType"; type: "QLocale::FormatType" }
        }
        Method {
            name: "formatDate"
            type: "QString"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "dateTime"; type: "QDateTime" }
            Parameter { name: "locale"; type: "QLocale" }
        }
        Method {
            name: "formatDate"
            type: "QString"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "dateTime"; type: "QDateTime" }
        }
        Method {
            name: "formatDate"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "string"; type: "QString" }
            Parameter { name: "locale"; type: "QLocale" }
            Parameter { name: "formatType"; type: "QLocale::FormatType" }
        }
        Method {
            name: "formatDate"
            type: "QString"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "string"; type: "QString" }
            Parameter { name: "locale"; type: "QLocale" }
        }
        Method {
            name: "formatDate"
            type: "QString"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "string"; type: "QString" }
        }
        Method {
            name: "formatTime"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "time"; type: "QTime" }
            Parameter { name: "locale"; type: "QLocale" }
            Parameter { name: "formatType"; type: "QLocale::FormatType" }
        }
        Method {
            name: "formatTime"
            type: "QString"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "time"; type: "QTime" }
            Parameter { name: "locale"; type: "QLocale" }
        }
        Method {
            name: "formatTime"
            type: "QString"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "time"; type: "QTime" }
        }
        Method {
            name: "formatTime"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "dateTime"; type: "QDateTime" }
            Parameter { name: "locale"; type: "QLocale" }
            Parameter { name: "formatType"; type: "QLocale::FormatType" }
        }
        Method {
            name: "formatTime"
            type: "QString"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "dateTime"; type: "QDateTime" }
            Parameter { name: "locale"; type: "QLocale" }
        }
        Method {
            name: "formatTime"
            type: "QString"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "dateTime"; type: "QDateTime" }
        }
        Method {
            name: "formatTime"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "time"; type: "QString" }
            Parameter { name: "locale"; type: "QLocale" }
            Parameter { name: "formatType"; type: "QLocale::FormatType" }
        }
        Method {
            name: "formatTime"
            type: "QString"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "time"; type: "QString" }
            Parameter { name: "locale"; type: "QLocale" }
        }
        Method {
            name: "formatTime"
            type: "QString"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "time"; type: "QString" }
        }
        Method {
            name: "formatDateTime"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "date"; type: "QDateTime" }
            Parameter { name: "locale"; type: "QLocale" }
            Parameter { name: "formatType"; type: "QLocale::FormatType" }
        }
        Method {
            name: "formatDateTime"
            type: "QString"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "date"; type: "QDateTime" }
            Parameter { name: "locale"; type: "QLocale" }
        }
        Method {
            name: "formatDateTime"
            type: "QString"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "date"; type: "QDateTime" }
        }
        Method {
            name: "formatDateTime"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "string"; type: "QString" }
            Parameter { name: "locale"; type: "QLocale" }
            Parameter { name: "formatType"; type: "QLocale::FormatType" }
        }
        Method {
            name: "formatDateTime"
            type: "QString"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "string"; type: "QString" }
            Parameter { name: "locale"; type: "QLocale" }
        }
        Method {
            name: "formatDateTime"
            type: "QString"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "string"; type: "QString" }
        }
        Method { name: "locale"; type: "QLocale"; isMethodConstant: true }
        Method {
            name: "locale"
            type: "QLocale"
            isMethodConstant: true
            Parameter { name: "name"; type: "QString" }
        }
        Method {
            name: "url"
            type: "QUrl"
            isMethodConstant: true
            Parameter { name: "url"; type: "QUrl" }
        }
        Method {
            name: "resolvedUrl"
            type: "QUrl"
            isMethodConstant: true
            Parameter { name: "url"; type: "QUrl" }
        }
        Method {
            name: "resolvedUrl"
            type: "QUrl"
            isMethodConstant: true
            Parameter { name: "url"; type: "QUrl" }
            Parameter { name: "context"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "openUrlExternally"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "url"; type: "QUrl" }
        }
        Method {
            name: "font"
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "fontSpecifier"; type: "QJSValue" }
        }
        Method { name: "fontFamilies"; type: "QStringList"; isMethodConstant: true }
        Method {
            name: "md5"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "data"; type: "QString" }
        }
        Method {
            name: "btoa"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "data"; type: "QString" }
        }
        Method {
            name: "atob"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "data"; type: "QString" }
        }
        Method { name: "quit"; isMethodConstant: true }
        Method {
            name: "exit"
            isMethodConstant: true
            Parameter { name: "retCode"; type: "int" }
        }
        Method {
            name: "createQmlObject"
            type: "QObject"
            isPointer: true
            isMethodConstant: true
            Parameter { name: "qml"; type: "QString" }
            Parameter { name: "parent"; type: "QObject"; isPointer: true }
            Parameter { name: "url"; type: "QUrl" }
        }
        Method {
            name: "createQmlObject"
            type: "QObject"
            isPointer: true
            isCloned: true
            isMethodConstant: true
            Parameter { name: "qml"; type: "QString" }
            Parameter { name: "parent"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "createComponent"
            type: "QQmlComponent"
            isPointer: true
            isMethodConstant: true
            Parameter { name: "url"; type: "QUrl" }
            Parameter { name: "parent"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "createComponent"
            type: "QQmlComponent"
            isPointer: true
            isMethodConstant: true
            Parameter { name: "url"; type: "QUrl" }
            Parameter { name: "mode"; type: "QQmlComponent::CompilationMode" }
            Parameter { name: "parent"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "createComponent"
            type: "QQmlComponent"
            isPointer: true
            isCloned: true
            isMethodConstant: true
            Parameter { name: "url"; type: "QUrl" }
            Parameter { name: "mode"; type: "QQmlComponent::CompilationMode" }
        }
        Method {
            name: "createComponent"
            type: "QQmlComponent"
            isPointer: true
            isCloned: true
            isMethodConstant: true
            Parameter { name: "url"; type: "QUrl" }
        }
        Method {
            name: "createComponent"
            type: "QQmlComponent"
            isPointer: true
            isMethodConstant: true
            Parameter { name: "moduleUri"; type: "QString" }
            Parameter { name: "typeName"; type: "QString" }
            Parameter { name: "parent"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "createComponent"
            type: "QQmlComponent"
            isPointer: true
            isMethodConstant: true
            Parameter { name: "moduleUri"; type: "QString" }
            Parameter { name: "typeName"; type: "QString" }
            Parameter { name: "mode"; type: "QQmlComponent::CompilationMode" }
            Parameter { name: "parent"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "createComponent"
            type: "QQmlComponent"
            isPointer: true
            isCloned: true
            isMethodConstant: true
            Parameter { name: "moduleUri"; type: "QString" }
            Parameter { name: "typeName"; type: "QString" }
            Parameter { name: "mode"; type: "QQmlComponent::CompilationMode" }
        }
        Method {
            name: "createComponent"
            type: "QQmlComponent"
            isPointer: true
            isCloned: true
            isMethodConstant: true
            Parameter { name: "moduleUri"; type: "QString" }
            Parameter { name: "typeName"; type: "QString" }
        }
        Method {
            name: "binding"
            type: "QJSValue"
            isMethodConstant: true
            Parameter { name: "function"; type: "QJSValue" }
        }
        Method { name: "callLater"; isJavaScriptFunction: true }
    }
}
