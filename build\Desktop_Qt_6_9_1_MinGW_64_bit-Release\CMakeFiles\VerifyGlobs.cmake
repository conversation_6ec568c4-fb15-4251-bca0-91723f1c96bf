# CMAKE generated file: DO NOT EDIT!
# Generated by CMake Version 3.30
cmake_policy(SET CMP0009 NEW)

# SOURCE_FILES at backend/CMakeLists.txt:20 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/Qt/file/palyer/backend/*.cpp")
set(OLD_GLOB
  "C:/Qt/file/palyer/backend/ConversionTaskRunnable.cpp"
  "C:/Qt/file/palyer/backend/ImageConversionManager.cpp"
  "C:/Qt/file/palyer/backend/ImageProcessor.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/CMakeFiles/cmake.verify_globs")
endif()

# HEADER_FILES at backend/CMakeLists.txt:19 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/Qt/file/palyer/backend/*.h")
set(OLD_GLOB
  "C:/Qt/file/palyer/backend/ConversionTaskRunnable.h"
  "C:/Qt/file/palyer/backend/ImageConversionManager.h"
  "C:/Qt/file/palyer/backend/ImageProcessor.h"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/CMakeFiles/cmake.verify_globs")
endif()

# SOURCE_FILES at backend/CMakeLists.txt:20 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/Qt/file/palyer/backend/*/*.cpp")
set(OLD_GLOB
  "C:/Qt/file/palyer/backend/server/ClientMode.cpp"
  "C:/Qt/file/palyer/backend/server/ServerMode.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/CMakeFiles/cmake.verify_globs")
endif()

# HEADER_FILES at backend/CMakeLists.txt:19 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "C:/Qt/file/palyer/backend/*/*.h")
set(OLD_GLOB
  "C:/Qt/file/palyer/backend/server/ClientMode.h"
  "C:/Qt/file/palyer/backend/server/ServerMode.h"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/CMakeFiles/cmake.verify_globs")
endif()
