[{"moduleFiles": [{"entries": [{"codegenSuccessful": true, "column": 12, "durationMicroseconds": 1875, "errorMessage": "", "functionName": "width", "line": 12}, {"codegenSuccessful": true, "column": 34, "durationMicroseconds": 55, "errorMessage": "", "functionName": "height", "line": 12}, {"codegenSuccessful": false, "column": 34, "durationMicroseconds": 39, "errorMessage": "Cannot generate efficient code for storing an array in a non-sequence type", "functionName": "previewWindows", "line": 17}, {"codegenSuccessful": false, "column": 59, "durationMicroseconds": 105, "errorMessage": "Cannot generate efficient code for storing an array in a non-sequence type", "functionName": "sortModeNames", "line": 18}, {"codegenSuccessful": false, "column": 27, "durationMicroseconds": 1059, "errorMessage": "Cannot generate efficient code for MoveRegExp", "functionName": "onCurrentP<PERSON><PERSON><PERSON><PERSON>", "line": 24}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "getBreadcrumbParts", "line": 454}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 288, "errorMessage": "Cannot access value for name imageProcessor", "functionName": "loadImagesFromFolder", "line": 478}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "openImagePreview", "line": 488}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "removePreviewWindow", "line": 507}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "showFileNameTip", "line": 518}, {"codegenSuccessful": true, "column": 5, "durationMicroseconds": 269, "errorMessage": "", "functionName": "showMaxWindowsMessage", "line": 525}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "_copyPathToClipboard", "line": 527}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "showCopyTip", "line": 534}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "openConversionWindowWithFormat", "line": 541}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 468, "errorMessage": "Cannot access value for name imageProcessor", "functionName": "requestVisibleThumbnails", "line": 565}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "showSortTip", "line": 578}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 147, "errorMessage": "Type call to method createObject, returning QObject does not have a property visible for writing", "functionName": "smartCompress", "line": 582}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 110, "errorMessage": "Type call to method createObject, returning QObject does not have a property visible for writing", "functionName": "av1VideoConvert", "line": 601}, {"codegenSuccessful": true, "column": 16, "durationMicroseconds": 118, "errorMessage": "", "functionName": "width", "line": 34}, {"codegenSuccessful": false, "column": 16, "durationMicroseconds": 112, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 35}, {"codegenSuccessful": true, "column": 48, "durationMicroseconds": 47, "errorMessage": "", "functionName": "visible", "line": 35}, {"codegenSuccessful": true, "column": 62, "durationMicroseconds": 22, "errorMessage": "", "functionName": "centerIn", "line": 37}, {"codegenSuccessful": true, "column": 59, "durationMicroseconds": 54, "errorMessage": "", "functionName": "onTriggered", "line": 38}, {"codegenSuccessful": true, "column": 16, "durationMicroseconds": 61, "errorMessage": "", "functionName": "width", "line": 43}, {"codegenSuccessful": true, "column": 45, "durationMicroseconds": 83, "errorMessage": "", "functionName": "height", "line": 43}, {"codegenSuccessful": false, "column": 16, "durationMicroseconds": 80, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 44}, {"codegenSuccessful": true, "column": 55, "durationMicroseconds": 44, "errorMessage": "", "functionName": "visible", "line": 44}, {"codegenSuccessful": true, "column": 24, "durationMicroseconds": 324, "errorMessage": "", "functionName": "onTextChanged", "line": 49}, {"codegenSuccessful": false, "column": 23, "durationMicroseconds": 81, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 45}, {"codegenSuccessful": true, "column": 48, "durationMicroseconds": 21, "errorMessage": "", "functionName": "centerIn", "line": 47}, {"codegenSuccessful": true, "column": 16, "durationMicroseconds": 52, "errorMessage": "", "functionName": "width", "line": 57}, {"codegenSuccessful": false, "column": 70, "durationMicroseconds": 93, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 57}, {"codegenSuccessful": true, "column": 37, "durationMicroseconds": 42, "errorMessage": "", "functionName": "horizontalCenter", "line": 58}, {"codegenSuccessful": true, "column": 70, "durationMicroseconds": 38, "errorMessage": "", "functionName": "bottom", "line": 58}, {"codegenSuccessful": true, "column": 51, "durationMicroseconds": 19, "errorMessage": "", "functionName": "centerIn", "line": 60}, {"codegenSuccessful": true, "column": 64, "durationMicroseconds": 46, "errorMessage": "", "functionName": "onTriggered", "line": 61}, {"codegenSuccessful": true, "column": 23, "durationMicroseconds": 22, "errorMessage": "", "functionName": "fill", "line": 67}, {"codegenSuccessful": true, "column": 28, "durationMicroseconds": 45, "errorMessage": "", "functionName": "onClicked", "line": 75}, {"codegenSuccessful": true, "column": 33, "durationMicroseconds": 48, "errorMessage": "", "functionName": "left", "line": 73}, {"codegenSuccessful": true, "column": 77, "durationMicroseconds": 42, "errorMessage": "", "functionName": "verticalCenter", "line": 73}, {"codegenSuccessful": true, "column": 25, "durationMicroseconds": 74, "errorMessage": "", "functionName": "height", "line": 80}, {"codegenSuccessful": true, "column": 33, "durationMicroseconds": 40, "errorMessage": "", "functionName": "left", "line": 79}, {"codegenSuccessful": true, "column": 69, "durationMicroseconds": 36, "errorMessage": "", "functionName": "right", "line": 79}, {"codegenSuccessful": true, "column": 116, "durationMicroseconds": 35, "errorMessage": "", "functionName": "verticalCenter", "line": 79}, {"codegenSuccessful": true, "column": 45, "durationMicroseconds": 38, "errorMessage": "", "functionName": "verticalCenter", "line": 82}, {"codegenSuccessful": false, "column": 32, "durationMicroseconds": 141, "errorMessage": "Cannot access value for name fullPath", "functionName": "model", "line": 85}, {"codegenSuccessful": false, "column": 39, "durationMicroseconds": 154, "errorMessage": "Cannot access value for name modelData", "functionName": "text", "line": 88}, {"codegenSuccessful": false, "column": 44, "durationMicroseconds": 138, "errorMessage": "Cannot access value for name modelData", "functionName": "bold", "line": 89}, {"codegenSuccessful": false, "column": 56, "durationMicroseconds": 275, "errorMessage": "Cannot access value for name index", "functionName": "onCompleted", "line": 90}, {"codegenSuccessful": true, "column": 72, "durationMicroseconds": 42, "errorMessage": "", "functionName": "cursor<PERSON><PERSON>pe", "line": 92}, {"codegenSuccessful": true, "column": 112, "durationMicroseconds": 95, "errorMessage": "", "functionName": "acceptedButtons", "line": 92}, {"codegenSuccessful": false, "column": 48, "durationMicroseconds": 822, "errorMessage": "Cannot access value for name modelData", "functionName": "onClicked", "line": 93}, {"codegenSuccessful": true, "column": 48, "durationMicroseconds": 21, "errorMessage": "", "functionName": "onClicked", "line": 93}, {"codegenSuccessful": true, "column": 51, "durationMicroseconds": 26, "errorMessage": "", "functionName": "fill", "line": 92}, {"codegenSuccessful": false, "column": 110, "durationMicroseconds": 133, "errorMessage": "Cannot access value for name index", "functionName": "visible", "line": 108}, {"codegenSuccessful": false, "column": 63, "durationMicroseconds": 89, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 114}, {"codegenSuccessful": false, "column": 69, "durationMicroseconds": 149, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 117}, {"codegenSuccessful": true, "column": 31, "durationMicroseconds": 40, "errorMessage": "", "functionName": "contentWidth", "line": 122}, {"codegenSuccessful": true, "column": 63, "durationMicroseconds": 32, "errorMessage": "", "functionName": "contentHeight", "line": 122}, {"codegenSuccessful": true, "column": 44, "durationMicroseconds": 264, "errorMessage": "", "functionName": "isScrolling", "line": 123}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 182, "errorMessage": "", "functionName": "onIsScrollingChanged", "line": 125}, {"codegenSuccessful": false, "column": 36, "durationMicroseconds": 269, "errorMessage": "method requestVisibleThumbnails cannot be resolved.", "functionName": "onContentYChanged", "line": 126}, {"codegenSuccessful": false, "column": 81, "durationMicroseconds": 230, "errorMessage": "method requestVisibleThumbnails cannot be resolved.", "functionName": "onHeightChanged", "line": 126}, {"codegenSuccessful": false, "column": 125, "durationMicroseconds": 227, "errorMessage": "method requestVisibleThumbnails cannot be resolved.", "functionName": "onWidthChanged", "line": 126}, {"codegenSuccessful": true, "column": 31, "durationMicroseconds": 32, "errorMessage": "", "functionName": "fill", "line": 120}, {"codegenSuccessful": true, "column": 75, "durationMicroseconds": 133, "errorMessage": "", "functionName": "onTriggered", "line": 124}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 40, "errorMessage": "", "functionName": "policy", "line": 128}, {"codegenSuccessful": true, "column": 30, "durationMicroseconds": 72, "errorMessage": "", "functionName": "visible", "line": 129}, {"codegenSuccessful": true, "column": 40, "durationMicroseconds": 48, "errorMessage": "", "functionName": "onEntered", "line": 136}, {"codegenSuccessful": true, "column": 70, "durationMicroseconds": 90, "errorMessage": "", "functionName": "onExited", "line": 136}, {"codegenSuccessful": true, "column": 59, "durationMicroseconds": 23, "errorMessage": "", "functionName": "fill", "line": 135}, {"codegenSuccessful": true, "column": 72, "durationMicroseconds": 46, "errorMessage": "", "functionName": "radius", "line": 139}, {"codegenSuccessful": false, "column": 90, "durationMicroseconds": 62, "errorMessage": "Cannot load property pressed from (component in C:/Qt/file/palyer/compoment/imagePage.qml)::parent with type QQuickItem.", "functionName": "color", "line": 139}, {"codegenSuccessful": true, "column": 28, "durationMicroseconds": 151, "errorMessage": "", "functionName": "width", "line": 143}, {"codegenSuccessful": true, "column": 30, "durationMicroseconds": 132, "errorMessage": "", "functionName": "columns", "line": 145}, {"codegenSuccessful": false, "column": 39, "durationMicroseconds": 105, "errorMessage": "Cannot access value for name imageProcessor", "functionName": "model", "line": 147}, {"codegenSuccessful": false, "column": 60, "durationMicroseconds": 111, "errorMessage": "Cannot access value for name imageProcessor", "functionName": "currentSource", "line": 151}, {"codegenSuccessful": false, "column": 36, "durationMicroseconds": 153, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 155}, {"codegenSuccessful": false, "column": 52, "durationMicroseconds": 63, "errorMessage": "Cannot access value for name imageGrid", "functionName": "preferredWidth", "line": 153}, {"codegenSuccessful": true, "column": 53, "durationMicroseconds": 31, "errorMessage": "", "functionName": "preferredHeight", "line": 154}, {"codegenSuccessful": true, "column": 47, "durationMicroseconds": 21, "errorMessage": "", "functionName": "fill", "line": 158}, {"codegenSuccessful": false, "column": 45, "durationMicroseconds": 44, "errorMessage": "Cannot load property fileNameHeight from (component in C:/Qt/file/palyer/compoment/imagePage.qml)::parent with type QQuickItem.", "functionName": "height", "line": 163}, {"codegenSuccessful": false, "column": 46, "durationMicroseconds": 72, "errorMessage": "Cannot access value for name model", "functionName": "opacity", "line": 164}, {"codegenSuccessful": true, "column": 52, "durationMicroseconds": 44, "errorMessage": "", "functionName": "top", "line": 162}, {"codegenSuccessful": true, "column": 70, "durationMicroseconds": 38, "errorMessage": "", "functionName": "left", "line": 162}, {"codegenSuccessful": true, "column": 90, "durationMicroseconds": 38, "errorMessage": "", "functionName": "right", "line": 162}, {"codegenSuccessful": false, "column": 50, "durationMicroseconds": 22, "errorMessage": "Cannot access value for name model", "functionName": "visible", "line": 169}, {"codegenSuccessful": true, "column": 105, "durationMicroseconds": 69, "errorMessage": "", "functionName": "centerIn", "line": 169}, {"codegenSuccessful": true, "column": 57, "durationMicroseconds": 83, "errorMessage": "", "functionName": "pixelSize", "line": 170}, {"codegenSuccessful": false, "column": 56, "durationMicroseconds": 220, "errorMessage": "Cannot access value for name fileNameTip", "functionName": "onEntered", "line": 176}, {"codegenSuccessful": false, "column": 55, "durationMicroseconds": 21, "errorMessage": "Cannot access value for name fileNameTip", "functionName": "onExited", "line": 185}, {"codegenSuccessful": false, "column": 64, "durationMicroseconds": 209, "errorMessage": "Cannot access value for name fileNameTip", "functionName": "onPositionChanged", "line": 189}, {"codegenSuccessful": true, "column": 56, "durationMicroseconds": 31, "errorMessage": "", "functionName": "onClicked", "line": 197}, {"codegenSuccessful": true, "column": 56, "durationMicroseconds": 18, "errorMessage": "", "functionName": "onClicked", "line": 197}, {"codegenSuccessful": true, "column": 59, "durationMicroseconds": 23, "errorMessage": "", "functionName": "fill", "line": 174}, {"codegenSuccessful": true, "column": 51, "durationMicroseconds": 33, "errorMessage": "", "functionName": "fillMode", "line": 207}, {"codegenSuccessful": true, "column": 49, "durationMicroseconds": 56, "errorMessage": "", "functionName": "source", "line": 208}, {"codegenSuccessful": false, "column": 50, "durationMicroseconds": 20, "errorMessage": "Cannot access value for name model", "functionName": "visible", "line": 209}, {"codegenSuccessful": true, "column": 48, "durationMicroseconds": 62, "errorMessage": "", "functionName": "scale", "line": 210}, {"codegenSuccessful": true, "column": 58, "durationMicroseconds": 255, "errorMessage": "", "functionName": "onStatusChanged", "line": 213}, {"codegenSuccessful": true, "column": 55, "durationMicroseconds": 23, "errorMessage": "", "functionName": "fill", "line": 206}, {"codegenSuccessful": true, "column": 107, "durationMicroseconds": 32, "errorMessage": "", "functionName": "type", "line": 211}, {"codegenSuccessful": false, "column": 54, "durationMicroseconds": 290, "errorMessage": "Cannot access value for name imageProcessor", "functionName": "running", "line": 223}, {"codegenSuccessful": true, "column": 54, "durationMicroseconds": 25, "errorMessage": "", "functionName": "visible", "line": 224}, {"codegenSuccessful": true, "column": 63, "durationMicroseconds": 21, "errorMessage": "", "functionName": "centerIn", "line": 222}, {"codegenSuccessful": true, "column": 55, "durationMicroseconds": 19, "errorMessage": "", "functionName": "fill", "line": 230}, {"codegenSuccessful": true, "column": 63, "durationMicroseconds": 20, "errorMessage": "", "functionName": "centerIn", "line": 234}, {"codegenSuccessful": false, "column": 58, "durationMicroseconds": 498, "errorMessage": "Cannot access value for name model", "functionName": "onDoubleClicked", "line": 244}, {"codegenSuccessful": true, "column": 55, "durationMicroseconds": 28, "errorMessage": "", "functionName": "fill", "line": 243}, {"codegenSuccessful": false, "column": 45, "durationMicroseconds": 35, "errorMessage": "Cannot load property fileNameHeight from (component in C:/Qt/file/palyer/compoment/imagePage.qml)::parent with type QQuickItem.", "functionName": "height", "line": 262}, {"codegenSuccessful": false, "column": 75, "durationMicroseconds": 95, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 262}, {"codegenSuccessful": true, "column": 53, "durationMicroseconds": 42, "errorMessage": "", "functionName": "left", "line": 261}, {"codegenSuccessful": true, "column": 73, "durationMicroseconds": 38, "errorMessage": "", "functionName": "right", "line": 261}, {"codegenSuccessful": true, "column": 95, "durationMicroseconds": 38, "errorMessage": "", "functionName": "bottom", "line": 261}, {"codegenSuccessful": false, "column": 47, "durationMicroseconds": 20, "errorMessage": "Cannot access value for name model", "functionName": "text", "line": 266}, {"codegenSuccessful": true, "column": 60, "durationMicroseconds": 31, "errorMessage": "", "functionName": "verticalAlignment", "line": 268}, {"codegenSuccessful": true, "column": 62, "durationMicroseconds": 178, "errorMessage": "", "functionName": "horizontalAlignment", "line": 269}, {"codegenSuccessful": true, "column": 53, "durationMicroseconds": 228, "errorMessage": "", "functionName": "textFormat", "line": 270}, {"codegenSuccessful": true, "column": 51, "durationMicroseconds": 29, "errorMessage": "", "functionName": "wrapMode", "line": 271}, {"codegenSuccessful": true, "column": 55, "durationMicroseconds": 21, "errorMessage": "", "functionName": "fill", "line": 265}, {"codegenSuccessful": true, "column": 51, "durationMicroseconds": 48, "errorMessage": "", "functionName": "font", "line": 276}, {"codegenSuccessful": true, "column": 51, "durationMicroseconds": 31, "errorMessage": "", "functionName": "text", "line": 277}, {"codegenSuccessful": true, "column": 62, "durationMicroseconds": 98, "errorMessage": "", "functionName": "acceptedButtons", "line": 283}, {"codegenSuccessful": false, "column": 56, "durationMicroseconds": 231, "errorMessage": "Cannot access value for name fileNameTip", "functionName": "onEntered", "line": 286}, {"codegenSuccessful": false, "column": 55, "durationMicroseconds": 20, "errorMessage": "Cannot access value for name fileNameTip", "functionName": "onExited", "line": 295}, {"codegenSuccessful": false, "column": 64, "durationMicroseconds": 208, "errorMessage": "Cannot access value for name fileNameTip", "functionName": "onPositionChanged", "line": 298}, {"codegenSuccessful": true, "column": 56, "durationMicroseconds": 29, "errorMessage": "", "functionName": "onClicked", "line": 306}, {"codegenSuccessful": true, "column": 56, "durationMicroseconds": 18, "errorMessage": "", "functionName": "onClicked", "line": 306}, {"codegenSuccessful": true, "column": 59, "durationMicroseconds": 22, "errorMessage": "", "functionName": "fill", "line": 282}, {"codegenSuccessful": false, "column": 41, "durationMicroseconds": 38, "errorMessage": "Cannot access value for name imageProcessor", "functionName": "target", "line": 314}, {"codegenSuccessful": false, "column": 33, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "onThumbnailReady", "line": 315}, {"codegenSuccessful": false, "column": 64, "durationMicroseconds": 159, "errorMessage": "Cannot access value for name imageProcessor", "functionName": "running", "line": 329}, {"codegenSuccessful": true, "column": 100, "durationMicroseconds": 22, "errorMessage": "", "functionName": "visible", "line": 329}, {"codegenSuccessful": true, "column": 47, "durationMicroseconds": 67, "errorMessage": "", "functionName": "centerIn", "line": 329}, {"codegenSuccessful": true, "column": 16, "durationMicroseconds": 49, "errorMessage": "", "functionName": "width", "line": 335}, {"codegenSuccessful": false, "column": 16, "durationMicroseconds": 163, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 336}, {"codegenSuccessful": true, "column": 75, "durationMicroseconds": 44, "errorMessage": "", "functionName": "visible", "line": 336}, {"codegenSuccessful": true, "column": 37, "durationMicroseconds": 39, "errorMessage": "", "functionName": "horizontalCenter", "line": 337}, {"codegenSuccessful": true, "column": 70, "durationMicroseconds": 36, "errorMessage": "", "functionName": "bottom", "line": 337}, {"codegenSuccessful": true, "column": 99, "durationMicroseconds": 44, "errorMessage": "", "functionName": "bottom<PERSON>argin", "line": 337}, {"codegenSuccessful": true, "column": 77, "durationMicroseconds": 28, "errorMessage": "", "functionName": "type", "line": 338}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 40, "errorMessage": "", "functionName": "verticalCenter", "line": 341}, {"codegenSuccessful": true, "column": 68, "durationMicroseconds": 37, "errorMessage": "", "functionName": "left", "line": 341}, {"codegenSuccessful": false, "column": 30, "durationMicroseconds": 13, "errorMessage": "Cannot access value for name icon", "functionName": "text", "line": 350}, {"codegenSuccessful": true, "column": 92, "durationMicroseconds": 20, "errorMessage": "", "functionName": "centerIn", "line": 350}, {"codegenSuccessful": false, "column": 50, "durationMicroseconds": 86, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 352}, {"codegenSuccessful": true, "column": 35, "durationMicroseconds": 68, "errorMessage": "", "functionName": "fill", "line": 352}, {"codegenSuccessful": true, "column": 89, "durationMicroseconds": 36, "errorMessage": "", "functionName": "type", "line": 353}, {"codegenSuccessful": false, "column": 32, "durationMicroseconds": 152, "errorMessage": "Cannot access value for name toolTip", "functionName": "onEntered", "line": 357}, {"codegenSuccessful": false, "column": 31, "durationMicroseconds": 32, "errorMessage": "Cannot access value for name toolTip", "functionName": "onExited", "line": 363}, {"codegenSuccessful": false, "column": 40, "durationMicroseconds": 126, "errorMessage": "Cannot access value for name toolTip", "functionName": "onPositionChanged", "line": 364}, {"codegenSuccessful": false, "column": 32, "durationMicroseconds": 45, "errorMessage": "Type (component in C:/Qt/file/palyer/compoment/imagePage.qml)::parent with type QQuickItem does not have a property clicked for calling", "functionName": "onClicked", "line": 370}, {"codegenSuccessful": true, "column": 55, "durationMicroseconds": 22, "errorMessage": "", "functionName": "fill", "line": 356}, {"codegenSuccessful": false, "column": 72, "durationMicroseconds": 147, "errorMessage": "Cannot access value for name currentPath", "functionName": "onClicked", "line": 374}, {"codegenSuccessful": false, "column": 73, "durationMicroseconds": 139, "errorMessage": "Cannot access value for name currentPath", "functionName": "onClicked", "line": 375}, {"codegenSuccessful": false, "column": 69, "durationMicroseconds": 76, "errorMessage": "method smartCompress cannot be resolved.", "functionName": "onClicked", "line": 376}, {"codegenSuccessful": false, "column": 72, "durationMicroseconds": 80, "errorMessage": "method av1VideoConvert cannot be resolved.", "functionName": "onClicked", "line": 377}, {"codegenSuccessful": true, "column": 16, "durationMicroseconds": 56, "errorMessage": "", "functionName": "width", "line": 383}, {"codegenSuccessful": true, "column": 48, "durationMicroseconds": 49, "errorMessage": "", "functionName": "height", "line": 383}, {"codegenSuccessful": false, "column": 16, "durationMicroseconds": 80, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 384}, {"codegenSuccessful": true, "column": 54, "durationMicroseconds": 88, "errorMessage": "", "functionName": "visible", "line": 384}, {"codegenSuccessful": true, "column": 77, "durationMicroseconds": 30, "errorMessage": "", "functionName": "type", "line": 385}, {"codegenSuccessful": true, "column": 23, "durationMicroseconds": 28, "errorMessage": "", "functionName": "wrapMode", "line": 392}, {"codegenSuccessful": true, "column": 31, "durationMicroseconds": 20, "errorMessage": "", "functionName": "centerIn", "line": 389}, {"codegenSuccessful": false, "column": 33, "durationMicroseconds": 10, "errorMessage": "Cannot access value for name mouseInsidePage", "functionName": "enabled", "line": 400}, {"codegenSuccessful": true, "column": 22, "durationMicroseconds": 79, "errorMessage": "", "functionName": "onActivated", "line": 401}, {"codegenSuccessful": false, "column": 22, "durationMicroseconds": 101, "errorMessage": "Cannot access value for name sortMode", "functionName": "onActivated", "line": 406}, {"codegenSuccessful": true, "column": 24, "durationMicroseconds": 126, "errorMessage": "", "functionName": "currentFolder", "line": 417}, {"codegenSuccessful": false, "column": 21, "durationMicroseconds": 94, "errorMessage": "Cannot find name currentPath", "functionName": "onAccepted", "line": 418}, {"codegenSuccessful": true, "column": 52, "durationMicroseconds": 36, "errorMessage": "", "functionName": "standardButtons", "line": 427}, {"codegenSuccessful": true, "column": 81, "durationMicroseconds": 26, "errorMessage": "", "functionName": "centerIn", "line": 427}, {"codegenSuccessful": true, "column": 20, "durationMicroseconds": 29, "errorMessage": "", "functionName": "width", "line": 429}, {"codegenSuccessful": true, "column": 44, "durationMicroseconds": 28, "errorMessage": "", "functionName": "wrapMode", "line": 429}, {"codegenSuccessful": false, "column": 19, "durationMicroseconds": 14, "errorMessage": "Cannot access value for name maxPreviewWindows", "functionName": "text", "line": 430}, {"codegenSuccessful": false, "column": 20, "durationMicroseconds": 10, "errorMessage": "Cannot find name mouseInsidePage", "functionName": "onEntered", "line": 439}, {"codegenSuccessful": false, "column": 19, "durationMicroseconds": 9, "errorMessage": "Cannot find name mouseInsidePage", "functionName": "onExited", "line": 440}, {"codegenSuccessful": true, "column": 23, "durationMicroseconds": 20, "errorMessage": "", "functionName": "fill", "line": 437}, {"codegenSuccessful": true, "column": 28, "durationMicroseconds": 244, "errorMessage": "", "functionName": "onCompleted", "line": 443}], "filePath": "C:/Qt/file/palyer/compoment/imagePage.qml"}], "moduleId": "palyer(palyer)"}]