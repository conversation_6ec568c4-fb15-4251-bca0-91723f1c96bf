
set(target "palyer")
set(working_dir "C:/Qt/file/palyer")
set(src_and_dest_list
    "C:/Qt/file/palyer/Main.qml"
    "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/palyer/Main.qml"
    "C:/Qt/file/palyer/compoment/AppState.qml"
    "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/palyer/compoment/AppState.qml"
    "C:/Qt/file/palyer/compoment/ImagePreviewWindow.qml"
    "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/palyer/compoment/ImagePreviewWindow.qml"
    "C:/Qt/file/palyer/compoment/RemoteImagePage.qml"
    "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/palyer/compoment/RemoteImagePage.qml"
    "C:/Qt/file/palyer/compoment/SettingsPage.qml"
    "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/palyer/compoment/SettingsPage.qml"
    "C:/Qt/file/palyer/compoment/imagePage.qml"
    "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/palyer/compoment/imagePage.qml"
    "C:/Qt/file/palyer/compoment/template/BaseWindowTemplate.qml"
    "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/palyer/compoment/template/BaseWindowTemplate.qml"
    "C:/Qt/file/palyer/compoment/toolbar/ConversionWindow.qml"
    "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/palyer/compoment/toolbar/ConversionWindow.qml"

)
set(timestamp_file "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.qt/palyer_qml.txt")
