
set(target "palyer")
set(working_dir "C:/Qt/file/palyer")
set(src_and_dest_list
    "C:/Qt/file/palyer/Main.qml"
    "C:/Qt/file/palyer/build/palyer/Main.qml"
    "C:/Qt/file/palyer/compoment/AppState.qml"
    "C:/Qt/file/palyer/build/palyer/compoment/AppState.qml"
    "C:/Qt/file/palyer/compoment/ImagePreviewWindow.qml"
    "C:/Qt/file/palyer/build/palyer/compoment/ImagePreviewWindow.qml"
    "C:/Qt/file/palyer/compoment/NetworkSettingsDialog.qml"
    "C:/Qt/file/palyer/build/palyer/compoment/NetworkSettingsDialog.qml"
    "C:/Qt/file/palyer/compoment/RemoteImagePage.qml"
    "C:/Qt/file/palyer/build/palyer/compoment/RemoteImagePage.qml"
    "C:/Qt/file/palyer/compoment/SettingsPage.qml"
    "C:/Qt/file/palyer/build/palyer/compoment/SettingsPage.qml"
    "C:/Qt/file/palyer/compoment/imagePage.qml"
    "C:/Qt/file/palyer/build/palyer/compoment/imagePage.qml"
    "C:/Qt/file/palyer/compoment/template/BaseWindowTemplate.qml"
    "C:/Qt/file/palyer/build/palyer/compoment/template/BaseWindowTemplate.qml"
    "C:/Qt/file/palyer/compoment/toolbar/ConversionWindow.qml"
    "C:/Qt/file/palyer/build/palyer/compoment/toolbar/ConversionWindow.qml"

)
set(timestamp_file "C:/Qt/file/palyer/build/.qt/palyer_qml.txt")
