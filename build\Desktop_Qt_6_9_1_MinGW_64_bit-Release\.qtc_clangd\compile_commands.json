[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DNDEBUG", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DBACKEND_LIBRARY", "-DMINGW_HAS_SECURE_API", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_DEPRECATED_WARNINGS", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_NO_DEBUG", "-DQT_OPENGL_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-Dbackend_EXPORTS", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Qt\\file\\palyer\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Release\\backend\\backend_autogen\\include", "-IC:\\Qt\\file\\palyer\\backend", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQuick", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQml", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlIntegration", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlMeta", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlModels", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlWorkerScript", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtOpenGL", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Qt\\file\\palyer\\backend\\ConversionTaskRunnable.cpp"], "directory": "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.qtc_clangd", "file": "C:/Qt/file/palyer/backend/ConversionTaskRunnable.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DNDEBUG", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DBACKEND_LIBRARY", "-DMINGW_HAS_SECURE_API", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_DEPRECATED_WARNINGS", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_NO_DEBUG", "-DQT_OPENGL_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-Dbackend_EXPORTS", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Qt\\file\\palyer\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Release\\backend\\backend_autogen\\include", "-IC:\\Qt\\file\\palyer\\backend", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQuick", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQml", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlIntegration", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlMeta", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlModels", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlWorkerScript", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtOpenGL", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Qt\\file\\palyer\\backend\\ImageConversionManager.cpp"], "directory": "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.qtc_clangd", "file": "C:/Qt/file/palyer/backend/ImageConversionManager.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DNDEBUG", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DBACKEND_LIBRARY", "-DMINGW_HAS_SECURE_API", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_DEPRECATED_WARNINGS", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_NO_DEBUG", "-DQT_OPENGL_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-Dbackend_EXPORTS", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Qt\\file\\palyer\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Release\\backend\\backend_autogen\\include", "-IC:\\Qt\\file\\palyer\\backend", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQuick", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQml", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlIntegration", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlMeta", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlModels", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlWorkerScript", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtOpenGL", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Qt\\file\\palyer\\backend\\ImageProcessor.cpp"], "directory": "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.qtc_clangd", "file": "C:/Qt/file/palyer/backend/ImageProcessor.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DNDEBUG", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DBACKEND_LIBRARY", "-DMINGW_HAS_SECURE_API", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_DEPRECATED_WARNINGS", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_NO_DEBUG", "-DQT_OPENGL_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-Dbackend_EXPORTS", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Qt\\file\\palyer\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Release\\backend\\backend_autogen\\include", "-IC:\\Qt\\file\\palyer\\backend", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQuick", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQml", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlIntegration", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlMeta", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlModels", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlWorkerScript", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtOpenGL", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Qt\\file\\palyer\\backend\\server\\ClientMode.cpp"], "directory": "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.qtc_clangd", "file": "C:/Qt/file/palyer/backend/server/ClientMode.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DNDEBUG", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DBACKEND_LIBRARY", "-DMINGW_HAS_SECURE_API", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_DEPRECATED_WARNINGS", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_NO_DEBUG", "-DQT_OPENGL_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-Dbackend_EXPORTS", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Qt\\file\\palyer\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Release\\backend\\backend_autogen\\include", "-IC:\\Qt\\file\\palyer\\backend", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQuick", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQml", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlIntegration", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlMeta", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlModels", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlWorkerScript", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtOpenGL", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Qt\\file\\palyer\\backend\\server\\ServerMode.cpp"], "directory": "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.qtc_clangd", "file": "C:/Qt/file/palyer/backend/server/ServerMode.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DNDEBUG", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DBACKEND_LIBRARY", "-DMINGW_HAS_SECURE_API", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_DEPRECATED_WARNINGS", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_NO_DEBUG", "-DQT_OPENGL_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-Dbackend_EXPORTS", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Qt\\file\\palyer\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Release\\backend\\backend_autogen\\include", "-IC:\\Qt\\file\\palyer\\backend", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQuick", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQml", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlIntegration", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlMeta", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlModels", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlWorkerScript", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtOpenGL", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Qt\\file\\palyer\\backend\\ConversionTaskRunnable.h"], "directory": "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.qtc_clangd", "file": "C:/Qt/file/palyer/backend/ConversionTaskRunnable.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DNDEBUG", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DBACKEND_LIBRARY", "-DMINGW_HAS_SECURE_API", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_DEPRECATED_WARNINGS", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_NO_DEBUG", "-DQT_OPENGL_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-Dbackend_EXPORTS", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Qt\\file\\palyer\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Release\\backend\\backend_autogen\\include", "-IC:\\Qt\\file\\palyer\\backend", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQuick", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQml", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlIntegration", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlMeta", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlModels", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlWorkerScript", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtOpenGL", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Qt\\file\\palyer\\backend\\ImageConversionManager.h"], "directory": "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.qtc_clangd", "file": "C:/Qt/file/palyer/backend/ImageConversionManager.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DNDEBUG", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DBACKEND_LIBRARY", "-DMINGW_HAS_SECURE_API", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_DEPRECATED_WARNINGS", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_NO_DEBUG", "-DQT_OPENGL_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-Dbackend_EXPORTS", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Qt\\file\\palyer\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Release\\backend\\backend_autogen\\include", "-IC:\\Qt\\file\\palyer\\backend", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQuick", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQml", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlIntegration", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlMeta", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlModels", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlWorkerScript", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtOpenGL", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Qt\\file\\palyer\\backend\\ImageProcessor.h"], "directory": "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.qtc_clangd", "file": "C:/Qt/file/palyer/backend/ImageProcessor.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DNDEBUG", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DBACKEND_LIBRARY", "-DMINGW_HAS_SECURE_API", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_DEPRECATED_WARNINGS", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_NO_DEBUG", "-DQT_OPENGL_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-Dbackend_EXPORTS", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Qt\\file\\palyer\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Release\\backend\\backend_autogen\\include", "-IC:\\Qt\\file\\palyer\\backend", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQuick", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQml", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlIntegration", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlMeta", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlModels", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlWorkerScript", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtOpenGL", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Qt\\file\\palyer\\backend\\server\\ClientMode.h"], "directory": "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.qtc_clangd", "file": "C:/Qt/file/palyer/backend/server/ClientMode.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DNDEBUG", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DBACKEND_LIBRARY", "-DMINGW_HAS_SECURE_API", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_DEPRECATED_WARNINGS", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NETWORK_LIB", "-DQT_NO_DEBUG", "-DQT_OPENGL_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-Dbackend_EXPORTS", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Qt\\file\\palyer\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Release\\backend\\backend_autogen\\include", "-IC:\\Qt\\file\\palyer\\backend", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQuick", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQml", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlIntegration", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlMeta", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlModels", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlWorkerScript", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtOpenGL", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\Qt\\file\\palyer\\backend\\server\\ServerMode.h"], "directory": "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.qtc_clangd", "file": "C:/Qt/file/palyer/backend/server/ServerMode.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DNDEBUG", "-std=gnu++17", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DMINGW_HAS_SECURE_API", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_MULTIMEDIA_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_NO_DEBUG", "-DQT_OPENGL_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\Qt\\file\\palyer\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Release\\palyer_autogen\\include", "-IC:\\Qt\\file\\palyer", "-IC:\\Qt\\file\\palyer\\backend", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQml\\6.9.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQml\\6.9.1\\QtQml", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore\\6.9.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore\\6.9.1\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQml", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlIntegration", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQuick", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlMeta", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlModels", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtQmlWorkerScript", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtOpenGL", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtMultimedia", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\Qt\\file\\palyer\\main.cpp"], "directory": "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/.qtc_clangd", "file": "C:/Qt/file/palyer/main.cpp"}]