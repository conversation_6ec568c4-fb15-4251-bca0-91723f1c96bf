[{"moduleFiles": [{"entries": [{"codegenSuccessful": false, "column": 15, "durationMicroseconds": 0, "errorMessage": "Could not find property \"modality\".", "functionName": "modality", "line": 11}, {"codegenSuccessful": false, "column": 12, "durationMicroseconds": 0, "errorMessage": "Could not find property \"flags\".", "functionName": "flags", "line": 12}, {"codegenSuccessful": true, "column": 23, "durationMicroseconds": 295, "errorMessage": "", "functionName": "fill", "line": 18}, {"codegenSuccessful": true, "column": 31, "durationMicroseconds": 69, "errorMessage": "", "functionName": "fill", "line": 28}, {"codegenSuccessful": false, "column": 43, "durationMicroseconds": 0, "errorMessage": "Could not find signal \"checkedChanged\".", "functionName": "onCheckedChanged", "line": 35}, {"codegenSuccessful": false, "column": 34, "durationMicroseconds": 0, "errorMessage": "Could not find property \"enabled\".", "functionName": "enabled", "line": 61}, {"codegenSuccessful": false, "column": 27, "durationMicroseconds": 0, "errorMessage": "Could not find property \"text\".", "functionName": "text", "line": 67}, {"codegenSuccessful": false, "column": 28, "durationMicroseconds": 0, "errorMessage": "Could not find property \"color\".", "functionName": "color", "line": 68}, {"codegenSuccessful": false, "column": 27, "durationMicroseconds": 0, "errorMessage": "Could not find property \"text\".", "functionName": "text", "line": 73}, {"codegenSuccessful": false, "column": 30, "durationMicroseconds": 0, "errorMessage": "Could not find property \"visible\".", "functionName": "visible", "line": 75}, {"codegenSuccessful": false, "column": 36, "durationMicroseconds": 389, "errorMessage": "Cannot access value for name networkBackend", "functionName": "onClicked", "line": 80}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 75, "errorMessage": "", "functionName": "fill", "line": 79}, {"codegenSuccessful": true, "column": 31, "durationMicroseconds": 61, "errorMessage": "", "functionName": "fill", "line": 92}, {"codegenSuccessful": false, "column": 27, "durationMicroseconds": 0, "errorMessage": "Could not find property \"text\".", "functionName": "text", "line": 97}, {"codegenSuccessful": false, "column": 31, "durationMicroseconds": 0, "errorMessage": "Could not find property \"text\".", "functionName": "text", "line": 102}, {"codegenSuccessful": false, "column": 31, "durationMicroseconds": 0, "errorMessage": "Could not find property \"text\".", "functionName": "text", "line": 105}, {"codegenSuccessful": false, "column": 31, "durationMicroseconds": 0, "errorMessage": "Could not find property \"fill\".", "functionName": "fill", "line": 115}, {"codegenSuccessful": false, "column": 28, "durationMicroseconds": 0, "errorMessage": "Could not find property \"width\".", "functionName": "width", "line": 118}, {"codegenSuccessful": false, "column": 31, "durationMicroseconds": 0, "errorMessage": "Could not find property \"wrapMode\".", "functionName": "wrapMode", "line": 119}, {"codegenSuccessful": false, "column": 27, "durationMicroseconds": 0, "errorMessage": "Could not find property \"text\".", "functionName": "text", "line": 120}, {"codegenSuccessful": false, "column": 28, "durationMicroseconds": 0, "errorMessage": "Could not find signal \"clicked\".", "functionName": "onClicked", "line": 137}, {"codegenSuccessful": false, "column": 28, "durationMicroseconds": 0, "errorMessage": "Could not find signal \"clicked\".", "functionName": "onClicked", "line": 144}, {"codegenSuccessful": false, "column": 17, "durationMicroseconds": 66, "errorMessage": "Cannot access value for name networkBackend", "functionName": "target", "line": 151}, {"codegenSuccessful": false, "column": 9, "durationMicroseconds": 205, "errorMessage": "Cannot retrieve a non-object type by ID: statusLabel", "functionName": "onServerStatusChanged", "line": 152}], "filePath": "C:/Qt/file/palyer/compoment/NetworkSettingsDialog.qml"}], "moduleId": "palyer(palyer)"}]