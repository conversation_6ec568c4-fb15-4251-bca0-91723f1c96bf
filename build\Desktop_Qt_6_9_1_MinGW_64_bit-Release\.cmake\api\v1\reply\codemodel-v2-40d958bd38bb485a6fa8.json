{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "hasInstallRule": true, "jsonFile": "directory-.-Release-a6262025076862d82d6d.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18]}, {"build": "backend", "hasInstallRule": true, "jsonFile": "directory-backend-Release-1d02cd6d3aa3dfd0ba90.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "backend", "targetIndexes": [5, 6, 7]}], "name": "Release", "projects": [{"directoryIndexes": [0, 1], "name": "palyer", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18]}], "targets": [{"directoryIndex": 0, "id": "all_aotstats::@6890427a1f51a3e7e1df", "jsonFile": "target-all_aotstats-Release-6d461c9754d437b337d3.json", "name": "all_aotstats", "projectIndex": 0}, {"directoryIndex": 0, "id": "all_qmllint::@6890427a1f51a3e7e1df", "jsonFile": "target-all_qmllint-Release-1652c46152ebd37295f3.json", "name": "all_qmllint", "projectIndex": 0}, {"directoryIndex": 0, "id": "all_qmllint_json::@6890427a1f51a3e7e1df", "jsonFile": "target-all_qmllint_json-Release-b2748a93b3bb04f32ffb.json", "name": "all_qmllint_json", "projectIndex": 0}, {"directoryIndex": 0, "id": "all_qmllint_module::@6890427a1f51a3e7e1df", "jsonFile": "target-all_qmllint_module-Release-547b4d2f9c8148fedd7a.json", "name": "all_qmllint_module", "projectIndex": 0}, {"directoryIndex": 0, "id": "all_qmltyperegistrations::@6890427a1f51a3e7e1df", "jsonFile": "target-all_qmltyperegistrations-Release-113c1777400558012d9e.json", "name": "all_qmltyperegistrations", "projectIndex": 0}, {"directoryIndex": 1, "id": "backend::@e17dcb4e28158375c849", "jsonFile": "target-backend-Release-42cabb0a5dfaedca1750.json", "name": "backend", "projectIndex": 0}, {"directoryIndex": 1, "id": "backend_autogen::@e17dcb4e28158375c849", "jsonFile": "target-backend_autogen-Release-abd0f413baf38e775c5f.json", "name": "backend_autogen", "projectIndex": 0}, {"directoryIndex": 1, "id": "backend_autogen_timestamp_deps::@e17dcb4e28158375c849", "jsonFile": "target-backend_autogen_timestamp_deps-Release-04c4e7469393a0aff365.json", "name": "backend_autogen_timestamp_deps", "projectIndex": 0}, {"directoryIndex": 0, "id": "module_palyer_aotstats_target::@6890427a1f51a3e7e1df", "jsonFile": "target-module_palyer_aotstats_target-Release-822b31ab66998394caf4.json", "name": "module_palyer_aotstats_target", "projectIndex": 0}, {"directoryIndex": 0, "id": "palyer::@6890427a1f51a3e7e1df", "jsonFile": "target-palyer-Release-27dfa174f1505a55ab54.json", "name": "palyer", "projectIndex": 0}, {"directoryIndex": 0, "id": "palyer_autogen::@6890427a1f51a3e7e1df", "jsonFile": "target-palyer_autogen-Release-bae99a32bd7061de8637.json", "name": "palyer_autogen", "projectIndex": 0}, {"directoryIndex": 0, "id": "palyer_autogen_timestamp_deps::@6890427a1f51a3e7e1df", "jsonFile": "target-palyer_autogen_timestamp_deps-Release-4a4f6472c57f8d1668ce.json", "name": "palyer_autogen_timestamp_deps", "projectIndex": 0}, {"directoryIndex": 0, "id": "palyer_copy_qml::@6890427a1f51a3e7e1df", "jsonFile": "target-palyer_copy_qml-Release-9cdccb7ca0d8c6aba748.json", "name": "palyer_copy_qml", "projectIndex": 0}, {"directoryIndex": 0, "id": "palyer_copy_res::@6890427a1f51a3e7e1df", "jsonFile": "target-palyer_copy_res-Release-f1fd74f3d9b5093272c8.json", "name": "palyer_copy_res", "projectIndex": 0}, {"directoryIndex": 0, "id": "palyer_qmlimportscan::@6890427a1f51a3e7e1df", "jsonFile": "target-palyer_qmlimportscan-Release-29f7cee33deacd0d6b46.json", "name": "palyer_qmlimportscan", "projectIndex": 0}, {"directoryIndex": 0, "id": "palyer_qmllint::@6890427a1f51a3e7e1df", "jsonFile": "target-palyer_qmllint-Release-b0e4e6e31a88695137a9.json", "name": "palyer_qmllint", "projectIndex": 0}, {"directoryIndex": 0, "id": "palyer_qmllint_json::@6890427a1f51a3e7e1df", "jsonFile": "target-palyer_qmllint_json-Release-974bba18e281db2311ed.json", "name": "palyer_qmllint_json", "projectIndex": 0}, {"directoryIndex": 0, "id": "palyer_qmllint_module::@6890427a1f51a3e7e1df", "jsonFile": "target-palyer_qmllint_module-Release-4878ccbab1eb4353ef5a.json", "name": "palyer_qmllint_module", "projectIndex": 0}, {"directoryIndex": 0, "id": "palyer_qmltyperegistration::@6890427a1f51a3e7e1df", "jsonFile": "target-palyer_qmltyperegistration-Release-5d1b526feb69fb8b2e46.json", "name": "palyer_qmltyperegistration", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release", "source": "C:/Qt/file/palyer"}, "version": {"major": 2, "minor": 7}}