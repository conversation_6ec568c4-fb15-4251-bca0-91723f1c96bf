[{"moduleFiles": [{"entries": [{"codegenSuccessful": true, "column": 8, "durationMicroseconds": 353, "errorMessage": "", "functionName": "x", "line": 13}, {"codegenSuccessful": true, "column": 8, "durationMicroseconds": 162, "errorMessage": "", "functionName": "y", "line": 14}, {"codegenSuccessful": true, "column": 12, "durationMicroseconds": 131, "errorMessage": "", "functionName": "flags", "line": 15}, {"codegenSuccessful": true, "column": 14, "durationMicroseconds": 88, "errorMessage": "", "functionName": "opacity", "line": 17}, {"codegenSuccessful": true, "column": 5, "durationMicroseconds": 77, "errorMessage": "", "functionName": "closeWindow", "line": 34}, {"codegenSuccessful": true, "column": 5, "durationMicroseconds": 108, "errorMessage": "", "functionName": "toggleFullScreen", "line": 39}, {"codegenSuccessful": true, "column": 73, "durationMicroseconds": 42, "errorMessage": "", "functionName": "type", "line": 31}, {"codegenSuccessful": false, "column": 22, "durationMicroseconds": 188, "errorMessage": "Cannot access value for name isFullScreen", "functionName": "onTriggered", "line": 48}, {"codegenSuccessful": true, "column": 23, "durationMicroseconds": 35, "errorMessage": "", "functionName": "fill", "line": 59}, {"codegenSuccessful": false, "column": 31, "durationMicroseconds": 147, "errorMessage": "method closeWindow cannot be resolved.", "functionName": "onEscapePressed", "line": 65}, {"codegenSuccessful": true, "column": 20, "durationMicroseconds": 44, "errorMessage": "", "functionName": "width", "line": 70}, {"codegenSuccessful": true, "column": 28, "durationMicroseconds": 71, "errorMessage": "", "functionName": "onPressed", "line": 75}, {"codegenSuccessful": true, "column": 31, "durationMicroseconds": 26, "errorMessage": "", "functionName": "fill", "line": 74}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 71, "errorMessage": "", "functionName": "top", "line": 81}, {"codegenSuccessful": true, "column": 50, "durationMicroseconds": 49, "errorMessage": "", "functionName": "left", "line": 81}, {"codegenSuccessful": false, "column": 28, "durationMicroseconds": 316, "errorMessage": "Cannot generate efficient code for LoadClosure", "functionName": "model", "line": 85}, {"codegenSuccessful": true, "column": 56, "durationMicroseconds": 80, "errorMessage": "", "functionName": "radius", "line": 92}, {"codegenSuccessful": false, "column": 32, "durationMicroseconds": 139, "errorMessage": "Cannot access value for name modelData", "functionName": "color", "line": 93}, {"codegenSuccessful": true, "column": 34, "durationMicroseconds": 91, "errorMessage": "", "functionName": "opacity", "line": 94}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 69, "errorMessage": "", "functionName": "scale", "line": 95}, {"codegenSuccessful": true, "column": 91, "durationMicroseconds": 64, "errorMessage": "", "functionName": "type", "line": 97}, {"codegenSuccessful": true, "column": 93, "durationMicroseconds": 44, "errorMessage": "", "functionName": "type", "line": 98}, {"codegenSuccessful": false, "column": 35, "durationMicroseconds": 209, "errorMessage": "Cannot access value for name modelData", "functionName": "text", "line": 101}, {"codegenSuccessful": true, "column": 38, "durationMicroseconds": 42, "errorMessage": "", "functionName": "visible", "line": 108}, {"codegenSuccessful": true, "column": 47, "durationMicroseconds": 28, "errorMessage": "", "functionName": "centerIn", "line": 103}, {"codegenSuccessful": false, "column": 36, "durationMicroseconds": 84, "errorMessage": "Cannot access value for name modelData", "functionName": "x", "line": 105}, {"codegenSuccessful": false, "column": 36, "durationMicroseconds": 84, "errorMessage": "Cannot access value for name modelData", "functionName": "y", "line": 106}, {"codegenSuccessful": false, "column": 40, "durationMicroseconds": 146, "errorMessage": "Cannot access value for name modelData", "functionName": "onClicked", "line": 116}, {"codegenSuccessful": true, "column": 43, "durationMicroseconds": 31, "errorMessage": "", "functionName": "fill", "line": 115}, {"codegenSuccessful": true, "column": 23, "durationMicroseconds": 45, "errorMessage": "", "functionName": "text", "line": 128}, {"codegenSuccessful": true, "column": 38, "durationMicroseconds": 39, "errorMessage": "", "functionName": "horizontalAlignment", "line": 129}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 33, "errorMessage": "", "functionName": "verticalAlignment", "line": 130}, {"codegenSuccessful": true, "column": 24, "durationMicroseconds": 66, "errorMessage": "", "functionName": "elide", "line": 131}, {"codegenSuccessful": true, "column": 24, "durationMicroseconds": 148, "errorMessage": "", "functionName": "width", "line": 132}, {"codegenSuccessful": true, "column": 35, "durationMicroseconds": 27, "errorMessage": "", "functionName": "centerIn", "line": 125}, {"codegenSuccessful": true, "column": 20, "durationMicroseconds": 36, "errorMessage": "", "functionName": "width", "line": 140}, {"codegenSuccessful": false, "column": 20, "durationMicroseconds": 125, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 141}, {"codegenSuccessful": true, "column": 26, "durationMicroseconds": 56, "errorMessage": "", "functionName": "top", "line": 139}, {"codegenSuccessful": true, "column": 22, "durationMicroseconds": 86, "errorMessage": "", "functionName": "top", "line": 148}, {"codegenSuccessful": true, "column": 23, "durationMicroseconds": 48, "errorMessage": "", "functionName": "left", "line": 149}, {"codegenSuccessful": true, "column": 24, "durationMicroseconds": 47, "errorMessage": "", "functionName": "right", "line": 150}, {"codegenSuccessful": true, "column": 25, "durationMicroseconds": 46, "errorMessage": "", "functionName": "bottom", "line": 151}], "filePath": "C:/Qt/file/palyer/compoment/template/BaseWindowTemplate.qml"}], "moduleId": "palyer(palyer)"}]