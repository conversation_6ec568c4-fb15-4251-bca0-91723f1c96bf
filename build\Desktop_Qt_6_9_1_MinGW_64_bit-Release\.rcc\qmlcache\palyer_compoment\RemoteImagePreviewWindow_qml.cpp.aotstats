[{"moduleFiles": [{"entries": [{"codegenSuccessful": true, "column": 8, "durationMicroseconds": 370, "errorMessage": "", "functionName": "x", "line": 12}, {"codegenSuccessful": true, "column": 8, "durationMicroseconds": 172, "errorMessage": "", "functionName": "y", "line": 13}, {"codegenSuccessful": true, "column": 12, "durationMicroseconds": 125, "errorMessage": "", "functionName": "flags", "line": 14}, {"codegenSuccessful": true, "column": 14, "durationMicroseconds": 90, "errorMessage": "", "functionName": "opacity", "line": 16}, {"codegenSuccessful": true, "column": 5, "durationMicroseconds": 95, "errorMessage": "", "functionName": "closeWindow", "line": 31}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "updateImageSize", "line": 267}, {"codegenSuccessful": true, "column": 73, "durationMicroseconds": 56, "errorMessage": "", "functionName": "type", "line": 19}, {"codegenSuccessful": false, "column": 22, "durationMicroseconds": 166, "errorMessage": "Cannot access value for name isFullScreen", "functionName": "onTriggered", "line": 40}, {"codegenSuccessful": false, "column": 16, "durationMicroseconds": 148, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 51}, {"codegenSuccessful": false, "column": 17, "durationMicroseconds": 21, "errorMessage": "Cannot access value for name isFullScreen", "functionName": "radius", "line": 52}, {"codegenSuccessful": true, "column": 23, "durationMicroseconds": 31, "errorMessage": "", "functionName": "fill", "line": 50}, {"codegenSuccessful": false, "column": 23, "durationMicroseconds": 143, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 53}, {"codegenSuccessful": false, "column": 23, "durationMicroseconds": 19, "errorMessage": "Cannot access value for name isFullScreen", "functionName": "width", "line": 54}, {"codegenSuccessful": true, "column": 20, "durationMicroseconds": 47, "errorMessage": "", "functionName": "width", "line": 59}, {"codegenSuccessful": false, "column": 20, "durationMicroseconds": 119, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 61}, {"codegenSuccessful": false, "column": 21, "durationMicroseconds": 19, "errorMessage": "Cannot access value for name isFullScreen", "functionName": "radius", "line": 62}, {"codegenSuccessful": false, "column": 22, "durationMicroseconds": 32, "errorMessage": "Cannot access value for name isFullScreen", "functionName": "visible", "line": 63}, {"codegenSuccessful": true, "column": 31, "durationMicroseconds": 61, "errorMessage": "", "functionName": "left", "line": 66}, {"codegenSuccessful": true, "column": 41, "durationMicroseconds": 55, "errorMessage": "", "functionName": "verticalCenter", "line": 68}, {"codegenSuccessful": true, "column": 45, "durationMicroseconds": 54, "errorMessage": "", "functionName": "verticalCenter", "line": 75}, {"codegenSuccessful": false, "column": 27, "durationMicroseconds": 46, "errorMessage": "Cannot access value for name imageData", "functionName": "text", "line": 79}, {"codegenSuccessful": true, "column": 28, "durationMicroseconds": 47, "errorMessage": "", "functionName": "elide", "line": 83}, {"codegenSuccessful": true, "column": 28, "durationMicroseconds": 137, "errorMessage": "", "functionName": "width", "line": 84}, {"codegenSuccessful": true, "column": 45, "durationMicroseconds": 62, "errorMessage": "", "functionName": "verticalCenter", "line": 82}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 56, "errorMessage": "", "functionName": "right", "line": 89}, {"codegenSuccessful": true, "column": 41, "durationMicroseconds": 54, "errorMessage": "", "functionName": "verticalCenter", "line": 91}, {"codegenSuccessful": false, "column": 28, "durationMicroseconds": 239, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: conversion to QVariant -> QColor stored as QVariant", "functionName": "color", "line": 97}, {"codegenSuccessful": true, "column": 43, "durationMicroseconds": 72, "errorMessage": "", "functionName": "centerIn", "line": 99}, {"codegenSuccessful": false, "column": 36, "durationMicroseconds": 90, "errorMessage": "Cannot access value for name isFullScreen", "functionName": "onClicked", "line": 108}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 29, "errorMessage": "", "functionName": "fill", "line": 106}, {"codegenSuccessful": false, "column": 28, "durationMicroseconds": 254, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: conversion to QVariant -> QColor stored as QVariant", "functionName": "color", "line": 122}, {"codegenSuccessful": true, "column": 43, "durationMicroseconds": 36, "errorMessage": "", "functionName": "centerIn", "line": 124}, {"codegenSuccessful": false, "column": 36, "durationMicroseconds": 200, "errorMessage": "method closeWindow cannot be resolved.", "functionName": "onClicked", "line": 133}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 33, "errorMessage": "", "functionName": "fill", "line": 131}, {"codegenSuccessful": true, "column": 20, "durationMicroseconds": 44, "errorMessage": "", "functionName": "width", "line": 142}, {"codegenSuccessful": false, "column": 20, "durationMicroseconds": 131, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 144}, {"codegenSuccessful": false, "column": 22, "durationMicroseconds": 18, "errorMessage": "Cannot access value for name isFullScreen", "functionName": "visible", "line": 146}, {"codegenSuccessful": true, "column": 26, "durationMicroseconds": 71, "errorMessage": "", "functionName": "top", "line": 145}, {"codegenSuccessful": false, "column": 26, "durationMicroseconds": 99, "errorMessage": "Cannot access value for name isFullScreen", "functionName": "top", "line": 152}, {"codegenSuccessful": true, "column": 27, "durationMicroseconds": 125, "errorMessage": "", "functionName": "left", "line": 153}, {"codegenSuccessful": true, "column": 28, "durationMicroseconds": 110, "errorMessage": "", "functionName": "right", "line": 154}, {"codegenSuccessful": true, "column": 29, "durationMicroseconds": 102, "errorMessage": "", "functionName": "bottom", "line": 155}, {"codegenSuccessful": true, "column": 31, "durationMicroseconds": 92, "errorMessage": "", "functionName": "contentWidth", "line": 161}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 89, "errorMessage": "", "functionName": "contentHeight", "line": 162}, {"codegenSuccessful": true, "column": 31, "durationMicroseconds": 165, "errorMessage": "", "functionName": "fill", "line": 160}, {"codegenSuccessful": true, "column": 31, "durationMicroseconds": 162, "errorMessage": "", "functionName": "fillMode", "line": 168}, {"codegenSuccessful": false, "column": 29, "durationMicroseconds": 214, "errorMessage": "Cannot access value for name imageData", "functionName": "source", "line": 172}, {"codegenSuccessful": false, "column": 38, "durationMicroseconds": 489, "errorMessage": "method updateImageSize cannot be resolved.", "functionName": "onStatusChanged", "line": 175}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 53, "errorMessage": "", "functionName": "centerIn", "line": 167}, {"codegenSuccessful": true, "column": 34, "durationMicroseconds": 112, "errorMessage": "", "functionName": "running", "line": 186}, {"codegenSuccessful": true, "column": 34, "durationMicroseconds": 33, "errorMessage": "", "functionName": "visible", "line": 187}, {"codegenSuccessful": true, "column": 43, "durationMicroseconds": 55, "errorMessage": "", "functionName": "centerIn", "line": 185}, {"codegenSuccessful": false, "column": 32, "durationMicroseconds": 133, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 194}, {"codegenSuccessful": true, "column": 34, "durationMicroseconds": 82, "errorMessage": "", "functionName": "visible", "line": 196}, {"codegenSuccessful": true, "column": 43, "durationMicroseconds": 27, "errorMessage": "", "functionName": "centerIn", "line": 192}, {"codegenSuccessful": true, "column": 47, "durationMicroseconds": 32, "errorMessage": "", "functionName": "centerIn", "line": 199}, {"codegenSuccessful": true, "column": 59, "durationMicroseconds": 66, "errorMessage": "", "functionName": "horizontalCenter", "line": 206}, {"codegenSuccessful": true, "column": 59, "durationMicroseconds": 55, "errorMessage": "", "functionName": "horizontalCenter", "line": 213}, {"codegenSuccessful": true, "column": 38, "durationMicroseconds": 191, "errorMessage": "", "functionName": "acceptedButtons", "line": 222}, {"codegenSuccessful": false, "column": 38, "durationMicroseconds": 92, "errorMessage": "Cannot access value for name isFullScreen", "functionName": "onDoubleClicked", "line": 224}, {"codegenSuccessful": true, "column": 35, "durationMicroseconds": 40, "errorMessage": "", "functionName": "fill", "line": 221}, {"codegenSuccessful": false, "column": 22, "durationMicroseconds": 16, "errorMessage": "Cannot access value for name isFullScreen", "functionName": "visible", "line": 240}, {"codegenSuccessful": true, "column": 24, "durationMicroseconds": 108, "errorMessage": "", "functionName": "onPressed", "line": 241}, {"codegenSuccessful": true, "column": 27, "durationMicroseconds": 40, "errorMessage": "", "functionName": "fill", "line": 239}, {"codegenSuccessful": true, "column": 21, "durationMicroseconds": 440, "errorMessage": "", "functionName": "onPressed", "line": 246}, {"codegenSuccessful": true, "column": 21, "durationMicroseconds": 29, "errorMessage": "", "functionName": "onPressed", "line": 246}, {"codegenSuccessful": false, "column": 28, "durationMicroseconds": 190, "errorMessage": "Cannot find name focus", "functionName": "onCompleted", "line": 307}], "filePath": "C:/Qt/file/palyer/compoment/RemoteImagePreviewWindow.qml"}], "moduleId": "palyer(palyer)"}]