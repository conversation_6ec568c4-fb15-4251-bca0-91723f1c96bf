{"entries": [{"name": "CMAKE_AR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lib.exe"}, {"name": "CMAKE_CACHEFILE_DIR", "properties": [{"name": "HELPSTRING", "value": "This is the directory where this CMakeCache.txt was created"}], "type": "INTERNAL", "value": "c:/Qt/file/palyer/build"}, {"name": "CMAKE_CACHE_MAJOR_VERSION", "properties": [{"name": "HELPSTRING", "value": "Major version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "3"}, {"name": "CMAKE_CACHE_MINOR_VERSION", "properties": [{"name": "HELPSTRING", "value": "Minor version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "30"}, {"name": "CMAKE_CACHE_PATCH_VERSION", "properties": [{"name": "HELPSTRING", "value": "Patch version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "5"}, {"name": "CMAKE_COLOR_DIAGNOSTICS", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "BOOL", "value": "ON"}, {"name": "CMAKE_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to CMake executable."}], "type": "INTERNAL", "value": "C:/Qt/Tools/CMake_64/bin/cmake.exe"}, {"name": "CMAKE_CONFIGURATION_TYPES", "properties": [{"name": "HELPSTRING", "value": "Semicolon separated list of supported configuration types, only supports Debug, Release, MinSizeRel, and RelWithDebInfo, anything else will be ignored."}], "type": "STRING", "value": "Debug;Release;MinSizeRel;RelWithDebInfo"}, {"name": "CMAKE_CPACK_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to cpack program executable."}], "type": "INTERNAL", "value": "C:/Qt/Tools/CMake_64/bin/cpack.exe"}, {"name": "CMAKE_CTEST_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to ctest program executable."}], "type": "INTERNAL", "value": "C:/Qt/Tools/CMake_64/bin/ctest.exe"}, {"name": "CMAKE_CXX_COMPILER", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "FILEPATH", "value": "C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/cl.exe"}, {"name": "CMAKE_CXX_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during all build types."}], "type": "STRING", "value": "/DWIN32 /D_WINDOWS /GR /EHsc"}, {"name": "CMAKE_CXX_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during DEBUG builds."}], "type": "STRING", "value": "/Zi /Ob0 /Od /RTC1"}, {"name": "CMAKE_CXX_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during MINSIZEREL builds."}], "type": "STRING", "value": "/O1 /Ob1 /DNDEBUG"}, {"name": "CMAKE_CXX_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during RELEASE builds."}], "type": "STRING", "value": "/O2 /Ob2 /DNDEBUG"}, {"name": "CMAKE_CXX_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/Zi /O2 /Ob1 /DNDEBUG"}, {"name": "CMAKE_CXX_OUTPUT_EXTENSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": ".obj"}, {"name": "CMAKE_CXX_STANDARD_LIBRARIES", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Libraries linked by default with all C++ applications."}], "type": "STRING", "value": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib"}, {"name": "CMAKE_C_COMPILER", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "FILEPATH", "value": "C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/cl.exe"}, {"name": "CMAKE_C_OUTPUT_EXTENSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXECUTABLE_FORMAT", "properties": [{"name": "HELPSTRING", "value": "Executable file format"}], "type": "INTERNAL", "value": "Unknown"}, {"name": "CMAKE_EXE_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during all build types."}], "type": "STRING", "value": "/machine:x64"}, {"name": "CMAKE_EXE_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during DEBUG builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_EXE_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during MINSIZEREL builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_EXE_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during RELEASE builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_FIND_PACKAGE_REDIRECTS_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake."}], "type": "STATIC", "value": "C:/Qt/file/palyer/build/CMakeFiles/pkgRedirects"}, {"name": "CMAKE_GENERATOR", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "STRING", "value": "Visual Studio 17 2022"}, {"name": "CMAKE_GENERATOR_INSTANCE", "properties": [{"name": "HELPSTRING", "value": "Generator instance identifier."}], "type": "INTERNAL", "value": "C:/Program Files/Microsoft Visual Studio/2022/Professional"}, {"name": "CMAKE_GENERATOR_PLATFORM", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "STRING", "value": "x64"}, {"name": "CMAKE_GENERATOR_TOOLSET", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "STRING", "value": "host=x64"}, {"name": "CMAKE_HAVE_LIBC_PTHREAD", "properties": [{"name": "HELPSTRING", "value": "Test CMAKE_HAVE_LIBC_PTHREAD"}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_HAVE_PTHREADS_CREATE", "properties": [{"name": "HELPSTRING", "value": "Have library pthreads"}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_HAVE_PTHREAD_CREATE", "properties": [{"name": "HELPSTRING", "value": "Have library pthread"}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_HOME_DIRECTORY", "properties": [{"name": "HELPSTRING", "value": "Source directory with the top level CMakeLists.txt file for this project"}], "type": "INTERNAL", "value": "C:/Qt/file/palyer"}, {"name": "CMAKE_INSTALL_BINDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "User executables (bin)"}], "type": "PATH", "value": "bin"}, {"name": "CMAKE_INSTALL_DATADIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Read-only architecture-independent data (DATAROOTDIR)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_DATAROOTDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Read-only architecture-independent data root (share)"}], "type": "PATH", "value": "share"}, {"name": "CMAKE_INSTALL_DOCDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Documentation root (DATAROOTDIR/doc/PROJECT_NAME)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_INCLUDEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C header files (include)"}], "type": "PATH", "value": "include"}, {"name": "CMAKE_INSTALL_INFODIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Info documentation (DATAROOTDIR/info)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_LIBDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Object code libraries (lib)"}], "type": "PATH", "value": "lib"}, {"name": "CMAKE_INSTALL_LIBEXECDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Program executables (libexec)"}], "type": "PATH", "value": "libexec"}, {"name": "CMAKE_INSTALL_LOCALEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Locale-dependent data (DATAROOTDIR/locale)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_LOCALSTATEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Modifiable single-machine data (var)"}], "type": "PATH", "value": "var"}, {"name": "CMAKE_INSTALL_MANDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Man documentation (DATAROOTDIR/man)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_OLDINCLUDEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C header files for non-gcc (/usr/include)"}], "type": "PATH", "value": "/usr/include"}, {"name": "CMAKE_INSTALL_PREFIX", "properties": [{"name": "HELPSTRING", "value": "Install path prefix, prepended onto install directories."}], "type": "PATH", "value": "C:/Program Files/palyer"}, {"name": "CMAKE_INSTALL_RUNSTATEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Run-time variable data (LOCALSTATEDIR/run)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_SBINDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "System admin executables (sbin)"}], "type": "PATH", "value": "sbin"}, {"name": "CMAKE_INSTALL_SHAREDSTATEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Modifiable architecture-independent data (com)"}], "type": "PATH", "value": "com"}, {"name": "CMAKE_INSTALL_SYSCONFDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Read-only single-machine data (etc)"}], "type": "PATH", "value": "etc"}, {"name": "CMAKE_LINKER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/link.exe"}, {"name": "CMAKE_MODULE_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during all build types."}], "type": "STRING", "value": "/machine:x64"}, {"name": "CMAKE_MODULE_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during DEBUG builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during MINSIZEREL builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_MODULE_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during RELEASE builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_MT", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "C:/Program Files (x86)/Windows Kits/10/bin/10.0.22621.0/x64/mt.exe"}, {"name": "CMAKE_NUMBER_OF_MAKEFILES", "properties": [{"name": "HELPSTRING", "value": "number of local generators"}], "type": "INTERNAL", "value": "2"}, {"name": "CMAKE_PLATFORM_INFO_INITIALIZED", "properties": [{"name": "HELPSTRING", "value": "Platform information initialized"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_PREFIX_PATH", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "PATH", "value": ""}, {"name": "CMAKE_PROJECT_DESCRIPTION", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_HOMEPAGE_URL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_INCLUDE_BEFORE", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "FILEPATH", "value": "C:/Qt/file/palyer/build/.qtc/package-manager/auto-setup.cmake"}, {"name": "CMAKE_PROJECT_NAME", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "palyer"}, {"name": "CMAKE_PROJECT_VERSION", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "0.1"}, {"name": "CMAKE_PROJECT_VERSION_MAJOR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "0"}, {"name": "CMAKE_PROJECT_VERSION_MINOR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "1"}, {"name": "CMAKE_PROJECT_VERSION_PATCH", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_VERSION_TWEAK", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_RANLIB", "properties": [{"name": "HELPSTRING", "value": "noop for ranlib"}], "type": "INTERNAL", "value": ":"}, {"name": "CMAKE_RC_COMPILER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "RC compiler"}], "type": "FILEPATH", "value": "C:/Program Files (x86)/Windows Kits/10/bin/10.0.22621.0/x64/rc.exe"}, {"name": "CMAKE_RC_COMPILER_WORKS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_RC_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during all build types."}], "type": "STRING", "value": "-DWIN32"}, {"name": "CMAKE_RC_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during DEBUG builds."}], "type": "STRING", "value": "-D_DEBUG"}, {"name": "CMAKE_RC_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_RC_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_RC_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags for Windows Resource Compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_ROOT", "properties": [{"name": "HELPSTRING", "value": "Path to CMake installation."}], "type": "INTERNAL", "value": "C:/Qt/Tools/CMake_64/share/cmake-3.30"}, {"name": "CMAKE_SHARED_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during all build types."}], "type": "STRING", "value": "/machine:x64"}, {"name": "CMAKE_SHARED_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during DEBUG builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during MINSIZEREL builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_SHARED_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during RELEASE builds."}], "type": "STRING", "value": "/INCREMENTAL:NO"}, {"name": "CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during RELWITHDEBINFO builds."}], "type": "STRING", "value": "/debug /INCREMENTAL"}, {"name": "CMAKE_SKIP_INSTALL_RPATH", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If set, runtime paths are not added when installing shared libraries, but are added when building."}], "type": "BOOL", "value": "NO"}, {"name": "CMAKE_SKIP_RPATH", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If set, runtime paths are not added when using shared libraries."}], "type": "BOOL", "value": "NO"}, {"name": "CMAKE_STATIC_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during all build types."}], "type": "STRING", "value": "/machine:x64"}, {"name": "CMAKE_STATIC_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_VERBOSE_MAKEFILE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If this value is on, makefiles will be generated without the .SILENT directive, and all commands will be echoed to the console during the make.  This is useful for debugging only. With Visual Studio IDE projects all commands are done without /nologo."}], "type": "BOOL", "value": "FALSE"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_Threads", "properties": [{"name": "HELPSTRING", "value": "Details about finding Threads"}], "type": "INTERNAL", "value": "[TRUE][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_WrapAtomic", "properties": [{"name": "HELPSTRING", "value": "Details about finding <PERSON><PERSON><PERSON><PERSON><PERSON>"}], "type": "INTERNAL", "value": "[1][v()]"}, {"name": "HAVE_STDATOMIC", "properties": [{"name": "HELPSTRING", "value": "Test HAVE_STDATOMIC"}], "type": "INTERNAL", "value": ""}, {"name": "HAVE_STDATOMIC_WITH_LIB", "properties": [{"name": "HELPSTRING", "value": "Test HAVE_STDATOMIC_WITH_LIB"}], "type": "INTERNAL", "value": "1"}, {"name": "QT_ADDITIONAL_HOST_PACKAGES_PREFIX_PATH", "properties": [{"name": "HELPSTRING", "value": "Additional directories where find(Qt6 ...) host Qt components are searched"}], "type": "STRING", "value": ""}, {"name": "QT_ADDITIONAL_PACKAGES_PREFIX_PATH", "properties": [{"name": "HELPSTRING", "value": "Additional directories where find(Qt6 ...) components are searched"}], "type": "STRING", "value": ""}, {"name": "QT_CREATOR_SKIP_PACKAGE_MANAGER_SETUP", "properties": [{"name": "HELPSTRING", "value": "Skip Qt Creator's package manager auto-setup"}], "type": "BOOL", "value": "OFF"}, {"name": "QT_CREATOR_SOURCE_GROUPS", "properties": [{"name": "HELPSTRING", "value": "Qt Creator source groups extensions"}], "type": "BOOL", "value": "ON"}, {"name": "QT_FEATURE_accessibility", "properties": [{"name": "HELPSTRING", "value": "Qt feature: accessibility (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_accessibility_atspi_bridge", "properties": [{"name": "HELPSTRING", "value": "Qt feature: accessibility_atspi_bridge (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_action", "properties": [{"name": "HELPSTRING", "value": "Qt feature: action (from target Qt6::<PERSON>ui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_aesni", "properties": [{"name": "HELPSTRING", "value": "Qt feature: aesni (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_alsa", "properties": [{"name": "HELPSTRING", "value": "Qt feature: alsa (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_android_style_assets", "properties": [{"name": "HELPSTRING", "value": "Qt feature: android_style_assets (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_animation", "properties": [{"name": "HELPSTRING", "value": "Qt feature: animation (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_appstore_compliant", "properties": [{"name": "HELPSTRING", "value": "Qt feature: appstore_compliant (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_arm_crc32", "properties": [{"name": "HELPSTRING", "value": "Qt feature: arm_crc32 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_arm_crypto", "properties": [{"name": "HELPSTRING", "value": "Qt feature: arm_crypto (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_arm_sve", "properties": [{"name": "HELPSTRING", "value": "Qt feature: arm_sve (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_avfoundation", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avfoundation (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_avx", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_avx2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx2 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_avx512bw", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx512bw (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_avx512cd", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx512cd (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_avx512dq", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx512dq (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_avx512er", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx512er (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_avx512f", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx512f (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_avx512ifma", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx512ifma (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_avx512pf", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx512pf (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_avx512vbmi", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx512vbmi (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_avx512vbmi2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx512vbmi2 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_avx512vl", "properties": [{"name": "HELPSTRING", "value": "Qt feature: avx512vl (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_backtrace", "properties": [{"name": "HELPSTRING", "value": "Qt feature: backtrace (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_broken_threadlocal_dtors", "properties": [{"name": "HELPSTRING", "value": "Qt feature: broken_threadlocal_dtors (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_brotli", "properties": [{"name": "HELPSTRING", "value": "Qt feature: brotli (from target Qt6::Network)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_cborstreamreader", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cborstreamreader (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_cborstreamwriter", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cborstreamwriter (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_clipboard", "properties": [{"name": "HELPSTRING", "value": "Qt feature: clipboard (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_clock_gettime", "properties": [{"name": "HELPSTRING", "value": "Qt feature: clock_gettime (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_clock_monotonic", "properties": [{"name": "HELPSTRING", "value": "Qt feature: clock_monotonic (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_colornames", "properties": [{"name": "HELPSTRING", "value": "Qt feature: colornames (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_commandlineparser", "properties": [{"name": "HELPSTRING", "value": "Qt feature: commandlineparser (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_concatenatetablesproxymodel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: concatenatetablesproxymodel (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_concurrent", "properties": [{"name": "HELPSTRING", "value": "Qt feature: concurrent (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_coreaudio", "properties": [{"name": "HELPSTRING", "value": "Qt feature: coreaudio (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_cpp_winrt", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cpp_winrt (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_cross_compile", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cross_compile (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_cssparser", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cssparser (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_ctf", "properties": [{"name": "HELPSTRING", "value": "Qt feature: ctf (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_cursor", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cursor (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_cxx11_future", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cxx11_future (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_cxx17_filesystem", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cxx17_filesystem (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_cxx20", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cxx20 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_cxx20_format", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cxx20_format (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_cxx23_stacktrace", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cxx23_stacktrace (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_cxx2a", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cxx2a (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_cxx2b", "properties": [{"name": "HELPSTRING", "value": "Qt feature: cxx2b (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_datestring", "properties": [{"name": "HELPSTRING", "value": "Qt feature: datestring (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_datetimeparser", "properties": [{"name": "HELPSTRING", "value": "Qt feature: datetimeparser (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_dbus", "properties": [{"name": "HELPSTRING", "value": "Qt feature: dbus (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_dbus_linked", "properties": [{"name": "HELPSTRING", "value": "Qt feature: dbus_linked (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_debug", "properties": [{"name": "HELPSTRING", "value": "Qt feature: debug (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_debug_and_release", "properties": [{"name": "HELPSTRING", "value": "Qt feature: debug_and_release (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_desktopservices", "properties": [{"name": "HELPSTRING", "value": "Qt feature: desktopservices (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_developer_build", "properties": [{"name": "HELPSTRING", "value": "Qt feature: developer_build (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_direct2d", "properties": [{"name": "HELPSTRING", "value": "Qt feature: direct2d (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_direct2d1_1", "properties": [{"name": "HELPSTRING", "value": "Qt feature: direct2d1_1 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_directfb", "properties": [{"name": "HELPSTRING", "value": "Qt feature: directfb (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_directwrite", "properties": [{"name": "HELPSTRING", "value": "Qt feature: directwrite (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_directwrite3", "properties": [{"name": "HELPSTRING", "value": "Qt feature: directwrite3 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_directwritecolrv1", "properties": [{"name": "HELPSTRING", "value": "Qt feature: directwritecolrv1 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_dladdr", "properties": [{"name": "HELPSTRING", "value": "Qt feature: dladdr (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_dlopen", "properties": [{"name": "HELPSTRING", "value": "Qt feature: dlopen (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_dnslookup", "properties": [{"name": "HELPSTRING", "value": "Qt feature: dnslookup (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_doc_snippets", "properties": [{"name": "HELPSTRING", "value": "Qt feature: doc_snippets (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_doubleconversion", "properties": [{"name": "HELPSTRING", "value": "Qt feature: doubleconversion (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_draganddrop", "properties": [{"name": "HELPSTRING", "value": "Qt feature: draganddrop (from target Qt6::<PERSON><PERSON>)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_drm_atomic", "properties": [{"name": "HELPSTRING", "value": "Qt feature: drm_atomic (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_dtls", "properties": [{"name": "HELPSTRING", "value": "Qt feature: dtls (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_dynamicgl", "properties": [{"name": "HELPSTRING", "value": "Qt feature: dynamicgl (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_easingcurve", "properties": [{"name": "HELPSTRING", "value": "Qt feature: easingcurve (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_egl", "properties": [{"name": "HELPSTRING", "value": "Qt feature: egl (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_egl_x11", "properties": [{"name": "HELPSTRING", "value": "Qt feature: egl_x11 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_eglfs", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_eglfs_brcm", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs_brcm (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_eglfs_egldevice", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs_egldevice (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_eglfs_gbm", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs_gbm (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_eglfs_mali", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs_mali (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_eglfs_openwfd", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs_openwfd (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_eglfs_rcar", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs_rcar (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_eglfs_viv", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs_viv (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_eglfs_viv_wl", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs_viv_wl (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_eglfs_vsp2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs_vsp2 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_eglfs_x11", "properties": [{"name": "HELPSTRING", "value": "Qt feature: eglfs_x11 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_elf_private_full_version", "properties": [{"name": "HELPSTRING", "value": "Qt feature: elf_private_full_version (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_emojisegmenter", "properties": [{"name": "HELPSTRING", "value": "Qt feature: emojisegmenter (from target Qt6::<PERSON><PERSON>)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_etw", "properties": [{"name": "HELPSTRING", "value": "Qt feature: etw (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_evdev", "properties": [{"name": "HELPSTRING", "value": "Qt feature: evdev (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_f16c", "properties": [{"name": "HELPSTRING", "value": "Qt feature: f16c (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_ffmpeg", "properties": [{"name": "HELPSTRING", "value": "Qt feature: ffmpeg (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_filesystemiterator", "properties": [{"name": "HELPSTRING", "value": "Qt feature: filesystemiterator (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_filesystemmodel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: filesystemmodel (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_filesystemwatcher", "properties": [{"name": "HELPSTRING", "value": "Qt feature: filesystemwatcher (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_fontconfig", "properties": [{"name": "HELPSTRING", "value": "Qt feature: fontconfig (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_force_asserts", "properties": [{"name": "HELPSTRING", "value": "Qt feature: force_asserts (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_force_bundled_libs", "properties": [{"name": "HELPSTRING", "value": "Qt feature: force_bundled_libs (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_force_debug_info", "properties": [{"name": "HELPSTRING", "value": "Qt feature: force_debug_info (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_force_system_libs", "properties": [{"name": "HELPSTRING", "value": "Qt feature: force_system_libs (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_forkfd_pidfd", "properties": [{"name": "HELPSTRING", "value": "Qt feature: forkfd_pidfd (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_framework", "properties": [{"name": "HELPSTRING", "value": "Qt feature: framework (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_freetype", "properties": [{"name": "HELPSTRING", "value": "Qt feature: freetype (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_futimens", "properties": [{"name": "HELPSTRING", "value": "Qt feature: futimens (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_future", "properties": [{"name": "HELPSTRING", "value": "Qt feature: future (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_gc_binaries", "properties": [{"name": "HELPSTRING", "value": "Qt feature: gc_binaries (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_gestures", "properties": [{"name": "HELPSTRING", "value": "Qt feature: gestures (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_getauxval", "properties": [{"name": "HELPSTRING", "value": "Qt feature: getauxval (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_getentropy", "properties": [{"name": "HELPSTRING", "value": "Qt feature: getentropy (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_getifaddrs", "properties": [{"name": "HELPSTRING", "value": "Qt feature: getifaddrs (from target Qt6::Network)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_gif", "properties": [{"name": "HELPSTRING", "value": "Qt feature: gif (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_glib", "properties": [{"name": "HELPSTRING", "value": "Qt feature: glib (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_glibc_fortify_source", "properties": [{"name": "HELPSTRING", "value": "Qt feature: glibc_fortify_source (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_gpu_vivante", "properties": [{"name": "HELPSTRING", "value": "Qt feature: gpu_vivante (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_graphicsframecapture", "properties": [{"name": "HELPSTRING", "value": "Qt feature: graphicsframecapture (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_gssapi", "properties": [{"name": "HELPSTRING", "value": "Qt feature: gssapi (from target Qt6::Network)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_gstreamer", "properties": [{"name": "HELPSTRING", "value": "Qt feature: gstreamer (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_gstreamer_gl", "properties": [{"name": "HELPSTRING", "value": "Qt feature: gstreamer_gl (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_gstreamer_gl_egl", "properties": [{"name": "HELPSTRING", "value": "Qt feature: gstreamer_gl_egl (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_gstreamer_gl_wayland", "properties": [{"name": "HELPSTRING", "value": "Qt feature: gstreamer_gl_wayland (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_gstreamer_gl_x11", "properties": [{"name": "HELPSTRING", "value": "Qt feature: gstreamer_gl_x11 (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_gstreamer_photography", "properties": [{"name": "HELPSTRING", "value": "Qt feature: gstreamer_photography (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_gui", "properties": [{"name": "HELPSTRING", "value": "Qt feature: gui (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_harfbuzz", "properties": [{"name": "HELPSTRING", "value": "Qt feature: harfbuzz (from target Qt6::<PERSON><PERSON>)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_highdpiscaling", "properties": [{"name": "HELPSTRING", "value": "Qt feature: highdpiscaling (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_hij<PERSON>endar", "properties": [{"name": "HELPSTRING", "value": "Qt feature: hij<PERSON><PERSON>ar (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_http", "properties": [{"name": "HELPSTRING", "value": "Qt feature: http (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_ico", "properties": [{"name": "HELPSTRING", "value": "Qt feature: ico (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_icu", "properties": [{"name": "HELPSTRING", "value": "Qt feature: icu (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_identityproxymodel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: identityproxymodel (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_im", "properties": [{"name": "HELPSTRING", "value": "Qt feature: im (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_image_heuristic_mask", "properties": [{"name": "HELPSTRING", "value": "Qt feature: image_heuristic_mask (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_image_text", "properties": [{"name": "HELPSTRING", "value": "Qt feature: image_text (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_imageformat_bmp", "properties": [{"name": "HELPSTRING", "value": "Qt feature: imageformat_bmp (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_imageformat_jpeg", "properties": [{"name": "HELPSTRING", "value": "Qt feature: imageformat_jpeg (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_imageformat_png", "properties": [{"name": "HELPSTRING", "value": "Qt feature: imageformat_png (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_imageformat_ppm", "properties": [{"name": "HELPSTRING", "value": "Qt feature: imageformat_ppm (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_imageformat_xbm", "properties": [{"name": "HELPSTRING", "value": "Qt feature: imageformat_xbm (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_imageformat_xpm", "properties": [{"name": "HELPSTRING", "value": "Qt feature: imageformat_xpm (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_imageformatplugin", "properties": [{"name": "HELPSTRING", "value": "Qt feature: imageformatplugin (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_imageio_text_loading", "properties": [{"name": "HELPSTRING", "value": "Qt feature: imageio_text_loading (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_inotify", "properties": [{"name": "HELPSTRING", "value": "Qt feature: inotify (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_integrityfb", "properties": [{"name": "HELPSTRING", "value": "Qt feature: integrityfb (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_integrityhid", "properties": [{"name": "HELPSTRING", "value": "Qt feature: <PERSON>hid (from target Qt6::<PERSON>ui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_intelcet", "properties": [{"name": "HELPSTRING", "value": "Qt feature: intelcet (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_ipv6ifname", "properties": [{"name": "HELPSTRING", "value": "Qt feature: ipv6ifname (from target Qt6::Network)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_islamiccivilcalendar", "properties": [{"name": "HELPSTRING", "value": "Qt feature: islamiccivilcalendar (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_itemmodel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: itemmodel (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_jalalicalendar", "properties": [{"name": "HELPSTRING", "value": "Qt feature: jala<PERSON><PERSON>dar (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_journald", "properties": [{"name": "HELPSTRING", "value": "Qt feature: journald (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_jpeg", "properties": [{"name": "HELPSTRING", "value": "Qt feature: jpeg (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_kms", "properties": [{"name": "HELPSTRING", "value": "Qt feature: kms (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_largefile", "properties": [{"name": "HELPSTRING", "value": "Qt feature: largefile (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_lasx", "properties": [{"name": "HELPSTRING", "value": "Qt feature: lasx (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_libcpp_hardening", "properties": [{"name": "HELPSTRING", "value": "Qt feature: libcpp_hardening (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_libinput", "properties": [{"name": "HELPSTRING", "value": "Qt feature: libinput (from target Qt6::<PERSON>ui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_libinput_axis_api", "properties": [{"name": "HELPSTRING", "value": "Qt feature: libinput_axis_api (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_libinput_hires_wheel_support", "properties": [{"name": "HELPSTRING", "value": "Qt feature: libinput_hires_wheel_support (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_libproxy", "properties": [{"name": "HELPSTRING", "value": "Qt feature: libproxy (from target Qt6::Network)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_library", "properties": [{"name": "HELPSTRING", "value": "Qt feature: library (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_libresolv", "properties": [{"name": "HELPSTRING", "value": "Qt feature: libresolv (from target Qt6::Network)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_libstdcpp_assertions", "properties": [{"name": "HELPSTRING", "value": "Qt feature: libstdcpp_assertions (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_libudev", "properties": [{"name": "HELPSTRING", "value": "Qt feature: libudev (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_linkat", "properties": [{"name": "HELPSTRING", "value": "Qt feature: linkat (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_linux_dmabuf", "properties": [{"name": "HELPSTRING", "value": "Qt feature: linux_dmabuf (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_linux_netlink", "properties": [{"name": "HELPSTRING", "value": "Qt feature: linux_netlink (from target Qt6::Network)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_linux_v4l", "properties": [{"name": "HELPSTRING", "value": "Qt feature: linux_v4l (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_linuxfb", "properties": [{"name": "HELPSTRING", "value": "Qt feature: linuxfb (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_localserver", "properties": [{"name": "HELPSTRING", "value": "Qt feature: localserver (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_localtime_r", "properties": [{"name": "HELPSTRING", "value": "Qt feature: localtime_r (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_localtime_s", "properties": [{"name": "HELPSTRING", "value": "Qt feature: localtime_s (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_lsx", "properties": [{"name": "HELPSTRING", "value": "Qt feature: lsx (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_lttng", "properties": [{"name": "HELPSTRING", "value": "Qt feature: lttng (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_memmem", "properties": [{"name": "HELPSTRING", "value": "Qt feature: memmem (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_memrchr", "properties": [{"name": "HELPSTRING", "value": "Qt feature: memrchr (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_metal", "properties": [{"name": "HELPSTRING", "value": "Qt feature: metal (from target Qt6::<PERSON>ui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_mimetype", "properties": [{"name": "HELPSTRING", "value": "Qt feature: mimetype (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_mimetype_database", "properties": [{"name": "HELPSTRING", "value": "Qt feature: mimetype_database (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_mips_dsp", "properties": [{"name": "HELPSTRING", "value": "Qt feature: mips_dsp (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_mips_dspr2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: mips_dspr2 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_mmrenderer", "properties": [{"name": "HELPSTRING", "value": "Qt feature: mmrenderer (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_movie", "properties": [{"name": "HELPSTRING", "value": "Qt feature: movie (from target Qt6::<PERSON><PERSON>)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_mtdev", "properties": [{"name": "HELPSTRING", "value": "Qt feature: mtdev (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_multiprocess", "properties": [{"name": "HELPSTRING", "value": "Qt feature: multiprocess (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_native_android_backend", "properties": [{"name": "HELPSTRING", "value": "Qt feature: native_android_backend (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_neon", "properties": [{"name": "HELPSTRING", "value": "Qt feature: neon (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_network", "properties": [{"name": "HELPSTRING", "value": "Qt feature: network (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_networkdiskcache", "properties": [{"name": "HELPSTRING", "value": "Qt feature: networkdiskcache (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_networkinterface", "properties": [{"name": "HELPSTRING", "value": "Qt feature: networkinterface (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_networklistmanager", "properties": [{"name": "HELPSTRING", "value": "Qt feature: networklistmanager (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_networkproxy", "properties": [{"name": "HELPSTRING", "value": "Qt feature: networkproxy (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_no_direct_extern_access", "properties": [{"name": "HELPSTRING", "value": "Qt feature: no_direct_extern_access (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_no_pkg_config", "properties": [{"name": "HELPSTRING", "value": "Qt feature: no_pkg_config (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_ocsp", "properties": [{"name": "HELPSTRING", "value": "Qt feature: ocsp (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_opengl", "properties": [{"name": "HELPSTRING", "value": "Qt feature: opengl (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_opengles2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: opengles2 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_opengles3", "properties": [{"name": "HELPSTRING", "value": "Qt feature: opengles3 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_opengles31", "properties": [{"name": "HELPSTRING", "value": "Qt feature: opengles31 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_opengles32", "properties": [{"name": "HELPSTRING", "value": "Qt feature: opengles32 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_opensles", "properties": [{"name": "HELPSTRING", "value": "Qt feature: opensles (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_openssl", "properties": [{"name": "HELPSTRING", "value": "Qt feature: openssl (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_openssl_hash", "properties": [{"name": "HELPSTRING", "value": "Qt feature: openssl_hash (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_openssl_linked", "properties": [{"name": "HELPSTRING", "value": "Qt feature: openssl_linked (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_opensslv11", "properties": [{"name": "HELPSTRING", "value": "Qt feature: opensslv11 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_opensslv30", "properties": [{"name": "HELPSTRING", "value": "Qt feature: opensslv30 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_openvg", "properties": [{"name": "HELPSTRING", "value": "Qt feature: openvg (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_pcre2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: pcre2 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_pdf", "properties": [{"name": "HELPSTRING", "value": "Qt feature: pdf (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_permissions", "properties": [{"name": "HELPSTRING", "value": "Qt feature: permissions (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_picture", "properties": [{"name": "HELPSTRING", "value": "Qt feature: picture (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_pipewire", "properties": [{"name": "HELPSTRING", "value": "Qt feature: pipewire (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_pipewire_screencapture", "properties": [{"name": "HELPSTRING", "value": "Qt feature: pipewire_screencapture (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_pkg_config", "properties": [{"name": "HELPSTRING", "value": "Qt feature: pkg_config (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_plugin_manifest", "properties": [{"name": "HELPSTRING", "value": "Qt feature: plugin_manifest (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_png", "properties": [{"name": "HELPSTRING", "value": "Qt feature: png (from target Qt6::<PERSON>ui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_poll_exit_on_error", "properties": [{"name": "HELPSTRING", "value": "Qt feature: poll_exit_on_error (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_poll_poll", "properties": [{"name": "HELPSTRING", "value": "Qt feature: poll_poll (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_poll_pollts", "properties": [{"name": "HELPSTRING", "value": "Qt feature: poll_pollts (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_poll_ppoll", "properties": [{"name": "HELPSTRING", "value": "Qt feature: poll_ppoll (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_poll_select", "properties": [{"name": "HELPSTRING", "value": "Qt feature: poll_select (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_posix_fallocate", "properties": [{"name": "HELPSTRING", "value": "Qt feature: posix_fallocate (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_posix_sem", "properties": [{"name": "HELPSTRING", "value": "Qt feature: posix_sem (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_posix_shm", "properties": [{"name": "HELPSTRING", "value": "Qt feature: posix_shm (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_precompile_header", "properties": [{"name": "HELPSTRING", "value": "Qt feature: precompile_header (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_printsupport", "properties": [{"name": "HELPSTRING", "value": "Qt feature: printsupport (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_private_tests", "properties": [{"name": "HELPSTRING", "value": "Qt feature: private_tests (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_process", "properties": [{"name": "HELPSTRING", "value": "Qt feature: process (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_processenvironment", "properties": [{"name": "HELPSTRING", "value": "Qt feature: processenvironment (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_proxymodel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: proxymodel (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_pthread_clockjoin", "properties": [{"name": "HELPSTRING", "value": "Qt feature: pthread_clockjoin (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_pthread_condattr_setclock", "properties": [{"name": "HELPSTRING", "value": "Qt feature: pthread_condattr_setclock (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_pthread_timedjoin", "properties": [{"name": "HELPSTRING", "value": "Qt feature: pthread_timedjoin (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_publicsuffix_qt", "properties": [{"name": "HELPSTRING", "value": "Qt feature: publicsuffix_qt (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_publicsuffix_system", "properties": [{"name": "HELPSTRING", "value": "Qt feature: publicsuffix_system (from target Qt6::Network)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_pulseaudio", "properties": [{"name": "HELPSTRING", "value": "Qt feature: pulseaudio (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_qml_animation", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_animation (from target Qt6::Qml)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_debug", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_debug (from target Qt6::Qml)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_delegate_model", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_delegate_model (from target Qt6::QmlModels)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_itemmodel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_itemmodel (from target Qt6::Qml)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_jit", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_jit (from target Qt6::Qml)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_list_model", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_list_model (from target Qt6::QmlModels)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_locale", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_locale (from target Qt6::Qml)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_network", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_network (from target Qt6::Qml)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_object_model", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_object_model (from target Qt6::QmlModels)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_preview", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_preview (from target Qt6::Qml)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_profiler", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_profiler (from target Qt6::Qml)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_python", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_python (from target Qt6::Qml)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_ssl", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_ssl (from target Qt6::Qml)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_table_model", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_table_model (from target Qt6::QmlModels)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_type_loader_thread", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_type_loader_thread (from target Qt6::Qml)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_worker_script", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_worker_script (from target Qt6::Qml)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_xml_http_request", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_xml_http_request (from target Qt6::Qml)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qml_xmllistmodel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qml_xmllistmodel (from target Qt6::Qml)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_qqnx_imf", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qqnx_imf (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_qqnx_pps", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qqnx_pps (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_qtgui_threadpool", "properties": [{"name": "HELPSTRING", "value": "Qt feature: qtgui_threadpool (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_animatedimage", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_animatedimage (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_canvas", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_canvas (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_designer", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_designer (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_draganddrop", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_draganddrop (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_flipable", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_flipable (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_gridview", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_gridview (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_itemview", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_itemview (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_listview", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_listview (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_particles", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_particles (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_path", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_path (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_pathview", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_pathview (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_pixmap_cache_threaded_download", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_pixmap_cache_threaded_download (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_positioners", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_positioners (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_repeater", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_repeater (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_shadereffect", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_shadereffect (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_sprite", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_sprite (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_tableview", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_tableview (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_treeview", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_treeview (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_quick_viewtransitions", "properties": [{"name": "HELPSTRING", "value": "Qt feature: quick_viewtransitions (from target Qt6::Quick)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_raster_64bit", "properties": [{"name": "HELPSTRING", "value": "Qt feature: raster_64bit (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_raster_fp", "properties": [{"name": "HELPSTRING", "value": "Qt feature: raster_fp (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_rdrnd", "properties": [{"name": "HELPSTRING", "value": "Qt feature: rdrnd (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_rdseed", "properties": [{"name": "HELPSTRING", "value": "Qt feature: rdseed (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_reduce_exports", "properties": [{"name": "HELPSTRING", "value": "Qt feature: reduce_exports (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_reduce_relocations", "properties": [{"name": "HELPSTRING", "value": "Qt feature: reduce_relocations (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_regularexpression", "properties": [{"name": "HELPSTRING", "value": "Qt feature: regularexpression (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_relocatable", "properties": [{"name": "HELPSTRING", "value": "Qt feature: relocatable (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_relro_now_linker", "properties": [{"name": "HELPSTRING", "value": "Qt feature: relro_now_linker (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_renameat2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: renameat2 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_res_setservers", "properties": [{"name": "HELPSTRING", "value": "Qt feature: res_setservers (from target Qt6::Network)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_rpath", "properties": [{"name": "HELPSTRING", "value": "Qt feature: rpath (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_schannel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: schannel (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_sctp", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sctp (from target Qt6::Network)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_securetransport", "properties": [{"name": "HELPSTRING", "value": "Qt feature: securetransport (from target Qt6::Network)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_separate_debug_info", "properties": [{"name": "HELPSTRING", "value": "Qt feature: separate_debug_info (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_sessionmanager", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sessionmanager (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_settings", "properties": [{"name": "HELPSTRING", "value": "Qt feature: settings (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_sha3_fast", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sha3_fast (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_shani", "properties": [{"name": "HELPSTRING", "value": "Qt feature: shani (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_shared", "properties": [{"name": "HELPSTRING", "value": "Qt feature: shared (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_sharedmemory", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sharedmemory (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_shortcut", "properties": [{"name": "HELPSTRING", "value": "Qt feature: shortcut (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_signaling_nan", "properties": [{"name": "HELPSTRING", "value": "Qt feature: signaling_nan (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_simulator_and_device", "properties": [{"name": "HELPSTRING", "value": "Qt feature: simulator_and_device (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_slog2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: slog2 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_socks5", "properties": [{"name": "HELPSTRING", "value": "Qt feature: socks5 (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_sortfilterproxymodel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sortfilterproxymodel (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_spatialaudio", "properties": [{"name": "HELPSTRING", "value": "Qt feature: spatialaudio (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_spatialaudio_quick3d", "properties": [{"name": "HELPSTRING", "value": "Qt feature: spatialaudio_quick3d (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_sql", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sql (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_sse2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sse2 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_sse3", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sse3 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_sse4_1", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sse4_1 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_sse4_2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sse4_2 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_ssl", "properties": [{"name": "HELPSTRING", "value": "Qt feature: ssl (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_sspi", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sspi (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_ssse3", "properties": [{"name": "HELPSTRING", "value": "Qt feature: ssse3 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_stack_clash_protection", "properties": [{"name": "HELPSTRING", "value": "Qt feature: stack_clash_protection (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_stack_protector", "properties": [{"name": "HELPSTRING", "value": "Qt feature: stack_protector (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_standarditemmodel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: standarditemmodel (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_static", "properties": [{"name": "HELPSTRING", "value": "Qt feature: static (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_std_atomic64", "properties": [{"name": "HELPSTRING", "value": "Qt feature: std_atomic64 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_stdlib_libcpp", "properties": [{"name": "HELPSTRING", "value": "Qt feature: stdlib_libcpp (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_stringlistmodel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: stringlistmodel (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_syslog", "properties": [{"name": "HELPSTRING", "value": "Qt feature: syslog (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_system_doubleconversion", "properties": [{"name": "HELPSTRING", "value": "Qt feature: system_doubleconversion (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_system_freetype", "properties": [{"name": "HELPSTRING", "value": "Qt feature: system_freetype (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_system_harfbuzz", "properties": [{"name": "HELPSTRING", "value": "Qt feature: system_harfbuzz (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_system_jpeg", "properties": [{"name": "HELPSTRING", "value": "Qt feature: system_jpeg (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_system_libb2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: system_libb2 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_system_pcre2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: system_pcre2 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_system_png", "properties": [{"name": "HELPSTRING", "value": "Qt feature: system_png (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_system_proxies", "properties": [{"name": "HELPSTRING", "value": "Qt feature: system_proxies (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_system_textmarkdownreader", "properties": [{"name": "HELPSTRING", "value": "Qt feature: system_textmarkdownreader (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_system_xcb_xinput", "properties": [{"name": "HELPSTRING", "value": "Qt feature: system_xcb_xinput (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_system_zlib", "properties": [{"name": "HELPSTRING", "value": "Qt feature: system_zlib (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_systemsemaphore", "properties": [{"name": "HELPSTRING", "value": "Qt feature: systemsemaphore (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_systemtrayicon", "properties": [{"name": "HELPSTRING", "value": "Qt feature: systemtrayicon (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_sysv_sem", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sysv_sem (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_sysv_shm", "properties": [{"name": "HELPSTRING", "value": "Qt feature: sysv_shm (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_tabletevent", "properties": [{"name": "HELPSTRING", "value": "Qt feature: tabletevent (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_temporaryfile", "properties": [{"name": "HELPSTRING", "value": "Qt feature: temporaryfile (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_test_gui", "properties": [{"name": "HELPSTRING", "value": "Qt feature: test_gui (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_testlib", "properties": [{"name": "HELPSTRING", "value": "Qt feature: testlib (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_textdate", "properties": [{"name": "HELPSTRING", "value": "Qt feature: textdate (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_texthtmlparser", "properties": [{"name": "HELPSTRING", "value": "Qt feature: texthtmlparser (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_textmarkdownreader", "properties": [{"name": "HELPSTRING", "value": "Qt feature: textmarkdownreader (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_textmarkdownwriter", "properties": [{"name": "HELPSTRING", "value": "Qt feature: textmarkdownwriter (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_textodfwriter", "properties": [{"name": "HELPSTRING", "value": "Qt feature: textodfwriter (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_thread", "properties": [{"name": "HELPSTRING", "value": "Qt feature: thread (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_timezone", "properties": [{"name": "HELPSTRING", "value": "Qt feature: timezone (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_timezone_locale", "properties": [{"name": "HELPSTRING", "value": "Qt feature: timezone_locale (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_timezone_tzdb", "properties": [{"name": "HELPSTRING", "value": "Qt feature: timezone_tzdb (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_topleveldomain", "properties": [{"name": "HELPSTRING", "value": "Qt feature: topleveldomain (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_translation", "properties": [{"name": "HELPSTRING", "value": "Qt feature: translation (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_transposeproxymodel", "properties": [{"name": "HELPSTRING", "value": "Qt feature: transposeproxymodel (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_trivial_auto_var_init_pattern", "properties": [{"name": "HELPSTRING", "value": "Qt feature: trivial_auto_var_init_pattern (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_tslib", "properties": [{"name": "HELPSTRING", "value": "Qt feature: tslib (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_tuiotouch", "properties": [{"name": "HELPSTRING", "value": "Qt feature: tui<PERSON>uch (from target Qt6::<PERSON><PERSON>)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_udpsocket", "properties": [{"name": "HELPSTRING", "value": "Qt feature: udpsocket (from target Qt6::Network)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_undocommand", "properties": [{"name": "HELPSTRING", "value": "Qt feature: undocommand (from target Qt6::<PERSON><PERSON>)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_undogroup", "properties": [{"name": "HELPSTRING", "value": "Qt feature: undogroup (from target Qt6::<PERSON>ui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_undostack", "properties": [{"name": "HELPSTRING", "value": "Qt feature: undostack (from target Qt6::<PERSON><PERSON>)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_use_bfd_linker", "properties": [{"name": "HELPSTRING", "value": "Qt feature: use_bfd_linker (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_use_gold_linker", "properties": [{"name": "HELPSTRING", "value": "Qt feature: use_gold_linker (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_use_lld_linker", "properties": [{"name": "HELPSTRING", "value": "Qt feature: use_lld_linker (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_use_mold_linker", "properties": [{"name": "HELPSTRING", "value": "Qt feature: use_mold_linker (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_vaapi", "properties": [{"name": "HELPSTRING", "value": "Qt feature: vaapi (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_vaes", "properties": [{"name": "HELPSTRING", "value": "Qt feature: vaes (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_validator", "properties": [{"name": "HELPSTRING", "value": "Qt feature: validator (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_version_tagging", "properties": [{"name": "HELPSTRING", "value": "Qt feature: version_tagging (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_videotoolbox", "properties": [{"name": "HELPSTRING", "value": "Qt feature: videotoolbox (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_vkgen", "properties": [{"name": "HELPSTRING", "value": "Qt feature: vkgen (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_vkkhrdisplay", "properties": [{"name": "HELPSTRING", "value": "Qt feature: vk<PERSON>rdisplay (from target Qt6::<PERSON><PERSON>)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_vnc", "properties": [{"name": "HELPSTRING", "value": "Qt feature: vnc (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_vsp2", "properties": [{"name": "HELPSTRING", "value": "Qt feature: vsp2 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_vulkan", "properties": [{"name": "HELPSTRING", "value": "Qt feature: vulkan (from target Qt6::<PERSON>ui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_vxworksevdev", "properties": [{"name": "HELPSTRING", "value": "Qt feature: vxworksevdev (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_wasm", "properties": [{"name": "HELPSTRING", "value": "Qt feature: wasm (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_wasm_exceptions", "properties": [{"name": "HELPSTRING", "value": "Qt feature: wasm_exceptions (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_wasm_jspi", "properties": [{"name": "HELPSTRING", "value": "Qt feature: wasm_jspi (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_wasm_simd128", "properties": [{"name": "HELPSTRING", "value": "Qt feature: wasm_simd128 (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_wayland", "properties": [{"name": "HELPSTRING", "value": "Qt feature: wayland (from target Qt6::<PERSON><PERSON>)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_whatsthis", "properties": [{"name": "HELPSTRING", "value": "Qt feature: whatsthis (from target Qt6::<PERSON><PERSON>)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_wheelevent", "properties": [{"name": "HELPSTRING", "value": "Qt feature: wheelevent (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_widgets", "properties": [{"name": "HELPSTRING", "value": "Qt feature: widgets (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_wmf", "properties": [{"name": "HELPSTRING", "value": "Qt feature: wmf (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_wmsdk", "properties": [{"name": "HELPSTRING", "value": "Qt feature: wmsdk (from target Qt6::Multimedia)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_x86intrin", "properties": [{"name": "HELPSTRING", "value": "Qt feature: x86intrin (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_xcb", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xcb (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_xcb_egl_plugin", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xcb_egl_plugin (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_xcb_glx", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xcb_glx (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_xcb_glx_plugin", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xcb_glx_plugin (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_xcb_native_painting", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xcb_native_painting (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_xcb_sm", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xcb_sm (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_xcb_xlib", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xcb_xlib (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_xkbcommon", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xkbcommon (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_xkbcommon_x11", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xkbcommon_x11 (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_xlib", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xlib (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_xml", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xml (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_xmlstream", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xmlstream (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_xmlstreamreader", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xmlstreamreader (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_xmlstreamwriter", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xmlstreamwriter (from target Qt6::Core)"}], "type": "INTERNAL", "value": "ON"}, {"name": "QT_FEATURE_xrender", "properties": [{"name": "HELPSTRING", "value": "Qt feature: xrender (from target Qt6::Gui)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_FEATURE_zstd", "properties": [{"name": "HELPSTRING", "value": "Qt feature: zstd (from target Qt6::Core)"}], "type": "INTERNAL", "value": "OFF"}, {"name": "QT_QMAKE_EXECUTABLE", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "FILEPATH", "value": ""}, {"name": "QT_TOOL_COMMAND_WRAPPER_PATH", "properties": [{"name": "HELPSTRING", "value": "Path to the wrapper of the tool commands"}], "type": "INTERNAL", "value": "C:/Qt/file/palyer/build/.qt/bin/qt_setup_tool_path.bat"}, {"name": "Qt6ConcurrentPrivate_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6ConcurrentPrivate."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6ConcurrentPrivate"}, {"name": "Qt6Concurrent_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6Concurrent."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Concurrent"}, {"name": "Qt6CorePrivate_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6CorePrivate."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6CorePrivate"}, {"name": "Qt6CoreTools_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6CoreTools."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6CoreTools"}, {"name": "Qt6Core_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6Core."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core"}, {"name": "Qt6EntryPointPrivate_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6EntryPointPrivate."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6EntryPointPrivate"}, {"name": "Qt6ExamplesAssetDownloaderPrivate_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6ExamplesAssetDownloaderPrivate."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6ExamplesAssetDownloaderPrivate"}, {"name": "Qt6GuiPrivate_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6GuiPrivate."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6GuiPrivate"}, {"name": "Qt6GuiTools_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6GuiTools."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6GuiTools"}, {"name": "Qt6Gui_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6Gui."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Gui"}, {"name": "Qt6HttpServerPrivate_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6HttpServerPrivate."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6HttpServerPrivate"}, {"name": "Qt6HttpServer_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6HttpServer."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6HttpServer"}, {"name": "Qt6MultimediaPrivate_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6MultimediaPrivate."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6MultimediaPrivate"}, {"name": "Qt6Multimedia_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6Multimedia."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Multimedia"}, {"name": "Qt6NetworkPrivate_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6NetworkPrivate."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6NetworkPrivate"}, {"name": "Qt6Network_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6Network."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Network"}, {"name": "Qt6OpenGLPrivate_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6OpenGLPrivate."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6OpenGLPrivate"}, {"name": "Qt6OpenGL_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6OpenGL."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6OpenGL"}, {"name": "Qt6QmlAssetDownloaderPrivate_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6QmlAssetDownloaderPrivate."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6QmlAssetDownloaderPrivate"}, {"name": "Qt6QmlAssetDownloader_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6QmlAssetDownloader."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6QmlAssetDownloader"}, {"name": "Qt6QmlCompilerPlusPrivateTools_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6QmlCompilerPlusPrivateTools."}], "type": "PATH", "value": "Qt6QmlCompilerPlusPrivateTools_DIR-NOTFOUND"}, {"name": "Qt6QmlIntegrationPrivate_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6QmlIntegrationPrivate."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6QmlIntegrationPrivate"}, {"name": "Qt6QmlIntegration_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6QmlIntegration."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6QmlIntegration"}, {"name": "Qt6QmlMetaPrivate_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6QmlMetaPrivate."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6QmlMetaPrivate"}, {"name": "Qt6QmlMeta_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6QmlMeta."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6QmlMeta"}, {"name": "Qt6QmlModelsPrivate_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6QmlModelsPrivate."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6QmlModelsPrivate"}, {"name": "Qt6QmlModels_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6QmlModels."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6QmlModels"}, {"name": "Qt6QmlPrivate_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6QmlPrivate."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6QmlPrivate"}, {"name": "Qt6QmlTools_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6QmlTools."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6QmlTools"}, {"name": "Qt6QmlWorkerScriptPrivate_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6QmlWorkerScriptPrivate."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6QmlWorkerScriptPrivate"}, {"name": "Qt6QmlWorkerScript_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6QmlWorkerScript."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6QmlWorkerScript"}, {"name": "Qt6Qml_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6Qml."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Qml"}, {"name": "Qt6QuickEffectsPrivate_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6QuickEffectsPrivate."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6QuickEffectsPrivate"}, {"name": "Qt6QuickPrivate_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6QuickPrivate."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6QuickPrivate"}, {"name": "Qt6QuickTools_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6QuickTools."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6QuickTools"}, {"name": "Qt6Quick_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6Quick."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Quick"}, {"name": "Qt6WebSocketsPrivate_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6WebSocketsPrivate."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6WebSocketsPrivate"}, {"name": "Qt6WebSockets_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6WebSockets."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6WebSockets"}, {"name": "Qt6ZlibPrivate_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6ZlibPrivate."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6ZlibPrivate"}, {"name": "Qt6_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6"}, {"name": "Qt6quickeffects_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Qt6quickeffects."}], "type": "PATH", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6QuickEffects"}, {"name": "Vulkan_GLSLANG_VALIDATOR_EXECUTABLE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "Vulkan_GLSLANG_VALIDATOR_EXECUTABLE-NOTFOUND"}, {"name": "Vulkan_GLSLC_EXECUTABLE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "Vulkan_GLSLC_EXECUTABLE-NOTFOUND"}, {"name": "Vulkan_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "Vulkan_INCLUDE_DIR-NOTFOUND"}, {"name": "Vulkan_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "Vulkan_LIBRARY-NOTFOUND"}, {"name": "WINDEPLOYQT_EXECUTABLE", "properties": [{"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "C:/Qt/6.9.1/mingw_64/bin/windeployqt.exe"}, {"name": "_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX", "properties": [{"name": "HELPSTRING", "value": "CMAKE_INSTALL_PREFIX during last run"}], "type": "INTERNAL", "value": "C:/Program Files/palyer"}, {"name": "__qt_qml_macros_module_base_dir", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Qml"}, {"name": "palyer_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "C:/Qt/file/palyer/build"}, {"name": "palyer_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "ON"}, {"name": "palyer_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "C:/Qt/file/palyer"}], "kind": "cache", "version": {"major": 2, "minor": 0}}