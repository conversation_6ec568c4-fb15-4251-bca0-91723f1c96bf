# Backend模块
set(CMAKE_CXX_STANDARD 17)
# 设置目录结构
set(SOURCES_DIR "${CMAKE_CURRENT_SOURCE_DIR}")
set(HEADERS_DIR "${CMAKE_CURRENT_SOURCE_DIR}")

# 查找Qt组件
find_package(Qt6 REQUIRED COMPONENTS Core Gui Quick Concurrent Network Multimedia)

# 验证关键网络模块
if(NOT TARGET Qt6::Network)
    message(FATAL_ERROR "Qt6::Network target not found - check Qt installation")
endif()

# 设置WebP工具路径
set(WEBP_BIN_DIR "${CMAKE_SOURCE_DIR}/lib/libwebp-1.5.0-windows-x64/bin")

# 收集源文件
file(GLOB HEADER_FILES CONFIGURE_DEPENDS "${HEADERS_DIR}/*.h" "${HEADERS_DIR}/*/*.h")
file(GLOB SOURCE_FILES CONFIGURE_DEPENDS "${SOURCES_DIR}/*.cpp" "${SOURCES_DIR}/*/*.cpp")

set(BACKEND_SOURCES ${HEADER_FILES} ${SOURCE_FILES})

# 创建动态库
add_library(backend SHARED ${BACKEND_SOURCES})

# 添加宏定义
target_compile_definitions(backend
    PRIVATE
    BACKEND_LIBRARY
    QT_DEPRECATED_WARNINGS
)

# 设置库的属性
set_target_properties(backend PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
)

# 设置包含目录
target_include_directories(backend PUBLIC
    ${HEADERS_DIR}
    ${BACKEND_DEP_INCLUDES}
)

# 链接Qt模块 - 使用现代CMake最佳实践
target_link_libraries(backend
    PRIVATE
        Qt6::Core
        Qt6::Concurrent
        Qt6::Gui
        Qt6::Quick
        Qt6::Network
        Qt6::Multimedia
        ${BACKEND_DEP_LIBS}
)

# 安装规则
install(TARGETS backend
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)

# 复制WebP工具到输出目录
add_custom_command(TARGET backend POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${WEBP_BIN_DIR}/dwebp.exe"
        $<TARGET_FILE_DIR:palyer>
    COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${WEBP_BIN_DIR}/cwebp.exe"
        $<TARGET_FILE_DIR:palyer>
    COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${CMAKE_SOURCE_DIR}/lib/ffmpeg2/ffmpeg.exe"
        $<TARGET_FILE_DIR:palyer>
    COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${CMAKE_SOURCE_DIR}/lib/windows-artifacts/avifdec.exe"
        $<TARGET_FILE_DIR:palyer>
    COMMAND ${CMAKE_COMMAND} -E copy_if_different
        $<TARGET_FILE:backend>
        $<TARGET_FILE_DIR:palyer>
    COMMENT "Copying external tools to build directory"
)
