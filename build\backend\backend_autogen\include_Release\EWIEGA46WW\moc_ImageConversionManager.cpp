/****************************************************************************
** Meta object code from reading C++ file 'ImageConversionManager.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.1)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../backend/ImageConversionManager.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ImageConversionManager.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.1. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN22ImageConversionManagerE_t {};
} // unnamed namespace

template <> constexpr inline auto ImageConversionManager::qt_create_metaobjectdata<qt_meta_tag_ZN22ImageConversionManagerE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ImageConversionManager",
        "isRunningChanged",
        "",
        "isPausedChanged",
        "isScanningChanged",
        "maxConcurrentTasksChanged",
        "conversionFinished",
        "fileConverted",
        "index",
        "newPath",
        "fileFailed",
        "fileConvertedWithSize",
        "originalSize",
        "newSize",
        "fileSizeIncreasedAfterConversion",
        "path",
        "fileSizeIncreasedWithSize",
        "fileCorrupted",
        "reason",
        "taskListCleared",
        "scanningStarted",
        "folderPath",
        "scanningProgress",
        "fileCount",
        "scanningFinished",
        "QVariantList",
        "fileList",
        "deleteFilesStarted",
        "deleteFilesCompleted",
        "QVariantMap",
        "result",
        "scanningCancelled",
        "scanningError",
        "errorMessage",
        "handleTaskFinished",
        "onFileSizeIncreasedWithSize",
        "onScanningFinished",
        "processFailedFilesQueue",
        "scanConvertibleFiles",
        "includeSubfolders",
        "targetFormat",
        "getConvertibleFiles",
        "startConversion",
        "QVariant",
        "fileListModel",
        "quality",
        "outputDir",
        "pauseConversion",
        "resumeConversion",
        "cancelConversion",
        "deleteFiles",
        "filePatterns",
        "deleteFilesAsync",
        "clearTaskList",
        "cancelScanning",
        "normalizePath",
        "getFileInfo",
        "filePath",
        "setThreadCount",
        "n",
        "setConversionThreads",
        "setThumbnailThreads",
        "isRunning",
        "isPaused",
        "maxConcurrentTasks",
        "isScanning"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'isRunningChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'isPausedChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'isScanningChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'maxConcurrentTasksChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'conversionFinished'
        QtMocHelpers::SignalData<void()>(6, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'fileConverted'
        QtMocHelpers::SignalData<void(int, const QString &)>(7, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 8 }, { QMetaType::QString, 9 },
        }}),
        // Signal 'fileFailed'
        QtMocHelpers::SignalData<void(int)>(10, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 8 },
        }}),
        // Signal 'fileConvertedWithSize'
        QtMocHelpers::SignalData<void(int, const QString &, qint64, qint64)>(11, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 8 }, { QMetaType::QString, 9 }, { QMetaType::LongLong, 12 }, { QMetaType::LongLong, 13 },
        }}),
        // Signal 'fileSizeIncreasedAfterConversion'
        QtMocHelpers::SignalData<void(int, const QString &)>(14, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 8 }, { QMetaType::QString, 15 },
        }}),
        // Signal 'fileSizeIncreasedWithSize'
        QtMocHelpers::SignalData<void(int, const QString &, qint64, qint64)>(16, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 8 }, { QMetaType::QString, 15 }, { QMetaType::LongLong, 12 }, { QMetaType::LongLong, 13 },
        }}),
        // Signal 'fileCorrupted'
        QtMocHelpers::SignalData<void(int, const QString &, const QString &)>(17, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 8 }, { QMetaType::QString, 15 }, { QMetaType::QString, 18 },
        }}),
        // Signal 'taskListCleared'
        QtMocHelpers::SignalData<void()>(19, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'scanningStarted'
        QtMocHelpers::SignalData<void(const QString &)>(20, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 21 },
        }}),
        // Signal 'scanningProgress'
        QtMocHelpers::SignalData<void(int)>(22, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 23 },
        }}),
        // Signal 'scanningFinished'
        QtMocHelpers::SignalData<void(const QVariantList &)>(24, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 25, 26 },
        }}),
        // Signal 'deleteFilesStarted'
        QtMocHelpers::SignalData<void()>(27, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'deleteFilesCompleted'
        QtMocHelpers::SignalData<void(const QVariantMap &)>(28, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 29, 30 },
        }}),
        // Signal 'scanningCancelled'
        QtMocHelpers::SignalData<void()>(31, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'scanningError'
        QtMocHelpers::SignalData<void(const QString &)>(32, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 33 },
        }}),
        // Slot 'handleTaskFinished'
        QtMocHelpers::SlotData<void()>(34, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onFileSizeIncreasedWithSize'
        QtMocHelpers::SlotData<void(int, const QString &, qint64, qint64)>(35, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Int, 8 }, { QMetaType::QString, 15 }, { QMetaType::LongLong, 12 }, { QMetaType::LongLong, 13 },
        }}),
        // Slot 'onScanningFinished'
        QtMocHelpers::SlotData<void()>(36, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'processFailedFilesQueue'
        QtMocHelpers::SlotData<void()>(37, 2, QMC::AccessPrivate, QMetaType::Void),
        // Method 'scanConvertibleFiles'
        QtMocHelpers::MethodData<void(const QString &, bool, const QString &)>(38, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 21 }, { QMetaType::Bool, 39 }, { QMetaType::QString, 40 },
        }}),
        // Method 'scanConvertibleFiles'
        QtMocHelpers::MethodData<void(const QString &, bool)>(38, 2, QMC::AccessPublic | QMC::MethodCloned, QMetaType::Void, {{
            { QMetaType::QString, 21 }, { QMetaType::Bool, 39 },
        }}),
        // Method 'getConvertibleFiles'
        QtMocHelpers::MethodData<QVariantList(const QString &, bool)>(41, 2, QMC::AccessPublic, 0x80000000 | 25, {{
            { QMetaType::QString, 21 }, { QMetaType::Bool, 39 },
        }}),
        // Method 'startConversion'
        QtMocHelpers::MethodData<void(const QVariant &, const QString &, int, const QString &)>(42, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 43, 44 }, { QMetaType::QString, 40 }, { QMetaType::Int, 45 }, { QMetaType::QString, 46 },
        }}),
        // Method 'pauseConversion'
        QtMocHelpers::MethodData<void()>(47, 2, QMC::AccessPublic, QMetaType::Void),
        // Method 'resumeConversion'
        QtMocHelpers::MethodData<void()>(48, 2, QMC::AccessPublic, QMetaType::Void),
        // Method 'cancelConversion'
        QtMocHelpers::MethodData<void()>(49, 2, QMC::AccessPublic, QMetaType::Void),
        // Method 'deleteFiles'
        QtMocHelpers::MethodData<QVariantMap(const QString &, const QVariantList &, bool)>(50, 2, QMC::AccessPublic, 0x80000000 | 29, {{
            { QMetaType::QString, 21 }, { 0x80000000 | 25, 51 }, { QMetaType::Bool, 39 },
        }}),
        // Method 'deleteFilesAsync'
        QtMocHelpers::MethodData<void(const QString &, const QVariantList &, bool)>(52, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 21 }, { 0x80000000 | 25, 51 }, { QMetaType::Bool, 39 },
        }}),
        // Method 'clearTaskList'
        QtMocHelpers::MethodData<bool()>(53, 2, QMC::AccessPublic, QMetaType::Bool),
        // Method 'cancelScanning'
        QtMocHelpers::MethodData<void()>(54, 2, QMC::AccessPublic, QMetaType::Void),
        // Method 'normalizePath'
        QtMocHelpers::MethodData<QString(const QString &) const>(55, 2, QMC::AccessPublic, QMetaType::QString, {{
            { QMetaType::QString, 15 },
        }}),
        // Method 'getFileInfo'
        QtMocHelpers::MethodData<QVariantMap(const QString &) const>(56, 2, QMC::AccessPublic, 0x80000000 | 29, {{
            { QMetaType::QString, 57 },
        }}),
        // Method 'setThreadCount'
        QtMocHelpers::MethodData<void(int)>(58, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 59 },
        }}),
        // Method 'setConversionThreads'
        QtMocHelpers::MethodData<void(int)>(60, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 59 },
        }}),
        // Method 'setThumbnailThreads'
        QtMocHelpers::MethodData<void(int)>(61, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 59 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'isRunning'
        QtMocHelpers::PropertyData<bool>(62, QMetaType::Bool, QMC::DefaultPropertyFlags, 0),
        // property 'isPaused'
        QtMocHelpers::PropertyData<bool>(63, QMetaType::Bool, QMC::DefaultPropertyFlags, 1),
        // property 'maxConcurrentTasks'
        QtMocHelpers::PropertyData<int>(64, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 3),
        // property 'isScanning'
        QtMocHelpers::PropertyData<bool>(65, QMetaType::Bool, QMC::DefaultPropertyFlags, 2),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ImageConversionManager, qt_meta_tag_ZN22ImageConversionManagerE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject ImageConversionManager::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN22ImageConversionManagerE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN22ImageConversionManagerE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN22ImageConversionManagerE_t>.metaTypes,
    nullptr
} };

void ImageConversionManager::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ImageConversionManager *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->isRunningChanged(); break;
        case 1: _t->isPausedChanged(); break;
        case 2: _t->isScanningChanged(); break;
        case 3: _t->maxConcurrentTasksChanged(); break;
        case 4: _t->conversionFinished(); break;
        case 5: _t->fileConverted((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 6: _t->fileFailed((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 7: _t->fileConvertedWithSize((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<qint64>>(_a[3])),(*reinterpret_cast< std::add_pointer_t<qint64>>(_a[4]))); break;
        case 8: _t->fileSizeIncreasedAfterConversion((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 9: _t->fileSizeIncreasedWithSize((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<qint64>>(_a[3])),(*reinterpret_cast< std::add_pointer_t<qint64>>(_a[4]))); break;
        case 10: _t->fileCorrupted((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[3]))); break;
        case 11: _t->taskListCleared(); break;
        case 12: _t->scanningStarted((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 13: _t->scanningProgress((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 14: _t->scanningFinished((*reinterpret_cast< std::add_pointer_t<QVariantList>>(_a[1]))); break;
        case 15: _t->deleteFilesStarted(); break;
        case 16: _t->deleteFilesCompleted((*reinterpret_cast< std::add_pointer_t<QVariantMap>>(_a[1]))); break;
        case 17: _t->scanningCancelled(); break;
        case 18: _t->scanningError((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 19: _t->handleTaskFinished(); break;
        case 20: _t->onFileSizeIncreasedWithSize((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<qint64>>(_a[3])),(*reinterpret_cast< std::add_pointer_t<qint64>>(_a[4]))); break;
        case 21: _t->onScanningFinished(); break;
        case 22: _t->processFailedFilesQueue(); break;
        case 23: _t->scanConvertibleFiles((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<bool>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[3]))); break;
        case 24: _t->scanConvertibleFiles((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<bool>>(_a[2]))); break;
        case 25: { QVariantList _r = _t->getConvertibleFiles((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<bool>>(_a[2])));
            if (_a[0]) *reinterpret_cast< QVariantList*>(_a[0]) = std::move(_r); }  break;
        case 26: _t->startConversion((*reinterpret_cast< std::add_pointer_t<QVariant>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[3])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[4]))); break;
        case 27: _t->pauseConversion(); break;
        case 28: _t->resumeConversion(); break;
        case 29: _t->cancelConversion(); break;
        case 30: { QVariantMap _r = _t->deleteFiles((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QVariantList>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<bool>>(_a[3])));
            if (_a[0]) *reinterpret_cast< QVariantMap*>(_a[0]) = std::move(_r); }  break;
        case 31: _t->deleteFilesAsync((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QVariantList>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<bool>>(_a[3]))); break;
        case 32: { bool _r = _t->clearTaskList();
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 33: _t->cancelScanning(); break;
        case 34: { QString _r = _t->normalizePath((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 35: { QVariantMap _r = _t->getFileInfo((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QVariantMap*>(_a[0]) = std::move(_r); }  break;
        case 36: _t->setThreadCount((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 37: _t->setConversionThreads((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 38: _t->setThumbnailThreads((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ImageConversionManager::*)()>(_a, &ImageConversionManager::isRunningChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ImageConversionManager::*)()>(_a, &ImageConversionManager::isPausedChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ImageConversionManager::*)()>(_a, &ImageConversionManager::isScanningChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (ImageConversionManager::*)()>(_a, &ImageConversionManager::maxConcurrentTasksChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (ImageConversionManager::*)()>(_a, &ImageConversionManager::conversionFinished, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (ImageConversionManager::*)(int , const QString & )>(_a, &ImageConversionManager::fileConverted, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (ImageConversionManager::*)(int )>(_a, &ImageConversionManager::fileFailed, 6))
            return;
        if (QtMocHelpers::indexOfMethod<void (ImageConversionManager::*)(int , const QString & , qint64 , qint64 )>(_a, &ImageConversionManager::fileConvertedWithSize, 7))
            return;
        if (QtMocHelpers::indexOfMethod<void (ImageConversionManager::*)(int , const QString & )>(_a, &ImageConversionManager::fileSizeIncreasedAfterConversion, 8))
            return;
        if (QtMocHelpers::indexOfMethod<void (ImageConversionManager::*)(int , const QString & , qint64 , qint64 )>(_a, &ImageConversionManager::fileSizeIncreasedWithSize, 9))
            return;
        if (QtMocHelpers::indexOfMethod<void (ImageConversionManager::*)(int , const QString & , const QString & )>(_a, &ImageConversionManager::fileCorrupted, 10))
            return;
        if (QtMocHelpers::indexOfMethod<void (ImageConversionManager::*)()>(_a, &ImageConversionManager::taskListCleared, 11))
            return;
        if (QtMocHelpers::indexOfMethod<void (ImageConversionManager::*)(const QString & )>(_a, &ImageConversionManager::scanningStarted, 12))
            return;
        if (QtMocHelpers::indexOfMethod<void (ImageConversionManager::*)(int )>(_a, &ImageConversionManager::scanningProgress, 13))
            return;
        if (QtMocHelpers::indexOfMethod<void (ImageConversionManager::*)(const QVariantList & )>(_a, &ImageConversionManager::scanningFinished, 14))
            return;
        if (QtMocHelpers::indexOfMethod<void (ImageConversionManager::*)()>(_a, &ImageConversionManager::deleteFilesStarted, 15))
            return;
        if (QtMocHelpers::indexOfMethod<void (ImageConversionManager::*)(const QVariantMap & )>(_a, &ImageConversionManager::deleteFilesCompleted, 16))
            return;
        if (QtMocHelpers::indexOfMethod<void (ImageConversionManager::*)()>(_a, &ImageConversionManager::scanningCancelled, 17))
            return;
        if (QtMocHelpers::indexOfMethod<void (ImageConversionManager::*)(const QString & )>(_a, &ImageConversionManager::scanningError, 18))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<bool*>(_v) = _t->isRunning(); break;
        case 1: *reinterpret_cast<bool*>(_v) = _t->isPaused(); break;
        case 2: *reinterpret_cast<int*>(_v) = _t->maxConcurrentTasks(); break;
        case 3: *reinterpret_cast<bool*>(_v) = _t->isScanning(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 2: _t->setMaxConcurrentTasks(*reinterpret_cast<int*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *ImageConversionManager::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ImageConversionManager::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN22ImageConversionManagerE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int ImageConversionManager::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 39)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 39;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 39)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 39;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    }
    return _id;
}

// SIGNAL 0
void ImageConversionManager::isRunningChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ImageConversionManager::isPausedChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ImageConversionManager::isScanningChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ImageConversionManager::maxConcurrentTasksChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void ImageConversionManager::conversionFinished()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void ImageConversionManager::fileConverted(int _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 5, nullptr, _t1, _t2);
}

// SIGNAL 6
void ImageConversionManager::fileFailed(int _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 6, nullptr, _t1);
}

// SIGNAL 7
void ImageConversionManager::fileConvertedWithSize(int _t1, const QString & _t2, qint64 _t3, qint64 _t4)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 7, nullptr, _t1, _t2, _t3, _t4);
}

// SIGNAL 8
void ImageConversionManager::fileSizeIncreasedAfterConversion(int _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 8, nullptr, _t1, _t2);
}

// SIGNAL 9
void ImageConversionManager::fileSizeIncreasedWithSize(int _t1, const QString & _t2, qint64 _t3, qint64 _t4)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 9, nullptr, _t1, _t2, _t3, _t4);
}

// SIGNAL 10
void ImageConversionManager::fileCorrupted(int _t1, const QString & _t2, const QString & _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 10, nullptr, _t1, _t2, _t3);
}

// SIGNAL 11
void ImageConversionManager::taskListCleared()
{
    QMetaObject::activate(this, &staticMetaObject, 11, nullptr);
}

// SIGNAL 12
void ImageConversionManager::scanningStarted(const QString & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 12, nullptr, _t1);
}

// SIGNAL 13
void ImageConversionManager::scanningProgress(int _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 13, nullptr, _t1);
}

// SIGNAL 14
void ImageConversionManager::scanningFinished(const QVariantList & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 14, nullptr, _t1);
}

// SIGNAL 15
void ImageConversionManager::deleteFilesStarted()
{
    QMetaObject::activate(this, &staticMetaObject, 15, nullptr);
}

// SIGNAL 16
void ImageConversionManager::deleteFilesCompleted(const QVariantMap & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 16, nullptr, _t1);
}

// SIGNAL 17
void ImageConversionManager::scanningCancelled()
{
    QMetaObject::activate(this, &staticMetaObject, 17, nullptr);
}

// SIGNAL 18
void ImageConversionManager::scanningError(const QString & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 18, nullptr, _t1);
}
QT_WARNING_POP
