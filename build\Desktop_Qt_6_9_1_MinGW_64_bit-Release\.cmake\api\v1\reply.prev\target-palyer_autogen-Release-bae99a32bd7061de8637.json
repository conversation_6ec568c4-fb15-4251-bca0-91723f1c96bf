{"backtrace": 0, "backtraceGraph": {"commands": [], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}]}, "dependencies": [{"id": "palyer_autogen_timestamp_deps::@6890427a1f51a3e7e1df"}], "id": "palyer_autogen::@6890427a1f51a3e7e1df", "isGeneratorProvided": true, "name": "palyer_autogen", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/CMakeFiles/palyer_autogen", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/CMakeFiles/palyer_autogen.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/palyer_autogen/timestamp.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}