﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{A6568A2D-8064-30F8-99E7-868F9B18416B}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>backend</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Qt\file\palyer\build\backend\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">backend.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">backend</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Qt\file\palyer\build\backend\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">backend.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">backend</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Qt\file\palyer\build\backend\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">backend.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">backend</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Qt\file\palyer\build\backend\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">backend.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">backend</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <PropertyGroup />
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Qt\file\palyer\build\backend\backend_autogen\include_Debug;C:\Qt\file\palyer\backend;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Qt/6.9.1/mingw_64/include/QtCore" /external:I "C:/Qt/6.9.1/mingw_64/include" /external:I "C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++" /external:I "C:/Qt/6.9.1/mingw_64/include/QtConcurrent" /external:I "C:/Qt/6.9.1/mingw_64/include/QtGui" /external:I "C:/Qt/6.9.1/mingw_64/include/QtQuick" /external:I "C:/Qt/6.9.1/mingw_64/include/QtQml" /external:I "C:/Qt/6.9.1/mingw_64/include/QtQmlIntegration" /external:I "C:/Qt/6.9.1/mingw_64/include/QtNetwork" /external:I "C:/Qt/6.9.1/mingw_64/include/QtQmlMeta" /external:I "C:/Qt/6.9.1/mingw_64/include/QtQmlModels" /external:I "C:/Qt/6.9.1/mingw_64/include/QtQmlWorkerScript" /external:I "C:/Qt/6.9.1/mingw_64/include/QtOpenGL" /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <ConformanceMode>true</ConformanceMode>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;BACKEND_LIBRARY;QT_DEPRECATED_WARNINGS;_CRT_SECURE_NO_WARNINGS;QT_CORE_LIB;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_CONCURRENT_LIB;QT_GUI_LIB;QT_QUICK_LIB;QT_QML_LIB;QT_QMLINTEGRATION_LIB;QT_NETWORK_LIB;QT_QMLMETA_LIB;QT_QMLMODELS_LIB;QT_QMLWORKERSCRIPT_LIB;QT_OPENGL_LIB;CMAKE_INTDIR="Debug";backend_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;BACKEND_LIBRARY;QT_DEPRECATED_WARNINGS;_CRT_SECURE_NO_WARNINGS;QT_CORE_LIB;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_CONCURRENT_LIB;QT_GUI_LIB;QT_QUICK_LIB;QT_QML_LIB;QT_QMLINTEGRATION_LIB;QT_NETWORK_LIB;QT_QMLMETA_LIB;QT_QMLMODELS_LIB;QT_QMLWORKERSCRIPT_LIB;QT_OPENGL_LIB;CMAKE_INTDIR=\"Debug\";backend_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Qt\file\palyer\build\backend\backend_autogen\include_Debug;C:\Qt\file\palyer\backend;C:\Qt\6.9.1\mingw_64\include\QtCore;C:\Qt\6.9.1\mingw_64\include;C:\Qt\6.9.1\mingw_64\mkspecs\win32-g++;C:\Qt\6.9.1\mingw_64\include\QtConcurrent;C:\Qt\6.9.1\mingw_64\include\QtGui;C:\Qt\6.9.1\mingw_64\include\QtQuick;C:\Qt\6.9.1\mingw_64\include\QtQml;C:\Qt\6.9.1\mingw_64\include\QtQmlIntegration;C:\Qt\6.9.1\mingw_64\include\QtNetwork;C:\Qt\6.9.1\mingw_64\include\QtQmlMeta;C:\Qt\6.9.1\mingw_64\include\QtQmlModels;C:\Qt\6.9.1\mingw_64\include\QtQmlWorkerScript;C:\Qt\6.9.1\mingw_64\include\QtOpenGL;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Qt\file\palyer\build\backend\backend_autogen\include_Debug;C:\Qt\file\palyer\backend;C:\Qt\6.9.1\mingw_64\include\QtCore;C:\Qt\6.9.1\mingw_64\include;C:\Qt\6.9.1\mingw_64\mkspecs\win32-g++;C:\Qt\6.9.1\mingw_64\include\QtConcurrent;C:\Qt\6.9.1\mingw_64\include\QtGui;C:\Qt\6.9.1\mingw_64\include\QtQuick;C:\Qt\6.9.1\mingw_64\include\QtQml;C:\Qt\6.9.1\mingw_64\include\QtQmlIntegration;C:\Qt\6.9.1\mingw_64\include\QtNetwork;C:\Qt\6.9.1\mingw_64\include\QtQmlMeta;C:\Qt\6.9.1\mingw_64\include\QtQmlModels;C:\Qt\6.9.1\mingw_64\include\QtQmlWorkerScript;C:\Qt\6.9.1\mingw_64\include\QtOpenGL;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC for target backend</Message>
      <Command>setlocal
cd C:\Qt\file\palyer\build\backend
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
C:\Qt\Tools\CMake_64\bin\cmake.exe -E cmake_autogen C:/Qt/file/palyer/build/backend/CMakeFiles/backend_autogen.dir/AutogenInfo.json Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <PostBuildEvent>
      <Message>Copying external tools to build directory</Message>
      <Command>setlocal
C:\Qt\Tools\CMake_64\bin\cmake.exe -E copy_if_different C:/Qt/file/palyer/lib/libwebp-1.5.0-windows-x64/bin/dwebp.exe C:/Qt/file/palyer/build/Debug
if %errorlevel% neq 0 goto :cmEnd
C:\Qt\Tools\CMake_64\bin\cmake.exe -E copy_if_different C:/Qt/file/palyer/lib/libwebp-1.5.0-windows-x64/bin/cwebp.exe C:/Qt/file/palyer/build/Debug
if %errorlevel% neq 0 goto :cmEnd
C:\Qt\Tools\CMake_64\bin\cmake.exe -E copy_if_different C:/Qt/file/palyer/lib/ffmpeg2/ffmpeg.exe C:/Qt/file/palyer/build/Debug
if %errorlevel% neq 0 goto :cmEnd
C:\Qt\Tools\CMake_64\bin\cmake.exe -E copy_if_different C:/Qt/file/palyer/lib/windows-artifacts/avifdec.exe C:/Qt/file/palyer/build/Debug
if %errorlevel% neq 0 goto :cmEnd
C:\Qt\Tools\CMake_64\bin\cmake.exe -E copy_if_different C:/Qt/file/palyer/build/backend/Debug/backend.dll C:/Qt/file/palyer/build/Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>C:\Qt\6.9.1\mingw_64\lib\libQt6Concurrent.a;C:\Qt\6.9.1\mingw_64\lib\libQt6Quick.a;C:\Qt\6.9.1\mingw_64\lib\libQt6QmlMeta.a;C:\Qt\6.9.1\mingw_64\lib\libQt6QmlWorkerScript.a;C:\Qt\6.9.1\mingw_64\lib\libQt6QmlModels.a;C:\Qt\6.9.1\mingw_64\lib\libQt6Qml.a;C:\Qt\6.9.1\mingw_64\lib\libQt6Network.a;ws2_32.lib;shell32.lib;C:\Qt\6.9.1\mingw_64\lib\libQt6OpenGL.a;C:\Qt\6.9.1\mingw_64\lib\libQt6Gui.a;C:\Qt\6.9.1\mingw_64\lib\libQt6Core.a;mpr.lib;userenv.lib;-latomic;d3d11.lib;dxgi.lib;dxguid.lib;d3d12.lib;user32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Qt/file/palyer/build/backend/Debug/backend.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Qt/file/palyer/build/backend/Debug/backend.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Qt\file\palyer\build\backend\backend_autogen\include_Release;C:\Qt\file\palyer\backend;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Qt/6.9.1/mingw_64/include/QtCore" /external:I "C:/Qt/6.9.1/mingw_64/include" /external:I "C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++" /external:I "C:/Qt/6.9.1/mingw_64/include/QtConcurrent" /external:I "C:/Qt/6.9.1/mingw_64/include/QtGui" /external:I "C:/Qt/6.9.1/mingw_64/include/QtQuick" /external:I "C:/Qt/6.9.1/mingw_64/include/QtQml" /external:I "C:/Qt/6.9.1/mingw_64/include/QtQmlIntegration" /external:I "C:/Qt/6.9.1/mingw_64/include/QtNetwork" /external:I "C:/Qt/6.9.1/mingw_64/include/QtQmlMeta" /external:I "C:/Qt/6.9.1/mingw_64/include/QtQmlModels" /external:I "C:/Qt/6.9.1/mingw_64/include/QtQmlWorkerScript" /external:I "C:/Qt/6.9.1/mingw_64/include/QtOpenGL" /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ConformanceMode>true</ConformanceMode>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;BACKEND_LIBRARY;QT_DEPRECATED_WARNINGS;_CRT_SECURE_NO_WARNINGS;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_CONCURRENT_LIB;QT_GUI_LIB;QT_QUICK_LIB;QT_QML_LIB;QT_QMLINTEGRATION_LIB;QT_NETWORK_LIB;QT_QMLMETA_LIB;QT_QMLMODELS_LIB;QT_QMLWORKERSCRIPT_LIB;QT_OPENGL_LIB;CMAKE_INTDIR="Release";backend_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;BACKEND_LIBRARY;QT_DEPRECATED_WARNINGS;_CRT_SECURE_NO_WARNINGS;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_CONCURRENT_LIB;QT_GUI_LIB;QT_QUICK_LIB;QT_QML_LIB;QT_QMLINTEGRATION_LIB;QT_NETWORK_LIB;QT_QMLMETA_LIB;QT_QMLMODELS_LIB;QT_QMLWORKERSCRIPT_LIB;QT_OPENGL_LIB;CMAKE_INTDIR=\"Release\";backend_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Qt\file\palyer\build\backend\backend_autogen\include_Release;C:\Qt\file\palyer\backend;C:\Qt\6.9.1\mingw_64\include\QtCore;C:\Qt\6.9.1\mingw_64\include;C:\Qt\6.9.1\mingw_64\mkspecs\win32-g++;C:\Qt\6.9.1\mingw_64\include\QtConcurrent;C:\Qt\6.9.1\mingw_64\include\QtGui;C:\Qt\6.9.1\mingw_64\include\QtQuick;C:\Qt\6.9.1\mingw_64\include\QtQml;C:\Qt\6.9.1\mingw_64\include\QtQmlIntegration;C:\Qt\6.9.1\mingw_64\include\QtNetwork;C:\Qt\6.9.1\mingw_64\include\QtQmlMeta;C:\Qt\6.9.1\mingw_64\include\QtQmlModels;C:\Qt\6.9.1\mingw_64\include\QtQmlWorkerScript;C:\Qt\6.9.1\mingw_64\include\QtOpenGL;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Qt\file\palyer\build\backend\backend_autogen\include_Release;C:\Qt\file\palyer\backend;C:\Qt\6.9.1\mingw_64\include\QtCore;C:\Qt\6.9.1\mingw_64\include;C:\Qt\6.9.1\mingw_64\mkspecs\win32-g++;C:\Qt\6.9.1\mingw_64\include\QtConcurrent;C:\Qt\6.9.1\mingw_64\include\QtGui;C:\Qt\6.9.1\mingw_64\include\QtQuick;C:\Qt\6.9.1\mingw_64\include\QtQml;C:\Qt\6.9.1\mingw_64\include\QtQmlIntegration;C:\Qt\6.9.1\mingw_64\include\QtNetwork;C:\Qt\6.9.1\mingw_64\include\QtQmlMeta;C:\Qt\6.9.1\mingw_64\include\QtQmlModels;C:\Qt\6.9.1\mingw_64\include\QtQmlWorkerScript;C:\Qt\6.9.1\mingw_64\include\QtOpenGL;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC for target backend</Message>
      <Command>setlocal
cd C:\Qt\file\palyer\build\backend
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
C:\Qt\Tools\CMake_64\bin\cmake.exe -E cmake_autogen C:/Qt/file/palyer/build/backend/CMakeFiles/backend_autogen.dir/AutogenInfo.json Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <PostBuildEvent>
      <Message>Copying external tools to build directory</Message>
      <Command>setlocal
C:\Qt\Tools\CMake_64\bin\cmake.exe -E copy_if_different C:/Qt/file/palyer/lib/libwebp-1.5.0-windows-x64/bin/dwebp.exe C:/Qt/file/palyer/build/Release
if %errorlevel% neq 0 goto :cmEnd
C:\Qt\Tools\CMake_64\bin\cmake.exe -E copy_if_different C:/Qt/file/palyer/lib/libwebp-1.5.0-windows-x64/bin/cwebp.exe C:/Qt/file/palyer/build/Release
if %errorlevel% neq 0 goto :cmEnd
C:\Qt\Tools\CMake_64\bin\cmake.exe -E copy_if_different C:/Qt/file/palyer/lib/ffmpeg2/ffmpeg.exe C:/Qt/file/palyer/build/Release
if %errorlevel% neq 0 goto :cmEnd
C:\Qt\Tools\CMake_64\bin\cmake.exe -E copy_if_different C:/Qt/file/palyer/lib/windows-artifacts/avifdec.exe C:/Qt/file/palyer/build/Release
if %errorlevel% neq 0 goto :cmEnd
C:\Qt\Tools\CMake_64\bin\cmake.exe -E copy_if_different C:/Qt/file/palyer/build/backend/Release/backend.dll C:/Qt/file/palyer/build/Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>C:\Qt\6.9.1\mingw_64\lib\libQt6Concurrent.a;C:\Qt\6.9.1\mingw_64\lib\libQt6Quick.a;C:\Qt\6.9.1\mingw_64\lib\libQt6QmlMeta.a;C:\Qt\6.9.1\mingw_64\lib\libQt6QmlWorkerScript.a;C:\Qt\6.9.1\mingw_64\lib\libQt6QmlModels.a;C:\Qt\6.9.1\mingw_64\lib\libQt6Qml.a;C:\Qt\6.9.1\mingw_64\lib\libQt6Network.a;ws2_32.lib;shell32.lib;C:\Qt\6.9.1\mingw_64\lib\libQt6OpenGL.a;C:\Qt\6.9.1\mingw_64\lib\libQt6Gui.a;C:\Qt\6.9.1\mingw_64\lib\libQt6Core.a;mpr.lib;userenv.lib;-latomic;d3d11.lib;dxgi.lib;dxguid.lib;d3d12.lib;user32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Qt/file/palyer/build/backend/Release/backend.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Qt/file/palyer/build/backend/Release/backend.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Qt\file\palyer\build\backend\backend_autogen\include_MinSizeRel;C:\Qt\file\palyer\backend;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Qt/6.9.1/mingw_64/include/QtCore" /external:I "C:/Qt/6.9.1/mingw_64/include" /external:I "C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++" /external:I "C:/Qt/6.9.1/mingw_64/include/QtConcurrent" /external:I "C:/Qt/6.9.1/mingw_64/include/QtGui" /external:I "C:/Qt/6.9.1/mingw_64/include/QtQuick" /external:I "C:/Qt/6.9.1/mingw_64/include/QtQml" /external:I "C:/Qt/6.9.1/mingw_64/include/QtQmlIntegration" /external:I "C:/Qt/6.9.1/mingw_64/include/QtNetwork" /external:I "C:/Qt/6.9.1/mingw_64/include/QtQmlMeta" /external:I "C:/Qt/6.9.1/mingw_64/include/QtQmlModels" /external:I "C:/Qt/6.9.1/mingw_64/include/QtQmlWorkerScript" /external:I "C:/Qt/6.9.1/mingw_64/include/QtOpenGL" /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ConformanceMode>true</ConformanceMode>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;BACKEND_LIBRARY;QT_DEPRECATED_WARNINGS;_CRT_SECURE_NO_WARNINGS;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_CONCURRENT_LIB;QT_GUI_LIB;QT_QUICK_LIB;QT_QML_LIB;QT_QMLINTEGRATION_LIB;QT_NETWORK_LIB;QT_QMLMETA_LIB;QT_QMLMODELS_LIB;QT_QMLWORKERSCRIPT_LIB;QT_OPENGL_LIB;CMAKE_INTDIR="MinSizeRel";backend_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;BACKEND_LIBRARY;QT_DEPRECATED_WARNINGS;_CRT_SECURE_NO_WARNINGS;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_CONCURRENT_LIB;QT_GUI_LIB;QT_QUICK_LIB;QT_QML_LIB;QT_QMLINTEGRATION_LIB;QT_NETWORK_LIB;QT_QMLMETA_LIB;QT_QMLMODELS_LIB;QT_QMLWORKERSCRIPT_LIB;QT_OPENGL_LIB;CMAKE_INTDIR=\"MinSizeRel\";backend_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Qt\file\palyer\build\backend\backend_autogen\include_MinSizeRel;C:\Qt\file\palyer\backend;C:\Qt\6.9.1\mingw_64\include\QtCore;C:\Qt\6.9.1\mingw_64\include;C:\Qt\6.9.1\mingw_64\mkspecs\win32-g++;C:\Qt\6.9.1\mingw_64\include\QtConcurrent;C:\Qt\6.9.1\mingw_64\include\QtGui;C:\Qt\6.9.1\mingw_64\include\QtQuick;C:\Qt\6.9.1\mingw_64\include\QtQml;C:\Qt\6.9.1\mingw_64\include\QtQmlIntegration;C:\Qt\6.9.1\mingw_64\include\QtNetwork;C:\Qt\6.9.1\mingw_64\include\QtQmlMeta;C:\Qt\6.9.1\mingw_64\include\QtQmlModels;C:\Qt\6.9.1\mingw_64\include\QtQmlWorkerScript;C:\Qt\6.9.1\mingw_64\include\QtOpenGL;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Qt\file\palyer\build\backend\backend_autogen\include_MinSizeRel;C:\Qt\file\palyer\backend;C:\Qt\6.9.1\mingw_64\include\QtCore;C:\Qt\6.9.1\mingw_64\include;C:\Qt\6.9.1\mingw_64\mkspecs\win32-g++;C:\Qt\6.9.1\mingw_64\include\QtConcurrent;C:\Qt\6.9.1\mingw_64\include\QtGui;C:\Qt\6.9.1\mingw_64\include\QtQuick;C:\Qt\6.9.1\mingw_64\include\QtQml;C:\Qt\6.9.1\mingw_64\include\QtQmlIntegration;C:\Qt\6.9.1\mingw_64\include\QtNetwork;C:\Qt\6.9.1\mingw_64\include\QtQmlMeta;C:\Qt\6.9.1\mingw_64\include\QtQmlModels;C:\Qt\6.9.1\mingw_64\include\QtQmlWorkerScript;C:\Qt\6.9.1\mingw_64\include\QtOpenGL;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC for target backend</Message>
      <Command>setlocal
cd C:\Qt\file\palyer\build\backend
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
C:\Qt\Tools\CMake_64\bin\cmake.exe -E cmake_autogen C:/Qt/file/palyer/build/backend/CMakeFiles/backend_autogen.dir/AutogenInfo.json MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <PostBuildEvent>
      <Message>Copying external tools to build directory</Message>
      <Command>setlocal
C:\Qt\Tools\CMake_64\bin\cmake.exe -E copy_if_different C:/Qt/file/palyer/lib/libwebp-1.5.0-windows-x64/bin/dwebp.exe C:/Qt/file/palyer/build/MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
C:\Qt\Tools\CMake_64\bin\cmake.exe -E copy_if_different C:/Qt/file/palyer/lib/libwebp-1.5.0-windows-x64/bin/cwebp.exe C:/Qt/file/palyer/build/MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
C:\Qt\Tools\CMake_64\bin\cmake.exe -E copy_if_different C:/Qt/file/palyer/lib/ffmpeg2/ffmpeg.exe C:/Qt/file/palyer/build/MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
C:\Qt\Tools\CMake_64\bin\cmake.exe -E copy_if_different C:/Qt/file/palyer/lib/windows-artifacts/avifdec.exe C:/Qt/file/palyer/build/MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
C:\Qt\Tools\CMake_64\bin\cmake.exe -E copy_if_different C:/Qt/file/palyer/build/backend/MinSizeRel/backend.dll C:/Qt/file/palyer/build/MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>C:\Qt\6.9.1\mingw_64\lib\libQt6Concurrent.a;C:\Qt\6.9.1\mingw_64\lib\libQt6Quick.a;C:\Qt\6.9.1\mingw_64\lib\libQt6QmlMeta.a;C:\Qt\6.9.1\mingw_64\lib\libQt6QmlWorkerScript.a;C:\Qt\6.9.1\mingw_64\lib\libQt6QmlModels.a;C:\Qt\6.9.1\mingw_64\lib\libQt6Qml.a;C:\Qt\6.9.1\mingw_64\lib\libQt6Network.a;ws2_32.lib;shell32.lib;C:\Qt\6.9.1\mingw_64\lib\libQt6OpenGL.a;C:\Qt\6.9.1\mingw_64\lib\libQt6Gui.a;C:\Qt\6.9.1\mingw_64\lib\libQt6Core.a;mpr.lib;userenv.lib;-latomic;d3d11.lib;dxgi.lib;dxguid.lib;d3d12.lib;user32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Qt/file/palyer/build/backend/MinSizeRel/backend.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Qt/file/palyer/build/backend/MinSizeRel/backend.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Qt\file\palyer\build\backend\backend_autogen\include_RelWithDebInfo;C:\Qt\file\palyer\backend;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Qt/6.9.1/mingw_64/include/QtCore" /external:I "C:/Qt/6.9.1/mingw_64/include" /external:I "C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++" /external:I "C:/Qt/6.9.1/mingw_64/include/QtConcurrent" /external:I "C:/Qt/6.9.1/mingw_64/include/QtGui" /external:I "C:/Qt/6.9.1/mingw_64/include/QtQuick" /external:I "C:/Qt/6.9.1/mingw_64/include/QtQml" /external:I "C:/Qt/6.9.1/mingw_64/include/QtQmlIntegration" /external:I "C:/Qt/6.9.1/mingw_64/include/QtNetwork" /external:I "C:/Qt/6.9.1/mingw_64/include/QtQmlMeta" /external:I "C:/Qt/6.9.1/mingw_64/include/QtQmlModels" /external:I "C:/Qt/6.9.1/mingw_64/include/QtQmlWorkerScript" /external:I "C:/Qt/6.9.1/mingw_64/include/QtOpenGL" /Zc:__cplusplus</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ConformanceMode>true</ConformanceMode>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;BACKEND_LIBRARY;QT_DEPRECATED_WARNINGS;_CRT_SECURE_NO_WARNINGS;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_CONCURRENT_LIB;QT_GUI_LIB;QT_QUICK_LIB;QT_QML_LIB;QT_QMLINTEGRATION_LIB;QT_NETWORK_LIB;QT_QMLMETA_LIB;QT_QMLMODELS_LIB;QT_QMLWORKERSCRIPT_LIB;QT_OPENGL_LIB;CMAKE_INTDIR="RelWithDebInfo";backend_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;BACKEND_LIBRARY;QT_DEPRECATED_WARNINGS;_CRT_SECURE_NO_WARNINGS;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;MINGW_HAS_SECURE_API=1;UNICODE;_UNICODE;QT_CONCURRENT_LIB;QT_GUI_LIB;QT_QUICK_LIB;QT_QML_LIB;QT_QMLINTEGRATION_LIB;QT_NETWORK_LIB;QT_QMLMETA_LIB;QT_QMLMODELS_LIB;QT_QMLWORKERSCRIPT_LIB;QT_OPENGL_LIB;CMAKE_INTDIR=\"RelWithDebInfo\";backend_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Qt\file\palyer\build\backend\backend_autogen\include_RelWithDebInfo;C:\Qt\file\palyer\backend;C:\Qt\6.9.1\mingw_64\include\QtCore;C:\Qt\6.9.1\mingw_64\include;C:\Qt\6.9.1\mingw_64\mkspecs\win32-g++;C:\Qt\6.9.1\mingw_64\include\QtConcurrent;C:\Qt\6.9.1\mingw_64\include\QtGui;C:\Qt\6.9.1\mingw_64\include\QtQuick;C:\Qt\6.9.1\mingw_64\include\QtQml;C:\Qt\6.9.1\mingw_64\include\QtQmlIntegration;C:\Qt\6.9.1\mingw_64\include\QtNetwork;C:\Qt\6.9.1\mingw_64\include\QtQmlMeta;C:\Qt\6.9.1\mingw_64\include\QtQmlModels;C:\Qt\6.9.1\mingw_64\include\QtQmlWorkerScript;C:\Qt\6.9.1\mingw_64\include\QtOpenGL;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Qt\file\palyer\build\backend\backend_autogen\include_RelWithDebInfo;C:\Qt\file\palyer\backend;C:\Qt\6.9.1\mingw_64\include\QtCore;C:\Qt\6.9.1\mingw_64\include;C:\Qt\6.9.1\mingw_64\mkspecs\win32-g++;C:\Qt\6.9.1\mingw_64\include\QtConcurrent;C:\Qt\6.9.1\mingw_64\include\QtGui;C:\Qt\6.9.1\mingw_64\include\QtQuick;C:\Qt\6.9.1\mingw_64\include\QtQml;C:\Qt\6.9.1\mingw_64\include\QtQmlIntegration;C:\Qt\6.9.1\mingw_64\include\QtNetwork;C:\Qt\6.9.1\mingw_64\include\QtQmlMeta;C:\Qt\6.9.1\mingw_64\include\QtQmlModels;C:\Qt\6.9.1\mingw_64\include\QtQmlWorkerScript;C:\Qt\6.9.1\mingw_64\include\QtOpenGL;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC for target backend</Message>
      <Command>setlocal
cd C:\Qt\file\palyer\build\backend
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
C:\Qt\Tools\CMake_64\bin\cmake.exe -E cmake_autogen C:/Qt/file/palyer/build/backend/CMakeFiles/backend_autogen.dir/AutogenInfo.json RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <PostBuildEvent>
      <Message>Copying external tools to build directory</Message>
      <Command>setlocal
C:\Qt\Tools\CMake_64\bin\cmake.exe -E copy_if_different C:/Qt/file/palyer/lib/libwebp-1.5.0-windows-x64/bin/dwebp.exe C:/Qt/file/palyer/build/RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
C:\Qt\Tools\CMake_64\bin\cmake.exe -E copy_if_different C:/Qt/file/palyer/lib/libwebp-1.5.0-windows-x64/bin/cwebp.exe C:/Qt/file/palyer/build/RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
C:\Qt\Tools\CMake_64\bin\cmake.exe -E copy_if_different C:/Qt/file/palyer/lib/ffmpeg2/ffmpeg.exe C:/Qt/file/palyer/build/RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
C:\Qt\Tools\CMake_64\bin\cmake.exe -E copy_if_different C:/Qt/file/palyer/lib/windows-artifacts/avifdec.exe C:/Qt/file/palyer/build/RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
C:\Qt\Tools\CMake_64\bin\cmake.exe -E copy_if_different C:/Qt/file/palyer/build/backend/RelWithDebInfo/backend.dll C:/Qt/file/palyer/build/RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>C:\Qt\6.9.1\mingw_64\lib\libQt6Concurrent.a;C:\Qt\6.9.1\mingw_64\lib\libQt6Quick.a;C:\Qt\6.9.1\mingw_64\lib\libQt6QmlMeta.a;C:\Qt\6.9.1\mingw_64\lib\libQt6QmlWorkerScript.a;C:\Qt\6.9.1\mingw_64\lib\libQt6QmlModels.a;C:\Qt\6.9.1\mingw_64\lib\libQt6Qml.a;C:\Qt\6.9.1\mingw_64\lib\libQt6Network.a;ws2_32.lib;shell32.lib;C:\Qt\6.9.1\mingw_64\lib\libQt6OpenGL.a;C:\Qt\6.9.1\mingw_64\lib\libQt6Gui.a;C:\Qt\6.9.1\mingw_64\lib\libQt6Core.a;mpr.lib;userenv.lib;-latomic;d3d11.lib;dxgi.lib;dxguid.lib;d3d12.lib;user32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Qt/file/palyer/build/backend/RelWithDebInfo/backend.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Qt/file/palyer/build/backend/RelWithDebInfo/backend.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Qt\file\palyer\backend\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Qt/file/palyer/backend/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
C:\Qt\Tools\CMake_64\bin\cmake.exe -SC:/Qt/file/palyer -BC:/Qt/file/palyer/build --check-stamp-file C:/Qt/file/palyer/build/backend/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\Qt6Config.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\Qt6Dependencies.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\Qt6Targets.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtFeature.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtFeatureCommon.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtInstallPaths.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicAndroidHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicGitHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicPluginHelpers_v2.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomAttributionHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomCpeHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomDepHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomFileHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomLicenseHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomOpsHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomPurlHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomPythonHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomQtEntityHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomSystemDepHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicWindowsHelpers.cmake;C:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake;C:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CheckCXXCompilerFlag.cmake;C:\Qt\file\palyer\build\CMakeFiles\cmake.verify_globs;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Qt\file\palyer\build\backend\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Qt/file/palyer/backend/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
C:\Qt\Tools\CMake_64\bin\cmake.exe -SC:/Qt/file/palyer -BC:/Qt/file/palyer/build --check-stamp-file C:/Qt/file/palyer/build/backend/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\Qt6Config.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\Qt6Dependencies.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\Qt6Targets.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtFeature.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtFeatureCommon.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtInstallPaths.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicAndroidHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicGitHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicPluginHelpers_v2.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomAttributionHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomCpeHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomDepHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomFileHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomLicenseHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomOpsHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomPurlHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomPythonHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomQtEntityHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomSystemDepHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicWindowsHelpers.cmake;C:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake;C:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CheckCXXCompilerFlag.cmake;C:\Qt\file\palyer\build\CMakeFiles\cmake.verify_globs;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Qt\file\palyer\build\backend\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule C:/Qt/file/palyer/backend/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
C:\Qt\Tools\CMake_64\bin\cmake.exe -SC:/Qt/file/palyer -BC:/Qt/file/palyer/build --check-stamp-file C:/Qt/file/palyer/build/backend/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\Qt6Config.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\Qt6Dependencies.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\Qt6Targets.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtFeature.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtFeatureCommon.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtInstallPaths.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicAndroidHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicGitHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicPluginHelpers_v2.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomAttributionHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomCpeHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomDepHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomFileHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomLicenseHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomOpsHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomPurlHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomPythonHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomQtEntityHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomSystemDepHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicWindowsHelpers.cmake;C:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake;C:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CheckCXXCompilerFlag.cmake;C:\Qt\file\palyer\build\CMakeFiles\cmake.verify_globs;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Qt\file\palyer\build\backend\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule C:/Qt/file/palyer/backend/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
C:\Qt\Tools\CMake_64\bin\cmake.exe -SC:/Qt/file/palyer -BC:/Qt/file/palyer/build --check-stamp-file C:/Qt/file/palyer/build/backend/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\Qt6Config.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\Qt6Dependencies.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\Qt6Targets.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtFeature.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtFeatureCommon.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtInstallPaths.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicAndroidHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicGitHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicPluginHelpers_v2.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomAttributionHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomCpeHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomDepHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomFileHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomLicenseHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomOpsHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomPurlHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomPythonHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomQtEntityHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicSbomSystemDepHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;C:\Qt\6.9.1\mingw_64\lib\cmake\Qt6\QtPublicWindowsHelpers.cmake;C:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake;C:\Qt\Tools\CMake_64\share\cmake-3.30\Modules\CheckCXXCompilerFlag.cmake;C:\Qt\file\palyer\build\CMakeFiles\cmake.verify_globs;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Qt\file\palyer\build\backend\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="C:\Qt\file\palyer\build\backend\backend_autogen\mocs_compilation_Debug.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClInclude Include="C:\Qt\file\palyer\backend\ConversionTaskRunnable.h" />
    <ClInclude Include="C:\Qt\file\palyer\backend\ImageConversionManager.h" />
    <ClInclude Include="C:\Qt\file\palyer\backend\ImageProcessor.h" />
    <ClInclude Include="C:\Qt\file\palyer\backend\NetworkBackend.h" />
    <ClInclude Include="C:\Qt\file\palyer\backend\server\ClientMode.h" />
    <ClInclude Include="C:\Qt\file\palyer\backend\server\ServerMode.h" />
    <ClCompile Include="C:\Qt\file\palyer\backend\ConversionTaskRunnable.cpp" />
    <ClCompile Include="C:\Qt\file\palyer\backend\ImageConversionManager.cpp" />
    <ClCompile Include="C:\Qt\file\palyer\backend\ImageProcessor.cpp" />
    <ClCompile Include="C:\Qt\file\palyer\backend\NetworkBackend.cpp" />
    <ClCompile Include="C:\Qt\file\palyer\backend\server\ClientMode.cpp" />
    <ClCompile Include="C:\Qt\file\palyer\backend\server\ServerMode.cpp" />
    <ClCompile Include="C:\Qt\file\palyer\build\backend\backend_autogen\mocs_compilation_Release.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="C:\Qt\file\palyer\build\backend\backend_autogen\mocs_compilation_MinSizeRel.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="C:\Qt\file\palyer\build\backend\backend_autogen\mocs_compilation_RelWithDebInfo.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="C:\Qt\file\palyer\build\ZERO_CHECK.vcxproj">
      <Project>{D931D31E-903B-3F64-B5EF-B0B066953FE1}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>