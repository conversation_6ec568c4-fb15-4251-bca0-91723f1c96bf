﻿#ifndef IMAGECONVERSIONMANAGER_H
#define IMAGECONVERSIONMANAGER_H

#include <QObject>
#include <QVariant>
#include <QList>
#include <QThreadPool>
#include <QMutex>
#include <QQueue>
#include <QWaitCondition>
#include <QSet>
#include <QFutureWatcher>

// Define ConversionTask struct
struct ConversionTask {
    int index;
    QString sourcePath;
    QString destPath;
    QString targetFormat;
    int quality;
    int retryCount = 0; // 添加重试计数
};
Q_DECLARE_METATYPE(ConversionTask)

// 文件扫描结果结构体
struct FileScanResult {
    QVariantList fileList;
    QString folderPath;
    bool includeSubfolders;
    bool success;
    QString errorMessage;
};
Q_DECLARE_METATYPE(FileScanResult)

class ImageConversionManager : public QObject
{
    Q_OBJECT
    // --- Properties for QML Binding ---
    Q_PROPERTY(bool isRunning READ isRunning NOTIFY isRunningChanged)
    Q_PROPERTY(bool isPaused READ isPaused NOTIFY isPausedChanged)
    Q_PROPERTY(int maxConcurrentTasks READ maxConcurrentTasks WRITE setMaxConcurrentTasks NOTIFY maxConcurrentTasksChanged)
    Q_PROPERTY(bool isScanning READ isScanning NOTIFY isScanningChanged)

public:
    explicit ImageConversionManager(QObject *parent = nullptr);
    ~ImageConversionManager();

    // --- Invokable Methods for QML ---
    // 异步扫描可转换文件，结果通过信号返回
    Q_INVOKABLE void scanConvertibleFiles(const QString &folderPath, bool includeSubfolders, const QString &targetFormat = "");
    // 保留原有同步方法以保持兼容性
    Q_INVOKABLE QVariantList getConvertibleFiles(const QString &folderPath, bool includeSubfolders);
    Q_INVOKABLE void startConversion(const QVariant &fileListModel, const QString &targetFormat, int quality, const QString &outputDir);
    Q_INVOKABLE void pauseConversion();
    Q_INVOKABLE void resumeConversion();
    Q_INVOKABLE void cancelConversion();
    Q_INVOKABLE QVariantMap deleteFiles(const QString &folderPath, const QVariantList &filePatterns, bool includeSubfolders);
    Q_INVOKABLE void deleteFilesAsync(const QString &folderPath, const QVariantList &filePatterns, bool includeSubfolders);
    Q_INVOKABLE bool clearTaskList();
    Q_INVOKABLE void cancelScanning(); // 取消正在进行的扫描
    
    // 通用路径处理函数，可以从QML或任何地方调用
    Q_INVOKABLE QString normalizePath(const QString &path) const;
    
    // 获取文件信息
    Q_INVOKABLE QVariantMap getFileInfo(const QString &filePath) const;

    // 智能压缩扫描：过滤avif/webp，根据硬件限制选择格式
    Q_INVOKABLE void scanSmartCompress(const QString &folderPath, bool includeSubfolders = true);

    // AV1视频转换扫描：只扫描视频文件
    Q_INVOKABLE void scanAV1Videos(const QString &folderPath, bool includeSubfolders = true);

    // --- Property Getters/Setters ---
    bool isRunning() const;
    bool isPaused() const;
    bool isScanning() const; // 是否正在扫描文件
    int maxConcurrentTasks() const;
    void setMaxConcurrentTasks(int count);
    Q_INVOKABLE void setThreadCount(int n);
    Q_INVOKABLE void setConversionThreads(int n);
    Q_INVOKABLE void setThumbnailThreads(int n);

    // 性能优化的数据处理函数
    Q_INVOKABLE QString formatBytes(qint64 bytes);
    Q_INVOKABLE qreal extractSizeValue(const QString& sizeString);

signals:
    // --- Signals for QML ---
    void isRunningChanged();
    void isPausedChanged();
    void isScanningChanged();
    void maxConcurrentTasksChanged();
    void conversionFinished();
    void fileConverted(int index, const QString &newPath);
    void fileFailed(int index);
    void fileConvertedWithSize(int index, const QString &newPath, qint64 originalSize, qint64 newSize);
    void fileSizeIncreasedAfterConversion(int index, const QString &path);
    void fileSizeIncreasedWithSize(int index, const QString &path, qint64 originalSize, qint64 newSize);
    void fileCorrupted(int index, const QString &path, const QString &reason);
    void taskListCleared();
    void scanningStarted(const QString &folderPath); // 开始扫描
    void scanningProgress(int fileCount); // 扫描进度更新
    void scanningFinished(const QVariantList &fileList); // 扫描完成
    void deleteFilesStarted();
    void deleteFilesCompleted(const QVariantMap &result);
    void scanningCancelled(); // 扫描被取消
    void scanningError(const QString &errorMessage); // 扫描出错

private slots:
    // --- Internal Slots ---
    void handleTaskFinished();
    void onFileSizeIncreasedWithSize(int index, const QString &path, qint64 originalSize, qint64 newSize);
    void onScanningFinished();
    void processFailedFilesQueue(); // 处理失败文件队列的方法

private:
    void processQueue();
    void retryConversion(const ConversionTask &task); // 重试转换
    void updateThreadPoolSize(); // 动态调整线程池大小
    
    // 在工作线程中执行文件扫描
    static FileScanResult scanFilesWorker(const QString &folderPath, bool includeSubfolders, ImageConversionManager* manager = nullptr, const QString &targetFormat = "");
    // 清理指定目录下的txt/url文件（递归处理子目录）
    int cleanupTxtUrlFiles(const QString &folderPath, bool includeSubfolders);

    QThreadPool m_threadPool;
    QThreadPool m_conversionPool;
    QThreadPool m_thumbnailPool;
    QQueue<ConversionTask> m_taskQueue;
    QMutex m_mutex;
    QWaitCondition m_pauseCondition;
    QSet<QString> m_sizeIncreasedFiles; // 存储文件大小增加的源文件路径

    // 动态线程管理
    int m_defaultThreadCount = 4;
    int m_videoThreadCount = 1;
    QAtomicInt m_activeVideoTasks = 0;
    
    // 文件扫描相关
    QFutureWatcher<FileScanResult>* m_scanWatcher = nullptr;
    volatile bool m_isScanning = false;
    volatile bool m_isScanCancelled = false;
    QString m_currentTargetFormat; // 当前目标格式，用于扫描时确定文件类型

    volatile bool m_isPaused;
    QAtomicInt m_isCancelled;
    int m_activeTasks;
    int m_maxRetryCount = 3; // 最大重试次数

    // 失败文件队列
    QMutex m_failedFilesMutex;
    QQueue<ConversionTask> m_failedFilesQueue;
    bool m_isProcessingFailedFiles;
};

#endif // IMAGECONVERSIONMANAGER_H
