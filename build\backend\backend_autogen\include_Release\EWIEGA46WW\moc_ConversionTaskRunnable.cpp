/****************************************************************************
** Meta object code from reading C++ file 'ConversionTaskRunnable.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.1)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../backend/ConversionTaskRunnable.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ConversionTaskRunnable.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.1. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN22ConversionTaskRunnableE_t {};
} // unnamed namespace

template <> constexpr inline auto ConversionTaskRunnable::qt_create_metaobjectdata<qt_meta_tag_ZN22ConversionTaskRunnableE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ConversionTaskRunnable",
        "taskCompleted",
        "",
        "index",
        "newPath",
        "taskCompletedWithSize",
        "originalSize",
        "newSize",
        "taskFailed",
        "taskFinished",
        "fileSizeIncreasedAfterConversion",
        "path",
        "fileSizeIncreasedWithSize",
        "fileCorrupted",
        "reason",
        "onProcessFinished",
        "exitCode",
        "QProcess::ExitStatus",
        "exitStatus",
        "onReadyReadStandardError",
        "onReadyReadStandardOutput"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'taskCompleted'
        QtMocHelpers::SignalData<void(int, const QString &)>(1, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 3 }, { QMetaType::QString, 4 },
        }}),
        // Signal 'taskCompletedWithSize'
        QtMocHelpers::SignalData<void(int, const QString &, qint64, qint64)>(5, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 3 }, { QMetaType::QString, 4 }, { QMetaType::LongLong, 6 }, { QMetaType::LongLong, 7 },
        }}),
        // Signal 'taskFailed'
        QtMocHelpers::SignalData<void(int)>(8, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 3 },
        }}),
        // Signal 'taskFinished'
        QtMocHelpers::SignalData<void()>(9, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'fileSizeIncreasedAfterConversion'
        QtMocHelpers::SignalData<void(int, const QString &)>(10, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 3 }, { QMetaType::QString, 11 },
        }}),
        // Signal 'fileSizeIncreasedWithSize'
        QtMocHelpers::SignalData<void(int, const QString &, qint64, qint64)>(12, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 3 }, { QMetaType::QString, 11 }, { QMetaType::LongLong, 6 }, { QMetaType::LongLong, 7 },
        }}),
        // Signal 'fileCorrupted'
        QtMocHelpers::SignalData<void(int, const QString &, const QString &)>(13, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 3 }, { QMetaType::QString, 11 }, { QMetaType::QString, 14 },
        }}),
        // Slot 'onProcessFinished'
        QtMocHelpers::SlotData<void(int, QProcess::ExitStatus)>(15, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Int, 16 }, { 0x80000000 | 17, 18 },
        }}),
        // Slot 'onReadyReadStandardError'
        QtMocHelpers::SlotData<void()>(19, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onReadyReadStandardOutput'
        QtMocHelpers::SlotData<void()>(20, 2, QMC::AccessPrivate, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ConversionTaskRunnable, qt_meta_tag_ZN22ConversionTaskRunnableE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject ConversionTaskRunnable::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN22ConversionTaskRunnableE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN22ConversionTaskRunnableE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN22ConversionTaskRunnableE_t>.metaTypes,
    nullptr
} };

void ConversionTaskRunnable::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ConversionTaskRunnable *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->taskCompleted((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 1: _t->taskCompletedWithSize((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<qint64>>(_a[3])),(*reinterpret_cast< std::add_pointer_t<qint64>>(_a[4]))); break;
        case 2: _t->taskFailed((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 3: _t->taskFinished(); break;
        case 4: _t->fileSizeIncreasedAfterConversion((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 5: _t->fileSizeIncreasedWithSize((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<qint64>>(_a[3])),(*reinterpret_cast< std::add_pointer_t<qint64>>(_a[4]))); break;
        case 6: _t->fileCorrupted((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[3]))); break;
        case 7: _t->onProcessFinished((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QProcess::ExitStatus>>(_a[2]))); break;
        case 8: _t->onReadyReadStandardError(); break;
        case 9: _t->onReadyReadStandardOutput(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ConversionTaskRunnable::*)(int , const QString & )>(_a, &ConversionTaskRunnable::taskCompleted, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ConversionTaskRunnable::*)(int , const QString & , qint64 , qint64 )>(_a, &ConversionTaskRunnable::taskCompletedWithSize, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ConversionTaskRunnable::*)(int )>(_a, &ConversionTaskRunnable::taskFailed, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (ConversionTaskRunnable::*)()>(_a, &ConversionTaskRunnable::taskFinished, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (ConversionTaskRunnable::*)(int , const QString & )>(_a, &ConversionTaskRunnable::fileSizeIncreasedAfterConversion, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (ConversionTaskRunnable::*)(int , const QString & , qint64 , qint64 )>(_a, &ConversionTaskRunnable::fileSizeIncreasedWithSize, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (ConversionTaskRunnable::*)(int , const QString & , const QString & )>(_a, &ConversionTaskRunnable::fileCorrupted, 6))
            return;
    }
}

const QMetaObject *ConversionTaskRunnable::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ConversionTaskRunnable::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN22ConversionTaskRunnableE_t>.strings))
        return static_cast<void*>(this);
    if (!strcmp(_clname, "QRunnable"))
        return static_cast< QRunnable*>(this);
    return QObject::qt_metacast(_clname);
}

int ConversionTaskRunnable::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 10)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 10;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 10)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 10;
    }
    return _id;
}

// SIGNAL 0
void ConversionTaskRunnable::taskCompleted(int _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 0, nullptr, _t1, _t2);
}

// SIGNAL 1
void ConversionTaskRunnable::taskCompletedWithSize(int _t1, const QString & _t2, qint64 _t3, qint64 _t4)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 1, nullptr, _t1, _t2, _t3, _t4);
}

// SIGNAL 2
void ConversionTaskRunnable::taskFailed(int _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 2, nullptr, _t1);
}

// SIGNAL 3
void ConversionTaskRunnable::taskFinished()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void ConversionTaskRunnable::fileSizeIncreasedAfterConversion(int _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 4, nullptr, _t1, _t2);
}

// SIGNAL 5
void ConversionTaskRunnable::fileSizeIncreasedWithSize(int _t1, const QString & _t2, qint64 _t3, qint64 _t4)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 5, nullptr, _t1, _t2, _t3, _t4);
}

// SIGNAL 6
void ConversionTaskRunnable::fileCorrupted(int _t1, const QString & _t2, const QString & _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 6, nullptr, _t1, _t2, _t3);
}
QT_WARNING_POP
