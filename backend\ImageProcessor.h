﻿#ifndef IMAGEPROCESSOR_H
#define IMAGEPROCESSOR_H

#include <QAbstractListModel>
#include <QCache>
#include <QFileInfo>
#include <QFuture>
#include <QImage>
#include <QMutex>
#include <QSet>
#include <QString>
#include <QStringList>
#include <QUrl>
#include <QScreen>
#include <QPainter>
#include <QGuiApplication>
#include <QDir>
#include <QQueue>
#include <QCryptographicHash>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonDocument>
#include <atomic>
#include <QThreadPool>
#include <QAtomicInteger>
#include <QRunnable>

// 前向声明
class AvifNativeDecoder;

/**
 * @brief The ImageProcessor class manages loading, displaying, and sorting images from a folder.
 * It serves as a model for QML views, providing image data and thumbnails.
 */
class ImageProcessor : public QAbstractListModel {
    Q_OBJECT
    // 属性：最后发生的错误信息
    Q_PROPERTY(QString lastError READ lastError NOTIFY lastErrorChanged)
    // 属性：指示当前是否正在处理（例如加载图片）
    Q_PROPERTY(bool processing READ processing NOTIFY processingChanged)
    // 属性：当前加载的文件/文件夹数量
    Q_PROPERTY(int count READ count NOTIFY countChanged)
    // 属性：当前正在浏览的文件夹路径
    Q_PROPERTY(QString currentFolder READ currentFolder WRITE setCurrentFolder NOTIFY currentFolderChanged)
    // 属性：当前的排序模式
    Q_PROPERTY(int sortMode READ sortMode WRITE setSortMode NOTIFY sortModeChanged)
    // 属性：当前使用的解码器 (0: 命令行工具, 1: WebP/dwebp)
    Q_PROPERTY(int decoderType READ decoderType WRITE setDecoderType NOTIFY decoderTypeChanged)

public:
    // 定义了QML中可以访问的角色
    enum ImageRoles {
        FilePathRole = Qt::UserRole + 1, // 文件完整路径
        FileNameRole,                   // 文件名
        FileSizeRole,                   // 文件大小（字节）
        LastModifiedRole,               // 文件最后修改日期
        ThumbnailRole,                  // 缩略图 (QImage)
        IsLoadingRole,                  // 是否正在加载缩略图
        IsFolderRole                    // 是否为文件夹
    };

    // 解码器类型
    enum DecoderType {
        CommandLineToolType = 0   // 使用命令行工具 (ffmpeg.exe、cwebp.exe、dwebp.exe等)
    };

    explicit ImageProcessor(QObject *parent = nullptr);
    ~ImageProcessor();

    // QAbstractListModel 接口实现
    int rowCount(const QModelIndex &parent = QModelIndex()) const override;
    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;
    QHash<int, QByteArray> roleNames() const override;

    // 属性访问器
    QString lastError() const { return m_lastError; }
    bool processing() const { return m_processing; }
    int count() const { return m_fileInfoList.count(); }
    QString currentFolder() const { return m_currentFolder; }
    void setCurrentFolder(const QString &folder);
    int sortMode() const { return m_sortMode; }
    void setSortMode(int mode); // 0:默认/名称自然排序, 1:名称升序, 2:时间降序, 3:大小升序, 4:大小降序, 5:类型
    int decoderType() const { return m_decoderType; }
    void setDecoderType(int type);
    
    // QML可调用接口
    Q_INVOKABLE void loadImagesFromFolder(const QString &folderPath, bool includeFolders = false);
    Q_INVOKABLE void sortImageList(int sortMode);
    Q_INVOKABLE void refreshCurrentFolder();
    Q_INVOKABLE void clearCache();
    Q_INVOKABLE void clearAllCacheFiles();
    Q_INVOKABLE qint64 getCacheSize() const;
    Q_INVOKABLE QString getFilePath(int index) const;
    Q_INVOKABLE QString getFileName(int index) const;
    Q_INVOKABLE qint64 getFileSize(int index) const;
    Q_INVOKABLE QString getImageInfo(int index) const;
    Q_INVOKABLE QString getThumbnailImage(int index) const;
    Q_INVOKABLE QString getImageUrl(int index) const;
    Q_INVOKABLE void switchDecoder(int decoderType);
    Q_INVOKABLE QString getThumbnailSource(int index);
    Q_INVOKABLE bool isItemLoading(int index) const {
        QMutexLocker locker(&m_mutex);
        return m_processingIndices.contains(index);
    }
    Q_INVOKABLE void requestThumbnails(int startIndex, int endIndex);
    Q_INVOKABLE QImage getPreviewImage(int index) const;
    Q_INVOKABLE QString getAvifPreviewUrl(int index) const;
    Q_INVOKABLE QString getAvifFullImageUrl(int index) const;
    Q_INVOKABLE QImage getAvifImage(int index) const;
    Q_INVOKABLE QUrl imageToUrl(const QImage &image) const;
    Q_INVOKABLE QSize getOptimalSize() const;

    // 网络服务方法
    Q_INVOKABLE QJsonObject getNetworkImageList(const QString &folderPath);
    Q_INVOKABLE QByteArray getNetworkThumbnail(const QString &imagePath);
    Q_INVOKABLE QByteArray getNetworkFullImage(const QString &imagePath);
    Q_INVOKABLE bool canServeNetworkRequest(qint64 estimatedSize = 0);

    // 任务管理
    Q_INVOKABLE void cancelAllTasks();
    Q_INVOKABLE void resetForNewFolder();

signals:
    void lastErrorChanged();
    void processingChanged();
    void countChanged();
    void currentFolderChanged();
    void sortModeChanged();
    void decoderTypeChanged();
    void processingFinished(bool success); // 指示处理完成及其成功状态
    void thumbnailReady(int index);        // 指示指定索引的缩略图已准备好

private slots:
    // 处理缩略图准备好的信号
    void handleThumbnailReady(int index);

private:
    // 内部类声明
    class ThumbnailTask;
    class ThumbnailGenerationTask;

    // 内部辅助函数
    QList<QFileInfo> collectEntriesFromPath(const QString &folderPath, bool includeFolders);
    bool isImageFile(const QString &filePath) const;
    void startThumbnailGeneration();
    static QString findDwebpExecutable();
    QImage generateThumbnail(const QString &filePath) const;
    void setLastError(const QString &error);
    void setProcessing(bool processing);
    QSize getOptimalImageSize() const;
    QImage decodeImage(const QString &filePath, bool thumbnail = false) const;
    void thumbnailWorker();
    // 工具函数：获取缩略图路径和确保目录存在
    QString thumbPathForFile(const QString &filePath) const;
    void ensureThumbCacheDir() const;
    void processThumbnailTask(int index, QAtomicInteger<bool>& stopFlag);

    // 核心数据成员
    QList<QFileInfo> m_fileInfoList;     // 当前文件夹中的文件和文件夹信息
    QString m_currentFolder;             // 当前文件夹路径
    QSet<QString> m_supportedFormats;    // 支持的图像文件扩展名集合
    int m_sortMode = 0;                  // 当前排序模式
    bool m_includeFolders = false;       // 是否在列表中包含文件夹
    int m_decoderType = 0;               // 当前使用的解码器类型
    QString m_lastError;                 // 最后的错误信息
    bool m_processing = false;           // 当前是否正在进行加载等操作

    // 缩略图处理相关
    mutable QCache<QString, QImage> m_thumbnailCache; // 缩略图缓存
    mutable QSet<int> m_processingIndices;            // 正在生成缩略图的条目索引
    // 移除了m_runningTasks，现在使用单线程worker处理缩略图
    int m_thumbnailSize = 200;                        // 缩略图目标尺寸
    QSize m_maxImageSize;                             // 最大图像尺寸
    QQueue<int> m_thumbnailTaskQueue;                 // 缩略图任务队列
    std::atomic<bool> m_thumbnailWorkerRunning {false};
    QString m_thumbCachePath;                         // 缩略图缓存目录路径
    mutable QCache<QString, QString> m_thumbnailFileCache;
    QThreadPool m_thumbnailThreadPool;                // 缩略图线程池

    // QML模型相关
    QHash<int, QByteArray> m_roleNames;  // 角色名映射
    AvifNativeDecoder *m_avifDecoder;    // AVIF原生解码器
    mutable QMutex m_mutex;              // 线程同步
    QAtomicInteger<bool> m_stopFlag {false};
    class ThumbnailTask;
};

#endif // IMAGEPROCESSOR_H
