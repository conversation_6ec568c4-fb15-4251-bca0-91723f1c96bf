[{"moduleFiles": [{"entries": [{"codegenSuccessful": false, "column": 32, "durationMicroseconds": 162, "errorMessage": "Cannot generate efficient code for storing an array in a non-sequence type", "functionName": "pendingFiles", "line": 175}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 359, "errorMessage": "Cannot access value for name imageProcessor", "functionName": "populateFileList", "line": 221}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "onScanningStarted", "line": 246}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "onScanningProgress", "line": 248}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "onScanningFinished", "line": 255}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "onScanningError", "line": 317}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 95, "errorMessage": "Cannot generate efficient code for call to untyped JavaScript function", "functionName": "onScanningCancelled", "line": 318}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "formatBytes", "line": 320}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "extractSizeValue", "line": 321}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 2762, "errorMessage": "Cannot retrieve a non-object type by ID: conversionManager", "functionName": "startConversion", "line": 323}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 54, "errorMessage": "Cannot retrieve a non-object type by ID: conversionManager", "functionName": "toggleConversion", "line": 388}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "showStatusMessage", "line": 402}, {"codegenSuccessful": false, "column": 17, "durationMicroseconds": 14, "errorMessage": "Cannot retrieve a non-object type by ID: conversionManager", "functionName": "target", "line": 36}, {"codegenSuccessful": false, "column": 9, "durationMicroseconds": 37, "errorMessage": "method showStatusMessage cannot be resolved.", "functionName": "onDeleteFilesStarted", "line": 37}, {"codegenSuccessful": false, "column": 9, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "onDeleteFilesCompleted", "line": 38}, {"codegenSuccessful": false, "column": 17, "durationMicroseconds": 12, "errorMessage": "Cannot retrieve a non-object type by ID: conversionManager", "functionName": "target", "line": 50}, {"codegenSuccessful": false, "column": 9, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "onFileConverted", "line": 52}, {"codegenSuccessful": false, "column": 9, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "onFileConvertedWithSize", "line": 83}, {"codegenSuccessful": false, "column": 9, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "onFileSizeIncreasedAfterConversion", "line": 91}, {"codegenSuccessful": false, "column": 9, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "onFileCorrupted", "line": 108}, {"codegenSuccessful": false, "column": 9, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "onFileFailed", "line": 119}, {"codegenSuccessful": false, "column": 9, "durationMicroseconds": 87, "errorMessage": "Cannot access value for name finishedTasks", "functionName": "checkAllTasksFinished", "line": 146}, {"codegenSuccessful": false, "column": 9, "durationMicroseconds": 51, "errorMessage": "Cannot find name totalTasks", "functionName": "onTaskListCleared", "line": 153}, {"codegenSuccessful": false, "column": 9, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "onFileSizeIncreasedWithSize", "line": 161}, {"codegenSuccessful": false, "column": 22, "durationMicroseconds": 130, "errorMessage": "Cannot access value for name displayIndex", "functionName": "onTriggered", "line": 181}, {"codegenSuccessful": false, "column": 28, "durationMicroseconds": 90, "errorMessage": "Cannot retrieve a non-object type by ID: conversionManager", "functionName": "onCompleted", "line": 195}, {"codegenSuccessful": false, "column": 30, "durationMicroseconds": 154, "errorMessage": "Cannot access value for name imageProcessor", "functionName": "onDestruction", "line": 914}, {"codegenSuccessful": false, "column": 17, "durationMicroseconds": 9, "errorMessage": "Cannot access value for name windowBackground", "functionName": "parent", "line": 411}, {"codegenSuccessful": true, "column": 23, "durationMicroseconds": 46, "errorMessage": "", "functionName": "fill", "line": 412}, {"codegenSuccessful": true, "column": 45, "durationMicroseconds": 77, "errorMessage": "", "functionName": "width", "line": 418}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 45, "errorMessage": "", "functionName": "horizontalCenter", "line": 417}, {"codegenSuccessful": true, "column": 79, "durationMicroseconds": 40, "errorMessage": "", "functionName": "bottom", "line": 417}, {"codegenSuccessful": true, "column": 35, "durationMicroseconds": 21, "errorMessage": "", "functionName": "centerIn", "line": 423}, {"codegenSuccessful": true, "column": 30, "durationMicroseconds": 49, "errorMessage": "", "functionName": "onTriggered", "line": 432}, {"codegenSuccessful": true, "column": 22, "durationMicroseconds": 47, "errorMessage": "", "functionName": "top", "line": 442}, {"codegenSuccessful": true, "column": 23, "durationMicroseconds": 38, "errorMessage": "", "functionName": "left", "line": 443}, {"codegenSuccessful": true, "column": 24, "durationMicroseconds": 40, "errorMessage": "", "functionName": "right", "line": 444}, {"codegenSuccessful": true, "column": 27, "durationMicroseconds": 67, "errorMessage": "", "functionName": "left", "line": 455}, {"codegenSuccessful": true, "column": 37, "durationMicroseconds": 40, "errorMessage": "", "functionName": "verticalCenter", "line": 457}, {"codegenSuccessful": true, "column": 77, "durationMicroseconds": 22, "errorMessage": "", "functionName": "centerIn", "line": 464}, {"codegenSuccessful": true, "column": 96, "durationMicroseconds": 23, "errorMessage": "", "functionName": "centerIn", "line": 465}, {"codegenSuccessful": false, "column": 54, "durationMicroseconds": 182, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 467}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 26, "errorMessage": "", "functionName": "fill", "line": 467}, {"codegenSuccessful": true, "column": 93, "durationMicroseconds": 36, "errorMessage": "", "functionName": "type", "line": 468}, {"codegenSuccessful": true, "column": 82, "durationMicroseconds": 46, "errorMessage": "", "functionName": "onEntered", "line": 470}, {"codegenSuccessful": true, "column": 114, "durationMicroseconds": 40, "errorMessage": "", "functionName": "onExited", "line": 470}, {"codegenSuccessful": false, "column": 40, "durationMicroseconds": 196, "errorMessage": "Cannot access value for name currentFolder", "functionName": "onClicked", "line": 471}, {"codegenSuccessful": true, "column": 43, "durationMicroseconds": 21, "errorMessage": "", "functionName": "fill", "line": 470}, {"codegenSuccessful": false, "column": 31, "durationMicroseconds": 35, "errorMessage": "Cannot access value for name isConverting", "functionName": "text", "line": 482}, {"codegenSuccessful": true, "column": 43, "durationMicroseconds": 20, "errorMessage": "", "functionName": "centerIn", "line": 484}, {"codegenSuccessful": false, "column": 31, "durationMicroseconds": 30, "errorMessage": "Cannot access value for name isConverting", "functionName": "text", "line": 488}, {"codegenSuccessful": true, "column": 43, "durationMicroseconds": 20, "errorMessage": "", "functionName": "centerIn", "line": 491}, {"codegenSuccessful": false, "column": 54, "durationMicroseconds": 91, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 495}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 21, "errorMessage": "", "functionName": "fill", "line": 495}, {"codegenSuccessful": true, "column": 93, "durationMicroseconds": 30, "errorMessage": "", "functionName": "type", "line": 496}, {"codegenSuccessful": true, "column": 82, "durationMicroseconds": 52, "errorMessage": "", "functionName": "onEntered", "line": 498}, {"codegenSuccessful": true, "column": 114, "durationMicroseconds": 39, "errorMessage": "", "functionName": "onExited", "line": 498}, {"codegenSuccessful": false, "column": 40, "durationMicroseconds": 155, "errorMessage": "method toggleConversion cannot be resolved.", "functionName": "onClicked", "line": 499}, {"codegenSuccessful": true, "column": 43, "durationMicroseconds": 22, "errorMessage": "", "functionName": "fill", "line": 498}, {"codegenSuccessful": true, "column": 77, "durationMicroseconds": 19, "errorMessage": "", "functionName": "centerIn", "line": 507}, {"codegenSuccessful": true, "column": 96, "durationMicroseconds": 60, "errorMessage": "", "functionName": "centerIn", "line": 508}, {"codegenSuccessful": false, "column": 54, "durationMicroseconds": 84, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 510}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 19, "errorMessage": "", "functionName": "fill", "line": 510}, {"codegenSuccessful": true, "column": 93, "durationMicroseconds": 28, "errorMessage": "", "functionName": "type", "line": 511}, {"codegenSuccessful": true, "column": 82, "durationMicroseconds": 55, "errorMessage": "", "functionName": "onEntered", "line": 514}, {"codegenSuccessful": true, "column": 114, "durationMicroseconds": 40, "errorMessage": "", "functionName": "onExited", "line": 514}, {"codegenSuccessful": false, "column": 40, "durationMicroseconds": 47, "errorMessage": "Cannot generate efficient code for call to untyped JavaScript function", "functionName": "onClicked", "line": 515}, {"codegenSuccessful": true, "column": 43, "durationMicroseconds": 21, "errorMessage": "", "functionName": "fill", "line": 514}, {"codegenSuccessful": false, "column": 42, "durationMicroseconds": 1367, "errorMessage": "Cannot retrieve a non-object type by ID: conversionManager", "functionName": "onTriggered", "line": 522}, {"codegenSuccessful": false, "column": 42, "durationMicroseconds": 101, "errorMessage": "Cannot access value for name targetFormat", "functionName": "onTriggered", "line": 542}, {"codegenSuccessful": false, "column": 42, "durationMicroseconds": 172, "errorMessage": "Cannot retrieve a non-object type by ID: conversionManager", "functionName": "onTriggered", "line": 557}, {"codegenSuccessful": true, "column": 78, "durationMicroseconds": 32, "errorMessage": "", "functionName": "centerIn", "line": 583}, {"codegenSuccessful": true, "column": 100, "durationMicroseconds": 22, "errorMessage": "", "functionName": "centerIn", "line": 584}, {"codegenSuccessful": false, "column": 32, "durationMicroseconds": 47, "errorMessage": "Cannot access value for name includeSubfolders", "functionName": "color", "line": 589}, {"codegenSuccessful": false, "column": 34, "durationMicroseconds": 25, "errorMessage": "Cannot access value for name includeSubfolders", "functionName": "opacity", "line": 592}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 21, "errorMessage": "", "functionName": "fill", "line": 587}, {"codegenSuccessful": true, "column": 93, "durationMicroseconds": 32, "errorMessage": "", "functionName": "type", "line": 593}, {"codegenSuccessful": true, "column": 90, "durationMicroseconds": 28, "errorMessage": "", "functionName": "type", "line": 594}, {"codegenSuccessful": false, "column": 36, "durationMicroseconds": 32, "errorMessage": "Cannot access value for name includeSubfolders", "functionName": "onEntered", "line": 601}, {"codegenSuccessful": false, "column": 35, "durationMicroseconds": 28, "errorMessage": "Cannot access value for name includeSubfolders", "functionName": "onExited", "line": 602}, {"codegenSuccessful": false, "column": 36, "durationMicroseconds": 162, "errorMessage": "Cannot access value for name includeSubfolders", "functionName": "onClicked", "line": 603}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 23, "errorMessage": "", "functionName": "fill", "line": 599}, {"codegenSuccessful": true, "column": 85, "durationMicroseconds": 52, "errorMessage": "", "functionName": "verticalCenter", "line": 613}, {"codegenSuccessful": true, "column": 122, "durationMicroseconds": 44, "errorMessage": "", "functionName": "left", "line": 613}, {"codegenSuccessful": true, "column": 49, "durationMicroseconds": 40, "errorMessage": "", "functionName": "verticalCenter", "line": 616}, {"codegenSuccessful": true, "column": 87, "durationMicroseconds": 39, "errorMessage": "", "functionName": "right", "line": 616}, {"codegenSuccessful": true, "column": 92, "durationMicroseconds": 29, "errorMessage": "", "functionName": "horizontalAlignment", "line": 619}, {"codegenSuccessful": true, "column": 135, "durationMicroseconds": 26, "errorMessage": "", "functionName": "verticalAlignment", "line": 619}, {"codegenSuccessful": false, "column": 35, "durationMicroseconds": 28, "errorMessage": "Cannot access value for name quality", "functionName": "text", "line": 620}, {"codegenSuccessful": false, "column": 48, "durationMicroseconds": 67, "errorMessage": "Cannot find name quality", "functionName": "onEditingFinished", "line": 622}, {"codegenSuccessful": true, "column": 43, "durationMicroseconds": 20, "errorMessage": "", "functionName": "fill", "line": 619}, {"codegenSuccessful": true, "column": 28, "durationMicroseconds": 49, "errorMessage": "", "functionName": "top", "line": 632}, {"codegenSuccessful": true, "column": 50, "durationMicroseconds": 37, "errorMessage": "", "functionName": "left", "line": 632}, {"codegenSuccessful": true, "column": 70, "durationMicroseconds": 59, "errorMessage": "", "functionName": "right", "line": 632}, {"codegenSuccessful": true, "column": 92, "durationMicroseconds": 45, "errorMessage": "", "functionName": "bottom", "line": 632}, {"codegenSuccessful": true, "column": 48, "durationMicroseconds": 48, "errorMessage": "", "functionName": "top", "line": 637}, {"codegenSuccessful": true, "column": 66, "durationMicroseconds": 38, "errorMessage": "", "functionName": "left", "line": 637}, {"codegenSuccessful": true, "column": 86, "durationMicroseconds": 37, "errorMessage": "", "functionName": "right", "line": 637}, {"codegenSuccessful": true, "column": 35, "durationMicroseconds": 21, "errorMessage": "", "functionName": "fill", "line": 639}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 49, "errorMessage": "", "functionName": "width", "line": 641}, {"codegenSuccessful": true, "column": 33, "durationMicroseconds": 49, "errorMessage": "", "functionName": "height", "line": 644}, {"codegenSuccessful": true, "column": 66, "durationMicroseconds": 30, "errorMessage": "", "functionName": "verticalAlignment", "line": 644}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 47, "errorMessage": "", "functionName": "width", "line": 647}, {"codegenSuccessful": true, "column": 33, "durationMicroseconds": 26, "errorMessage": "", "functionName": "height", "line": 650}, {"codegenSuccessful": true, "column": 66, "durationMicroseconds": 26, "errorMessage": "", "functionName": "verticalAlignment", "line": 650}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 45, "errorMessage": "", "functionName": "width", "line": 653}, {"codegenSuccessful": true, "column": 33, "durationMicroseconds": 26, "errorMessage": "", "functionName": "height", "line": 656}, {"codegenSuccessful": true, "column": 66, "durationMicroseconds": 26, "errorMessage": "", "functionName": "verticalAlignment", "line": 656}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 111, "errorMessage": "", "functionName": "width", "line": 659}, {"codegenSuccessful": true, "column": 33, "durationMicroseconds": 35, "errorMessage": "", "functionName": "height", "line": 662}, {"codegenSuccessful": true, "column": 66, "durationMicroseconds": 30, "errorMessage": "", "functionName": "verticalAlignment", "line": 662}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 51, "errorMessage": "", "functionName": "width", "line": 665}, {"codegenSuccessful": true, "column": 33, "durationMicroseconds": 28, "errorMessage": "", "functionName": "height", "line": 668}, {"codegenSuccessful": true, "column": 66, "durationMicroseconds": 28, "errorMessage": "", "functionName": "verticalAlignment", "line": 668}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 46, "errorMessage": "", "functionName": "width", "line": 671}, {"codegenSuccessful": true, "column": 33, "durationMicroseconds": 25, "errorMessage": "", "functionName": "height", "line": 674}, {"codegenSuccessful": true, "column": 66, "durationMicroseconds": 25, "errorMessage": "", "functionName": "verticalAlignment", "line": 674}, {"codegenSuccessful": true, "column": 24, "durationMicroseconds": 30, "errorMessage": "", "functionName": "model", "line": 684}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 41, "errorMessage": "", "functionName": "top", "line": 682}, {"codegenSuccessful": true, "column": 57, "durationMicroseconds": 38, "errorMessage": "", "functionName": "left", "line": 682}, {"codegenSuccessful": true, "column": 77, "durationMicroseconds": 39, "errorMessage": "", "functionName": "right", "line": 682}, {"codegenSuccessful": true, "column": 99, "durationMicroseconds": 36, "errorMessage": "", "functionName": "bottom", "line": 682}, {"codegenSuccessful": true, "column": 57, "durationMicroseconds": 35, "errorMessage": "", "functionName": "policy", "line": 685}, {"codegenSuccessful": false, "column": 28, "durationMicroseconds": 22, "errorMessage": "Cannot access value for name fileListView", "functionName": "width", "line": 688}, {"codegenSuccessful": false, "column": 67, "durationMicroseconds": 50, "errorMessage": "Cannot access value for name index", "functionName": "color", "line": 688}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 27, "errorMessage": "", "functionName": "fill", "line": 690}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 69, "errorMessage": "", "functionName": "width", "line": 692}, {"codegenSuccessful": false, "column": 35, "durationMicroseconds": 23, "errorMessage": "Cannot access value for name model", "functionName": "text", "line": 693}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 81, "errorMessage": "", "functionName": "elide", "line": 696}, {"codegenSuccessful": true, "column": 37, "durationMicroseconds": 31, "errorMessage": "", "functionName": "height", "line": 697}, {"codegenSuccessful": true, "column": 48, "durationMicroseconds": 29, "errorMessage": "", "functionName": "verticalAlignment", "line": 698}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 50, "errorMessage": "", "functionName": "width", "line": 701}, {"codegenSuccessful": false, "column": 35, "durationMicroseconds": 20, "errorMessage": "Cannot access value for name model", "functionName": "text", "line": 702}, {"codegenSuccessful": true, "column": 37, "durationMicroseconds": 26, "errorMessage": "", "functionName": "height", "line": 705}, {"codegenSuccessful": true, "column": 48, "durationMicroseconds": 25, "errorMessage": "", "functionName": "verticalAlignment", "line": 706}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 45, "errorMessage": "", "functionName": "width", "line": 709}, {"codegenSuccessful": false, "column": 35, "durationMicroseconds": 17, "errorMessage": "Cannot access value for name model", "functionName": "text", "line": 710}, {"codegenSuccessful": true, "column": 37, "durationMicroseconds": 29, "errorMessage": "", "functionName": "height", "line": 713}, {"codegenSuccessful": true, "column": 48, "durationMicroseconds": 25, "errorMessage": "", "functionName": "verticalAlignment", "line": 714}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 44, "errorMessage": "", "functionName": "width", "line": 717}, {"codegenSuccessful": false, "column": 35, "durationMicroseconds": 18, "errorMessage": "Cannot access value for name model", "functionName": "text", "line": 718}, {"codegenSuccessful": true, "column": 37, "durationMicroseconds": 27, "errorMessage": "", "functionName": "height", "line": 721}, {"codegenSuccessful": true, "column": 48, "durationMicroseconds": 25, "errorMessage": "", "functionName": "verticalAlignment", "line": 722}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 47, "errorMessage": "", "functionName": "width", "line": 725}, {"codegenSuccessful": false, "column": 35, "durationMicroseconds": 19, "errorMessage": "Cannot access value for name model", "functionName": "text", "line": 726}, {"codegenSuccessful": true, "column": 37, "durationMicroseconds": 29, "errorMessage": "", "functionName": "height", "line": 729}, {"codegenSuccessful": true, "column": 48, "durationMicroseconds": 28, "errorMessage": "", "functionName": "verticalAlignment", "line": 730}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 103, "errorMessage": "", "functionName": "width", "line": 733}, {"codegenSuccessful": true, "column": 64, "durationMicroseconds": 28, "errorMessage": "", "functionName": "height", "line": 733}, {"codegenSuccessful": false, "column": 39, "durationMicroseconds": 23, "errorMessage": "Cannot access value for name model", "functionName": "text", "line": 739}, {"codegenSuccessful": true, "column": 41, "durationMicroseconds": 29, "errorMessage": "", "functionName": "height", "line": 741}, {"codegenSuccessful": true, "column": 52, "durationMicroseconds": 26, "errorMessage": "", "functionName": "verticalAlignment", "line": 742}, {"codegenSuccessful": false, "column": 40, "durationMicroseconds": 146, "errorMessage": "Cannot access value for name model", "functionName": "color", "line": 744}, {"codegenSuccessful": true, "column": 47, "durationMicroseconds": 43, "errorMessage": "", "functionName": "left", "line": 736}, {"codegenSuccessful": true, "column": 57, "durationMicroseconds": 38, "errorMessage": "", "functionName": "verticalCenter", "line": 738}, {"codegenSuccessful": false, "column": 44, "durationMicroseconds": 19, "errorMessage": "Cannot access value for name model", "functionName": "bold", "line": 743}, {"codegenSuccessful": false, "column": 39, "durationMicroseconds": 43, "errorMessage": "Cannot access value for name model", "functionName": "text", "line": 760}, {"codegenSuccessful": false, "column": 42, "durationMicroseconds": 37, "errorMessage": "Cannot access value for name model", "functionName": "visible", "line": 763}, {"codegenSuccessful": true, "column": 47, "durationMicroseconds": 43, "errorMessage": "", "functionName": "left", "line": 757}, {"codegenSuccessful": true, "column": 57, "durationMicroseconds": 59, "errorMessage": "", "functionName": "verticalCenter", "line": 759}, {"codegenSuccessful": false, "column": 42, "durationMicroseconds": 37, "errorMessage": "Cannot access value for name model", "functionName": "visible", "line": 769}, {"codegenSuccessful": true, "column": 49, "durationMicroseconds": 42, "errorMessage": "", "functionName": "left", "line": 767}, {"codegenSuccessful": true, "column": 74, "durationMicroseconds": 37, "errorMessage": "", "functionName": "right", "line": 767}, {"codegenSuccessful": true, "column": 104, "durationMicroseconds": 36, "errorMessage": "", "functionName": "verticalCenter", "line": 767}, {"codegenSuccessful": false, "column": 44, "durationMicroseconds": 25, "errorMessage": "Cannot access value for name model", "functionName": "width", "line": 771}, {"codegenSuccessful": true, "column": 95, "durationMicroseconds": 27, "errorMessage": "", "functionName": "height", "line": 771}, {"codegenSuccessful": false, "column": 44, "durationMicroseconds": 29, "errorMessage": "Cannot access value for name model", "functionName": "color", "line": 772}, {"codegenSuccessful": true, "column": 103, "durationMicroseconds": 31, "errorMessage": "", "functionName": "type", "line": 773}, {"codegenSuccessful": false, "column": 27, "durationMicroseconds": 38, "errorMessage": "Cannot access value for name isLoading", "functionName": "text", "line": 782}, {"codegenSuccessful": true, "column": 30, "durationMicroseconds": 109, "errorMessage": "", "functionName": "visible", "line": 792}, {"codegenSuccessful": true, "column": 40, "durationMicroseconds": 32, "errorMessage": "", "functionName": "verticalAlignment", "line": 794}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 20, "errorMessage": "", "functionName": "centerIn", "line": 781}, {"codegenSuccessful": false, "column": 56, "durationMicroseconds": 14, "errorMessage": "Cannot access value for name isLoading", "functionName": "running", "line": 798}, {"codegenSuccessful": false, "column": 76, "durationMicroseconds": 34, "errorMessage": "Cannot access value for name isLoading", "functionName": "visible", "line": 798}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 21, "errorMessage": "", "functionName": "centerIn", "line": 798}, {"codegenSuccessful": false, "column": 22, "durationMicroseconds": 13, "errorMessage": "Cannot retrieve a non-object type by ID: conversionManager", "functionName": "visible", "line": 806}, {"codegenSuccessful": true, "column": 28, "durationMicroseconds": 41, "errorMessage": "", "functionName": "right", "line": 811}, {"codegenSuccessful": true, "column": 26, "durationMicroseconds": 38, "errorMessage": "", "functionName": "top", "line": 812}, {"codegenSuccessful": true, "column": 35, "durationMicroseconds": 19, "errorMessage": "", "functionName": "centerIn", "line": 818}, {"codegenSuccessful": false, "column": 28, "durationMicroseconds": 15, "errorMessage": "Cannot retrieve a non-object type by ID: conversionManager", "functionName": "onClicked", "line": 826}, {"codegenSuccessful": true, "column": 31, "durationMicroseconds": 19, "errorMessage": "", "functionName": "fill", "line": 825}, {"codegenSuccessful": false, "column": 22, "durationMicroseconds": 9, "errorMessage": "Cannot access value for name isLoading", "functionName": "visible", "line": 836}, {"codegenSuccessful": false, "column": 20, "durationMicroseconds": 107, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 838}, {"codegenSuccessful": true, "column": 27, "durationMicroseconds": 20, "errorMessage": "", "functionName": "fill", "line": 837}, {"codegenSuccessful": true, "column": 24, "durationMicroseconds": 48, "errorMessage": "", "functionName": "width", "line": 844}, {"codegenSuccessful": true, "column": 35, "durationMicroseconds": 19, "errorMessage": "", "functionName": "centerIn", "line": 842}, {"codegenSuccessful": true, "column": 47, "durationMicroseconds": 47, "errorMessage": "", "functionName": "horizontalCenter", "line": 848}, {"codegenSuccessful": true, "column": 28, "durationMicroseconds": 26, "errorMessage": "", "functionName": "width", "line": 856}, {"codegenSuccessful": true, "column": 47, "durationMicroseconds": 37, "errorMessage": "", "functionName": "horizontalCenter", "line": 855}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 46, "errorMessage": "", "functionName": "width", "line": 863}, {"codegenSuccessful": true, "column": 33, "durationMicroseconds": 74, "errorMessage": "", "functionName": "height", "line": 864}, {"codegenSuccessful": false, "column": 38, "durationMicroseconds": 16, "errorMessage": "Cannot access value for name isLoading", "functionName": "running", "line": 870}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 30, "errorMessage": "", "functionName": "loops", "line": 871}, {"codegenSuccessful": true, "column": 41, "durationMicroseconds": 35, "errorMessage": "", "functionName": "target", "line": 874}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 59, "errorMessage": "", "functionName": "from", "line": 876}, {"codegenSuccessful": true, "column": 37, "durationMicroseconds": 54, "errorMessage": "", "functionName": "to", "line": 877}, {"codegenSuccessful": true, "column": 46, "durationMicroseconds": 32, "errorMessage": "", "functionName": "type", "line": 879}, {"codegenSuccessful": true, "column": 41, "durationMicroseconds": 48, "errorMessage": "", "functionName": "target", "line": 883}, {"codegenSuccessful": true, "column": 37, "durationMicroseconds": 199, "errorMessage": "", "functionName": "to", "line": 886}, {"codegenSuccessful": true, "column": 46, "durationMicroseconds": 66, "errorMessage": "", "functionName": "type", "line": 888}, {"codegenSuccessful": true, "column": 41, "durationMicroseconds": 49, "errorMessage": "", "functionName": "target", "line": 892}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 82, "errorMessage": "", "functionName": "from", "line": 894}, {"codegenSuccessful": true, "column": 37, "durationMicroseconds": 66, "errorMessage": "", "functionName": "to", "line": 895}, {"codegenSuccessful": true, "column": 46, "durationMicroseconds": 37, "errorMessage": "", "functionName": "type", "line": 897}, {"codegenSuccessful": true, "column": 41, "durationMicroseconds": 33, "errorMessage": "", "functionName": "target", "line": 901}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 132, "errorMessage": "", "functionName": "from", "line": 903}, {"codegenSuccessful": true, "column": 46, "durationMicroseconds": 37, "errorMessage": "", "functionName": "type", "line": 906}], "filePath": "C:/Qt/file/palyer/compoment/toolbar/ConversionWindow.qml"}], "moduleId": "palyer(palyer)"}]