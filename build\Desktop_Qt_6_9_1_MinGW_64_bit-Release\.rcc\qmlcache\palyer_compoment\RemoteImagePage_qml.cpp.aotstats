[{"moduleFiles": [{"entries": [{"codegenSuccessful": true, "column": 12, "durationMicroseconds": 147, "errorMessage": "", "functionName": "width", "line": 12}, {"codegenSuccessful": true, "column": 34, "durationMicroseconds": 37, "errorMessage": "", "functionName": "height", "line": 12}, {"codegenSuccessful": false, "column": 34, "durationMicroseconds": 39, "errorMessage": "Cannot generate efficient code for storing an array in a non-sequence type", "functionName": "previewWindows", "line": 17}, {"codegenSuccessful": false, "column": 59, "durationMicroseconds": 129, "errorMessage": "Cannot generate efficient code for storing an array in a non-sequence type", "functionName": "sortModeNames", "line": 18}, {"codegenSuccessful": false, "column": 32, "durationMicroseconds": 171, "errorMessage": "Cannot access value for name clientMode", "functionName": "isConnected", "line": 23}, {"codegenSuccessful": false, "column": 35, "durationMicroseconds": 27, "errorMessage": "Cannot generate efficient code for storing an array in a non-sequence type", "functionName": "remoteImageList", "line": 24}, {"codegenSuccessful": false, "column": 39, "durationMicroseconds": 24, "errorMessage": "Cannot generate efficient code for storing an array in a non-sequence type", "functionName": "remoteImageOnlyList", "line": 25}, {"codegenSuccessful": true, "column": 33, "durationMicroseconds": 31, "errorMessage": "", "functionName": "fileTreeCache", "line": 28}, {"codegenSuccessful": false, "column": 27, "durationMicroseconds": 401, "errorMessage": "Cannot generate efficient code for call to untyped JavaScript function", "functionName": "onCurrentP<PERSON><PERSON><PERSON><PERSON>", "line": 31}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 275, "errorMessage": "Cannot access value for name clientMode", "functionName": "loadImagesFromFolder", "line": 81}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "navigateToFolder", "line": 96}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "updateImageList", "line": 104}, {"codegenSuccessful": true, "column": 5, "durationMicroseconds": 30, "errorMessage": "", "functionName": "clearCache", "line": 119}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 30, "errorMessage": "Instruction \"generate_DeleteProperty\" not implemented", "functionName": "refreshCurrentPath", "line": 122}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "naturalSortComparator", "line": 133}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "sortRemoteImageList", "line": 160}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "getBreadcrumbParts", "line": 533}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "openRemoteImagePreview", "line": 557}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "removePreviewWindow", "line": 605}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "showFileNameTip", "line": 616}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "showSortTip", "line": 624}, {"codegenSuccessful": true, "column": 16, "durationMicroseconds": 78, "errorMessage": "", "functionName": "width", "line": 42}, {"codegenSuccessful": false, "column": 16, "durationMicroseconds": 117, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 43}, {"codegenSuccessful": true, "column": 48, "durationMicroseconds": 63, "errorMessage": "", "functionName": "visible", "line": 43}, {"codegenSuccessful": true, "column": 62, "durationMicroseconds": 21, "errorMessage": "", "functionName": "centerIn", "line": 45}, {"codegenSuccessful": true, "column": 59, "durationMicroseconds": 52, "errorMessage": "", "functionName": "onTriggered", "line": 46}, {"codegenSuccessful": true, "column": 16, "durationMicroseconds": 60, "errorMessage": "", "functionName": "width", "line": 51}, {"codegenSuccessful": true, "column": 45, "durationMicroseconds": 66, "errorMessage": "", "functionName": "height", "line": 51}, {"codegenSuccessful": false, "column": 16, "durationMicroseconds": 86, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 52}, {"codegenSuccessful": true, "column": 55, "durationMicroseconds": 65, "errorMessage": "", "functionName": "visible", "line": 52}, {"codegenSuccessful": true, "column": 24, "durationMicroseconds": 297, "errorMessage": "", "functionName": "onTextChanged", "line": 57}, {"codegenSuccessful": false, "column": 23, "durationMicroseconds": 106, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 53}, {"codegenSuccessful": true, "column": 48, "durationMicroseconds": 21, "errorMessage": "", "functionName": "centerIn", "line": 55}, {"codegenSuccessful": true, "column": 16, "durationMicroseconds": 51, "errorMessage": "", "functionName": "width", "line": 66}, {"codegenSuccessful": false, "column": 70, "durationMicroseconds": 93, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 66}, {"codegenSuccessful": true, "column": 37, "durationMicroseconds": 42, "errorMessage": "", "functionName": "horizontalCenter", "line": 67}, {"codegenSuccessful": true, "column": 70, "durationMicroseconds": 37, "errorMessage": "", "functionName": "bottom", "line": 67}, {"codegenSuccessful": true, "column": 51, "durationMicroseconds": 20, "errorMessage": "", "functionName": "centerIn", "line": 69}, {"codegenSuccessful": true, "column": 64, "durationMicroseconds": 81, "errorMessage": "", "functionName": "onTriggered", "line": 70}, {"codegenSuccessful": false, "column": 29, "durationMicroseconds": 25, "errorMessage": "Cannot generate efficient code for storing an array in a non-sequence type", "functionName": "files", "line": 76}, {"codegenSuccessful": true, "column": 23, "durationMicroseconds": 36, "errorMessage": "", "functionName": "fill", "line": 198}, {"codegenSuccessful": false, "column": 26, "durationMicroseconds": 17, "errorMessage": "Cannot access value for name isConnected", "functionName": "enabled", "line": 206}, {"codegenSuccessful": false, "column": 28, "durationMicroseconds": 255, "errorMessage": "method refreshCurrentPath cannot be resolved.", "functionName": "onClicked", "line": 207}, {"codegenSuccessful": true, "column": 33, "durationMicroseconds": 46, "errorMessage": "", "functionName": "left", "line": 204}, {"codegenSuccessful": true, "column": 77, "durationMicroseconds": 40, "errorMessage": "", "functionName": "verticalCenter", "line": 204}, {"codegenSuccessful": false, "column": 32, "durationMicroseconds": 143, "errorMessage": "method refreshCurrentPath cannot be resolved.", "functionName": "onClicked", "line": 212}, {"codegenSuccessful": false, "column": 32, "durationMicroseconds": 539, "errorMessage": "Cannot generate efficient code for reading from a value that's potentially affected by side effects", "functionName": "onEntered", "line": 213}, {"codegenSuccessful": true, "column": 31, "durationMicroseconds": 49, "errorMessage": "", "functionName": "onExited", "line": 220}, {"codegenSuccessful": false, "column": 40, "durationMicroseconds": 590, "errorMessage": "Cannot generate efficient code for reading from a value that's potentially affected by side effects", "functionName": "onPositionChanged", "line": 221}, {"codegenSuccessful": true, "column": 35, "durationMicroseconds": 24, "errorMessage": "", "functionName": "fill", "line": 210}, {"codegenSuccessful": true, "column": 25, "durationMicroseconds": 31, "errorMessage": "", "functionName": "height", "line": 233}, {"codegenSuccessful": true, "column": 33, "durationMicroseconds": 42, "errorMessage": "", "functionName": "left", "line": 232}, {"codegenSuccessful": true, "column": 69, "durationMicroseconds": 39, "errorMessage": "", "functionName": "right", "line": 232}, {"codegenSuccessful": true, "column": 116, "durationMicroseconds": 38, "errorMessage": "", "functionName": "verticalCenter", "line": 232}, {"codegenSuccessful": true, "column": 45, "durationMicroseconds": 86, "errorMessage": "", "functionName": "verticalCenter", "line": 235}, {"codegenSuccessful": false, "column": 32, "durationMicroseconds": 133, "errorMessage": "Cannot access value for name fullPath", "functionName": "model", "line": 238}, {"codegenSuccessful": false, "column": 39, "durationMicroseconds": 149, "errorMessage": "Cannot access value for name modelData", "functionName": "text", "line": 241}, {"codegenSuccessful": false, "column": 44, "durationMicroseconds": 148, "errorMessage": "Cannot access value for name modelData", "functionName": "bold", "line": 242}, {"codegenSuccessful": false, "column": 56, "durationMicroseconds": 279, "errorMessage": "Cannot access value for name index", "functionName": "onCompleted", "line": 243}, {"codegenSuccessful": true, "column": 72, "durationMicroseconds": 42, "errorMessage": "", "functionName": "cursor<PERSON><PERSON>pe", "line": 245}, {"codegenSuccessful": true, "column": 112, "durationMicroseconds": 79, "errorMessage": "", "functionName": "acceptedButtons", "line": 245}, {"codegenSuccessful": false, "column": 48, "durationMicroseconds": 466, "errorMessage": "Cannot access value for name modelData", "functionName": "onClicked", "line": 246}, {"codegenSuccessful": true, "column": 48, "durationMicroseconds": 27, "errorMessage": "", "functionName": "onClicked", "line": 246}, {"codegenSuccessful": true, "column": 51, "durationMicroseconds": 27, "errorMessage": "", "functionName": "fill", "line": 245}, {"codegenSuccessful": false, "column": 110, "durationMicroseconds": 132, "errorMessage": "Cannot access value for name index", "functionName": "visible", "line": 267}, {"codegenSuccessful": false, "column": 63, "durationMicroseconds": 97, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 274}, {"codegenSuccessful": false, "column": 26, "durationMicroseconds": 41, "errorMessage": "Cannot access value for name isConnected", "functionName": "visible", "line": 283}, {"codegenSuccessful": true, "column": 31, "durationMicroseconds": 32, "errorMessage": "", "functionName": "fill", "line": 282}, {"codegenSuccessful": true, "column": 35, "durationMicroseconds": 44, "errorMessage": "", "functionName": "contentWidth", "line": 287}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 35, "errorMessage": "", "functionName": "contentHeight", "line": 288}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 139, "errorMessage": "", "functionName": "width", "line": 293}, {"codegenSuccessful": true, "column": 34, "durationMicroseconds": 135, "errorMessage": "", "functionName": "columns", "line": 295}, {"codegenSuccessful": false, "column": 36, "durationMicroseconds": 22, "errorMessage": "Cannot access value for name remoteImageList", "functionName": "model", "line": 299}, {"codegenSuccessful": false, "column": 57, "durationMicroseconds": 56, "errorMessage": "Cannot access value for name index", "functionName": "imageData", "line": 303}, {"codegenSuccessful": false, "column": 60, "durationMicroseconds": 15, "errorMessage": "Cannot load property name from (component in C:/Qt/file/palyer/compoment/RemoteImagePage.qml)::imageData with type QVariant.", "functionName": "imageName", "line": 304}, {"codegenSuccessful": false, "column": 60, "durationMicroseconds": 13, "errorMessage": "Cannot load property path from (component in C:/Qt/file/palyer/compoment/RemoteImagePage.qml)::imageData with type QVariant.", "functionName": "imagePath", "line": 305}, {"codegenSuccessful": false, "column": 57, "durationMicroseconds": 14, "errorMessage": "Cannot load property isFolder from (component in C:/Qt/file/palyer/compoment/RemoteImagePage.qml)::imageData with type QVariant.", "functionName": "isFolder", "line": 306}, {"codegenSuccessful": false, "column": 64, "durationMicroseconds": 327, "errorMessage": "Cannot access value for name clientMode", "functionName": "currentSource", "line": 307}, {"codegenSuccessful": false, "column": 40, "durationMicroseconds": 164, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 322}, {"codegenSuccessful": false, "column": 56, "durationMicroseconds": 67, "errorMessage": "Cannot access value for name imageGrid", "functionName": "preferredWidth", "line": 320}, {"codegenSuccessful": true, "column": 57, "durationMicroseconds": 34, "errorMessage": "", "functionName": "preferredHeight", "line": 321}, {"codegenSuccessful": true, "column": 51, "durationMicroseconds": 22, "errorMessage": "", "functionName": "fill", "line": 326}, {"codegenSuccessful": false, "column": 49, "durationMicroseconds": 45, "errorMessage": "Cannot load property fileNameHeight from (component in C:/Qt/file/palyer/compoment/RemoteImagePage.qml)::parent with type QQuickItem.", "functionName": "height", "line": 331}, {"codegenSuccessful": true, "column": 50, "durationMicroseconds": 344, "errorMessage": "", "functionName": "opacity", "line": 332}, {"codegenSuccessful": true, "column": 56, "durationMicroseconds": 48, "errorMessage": "", "functionName": "top", "line": 330}, {"codegenSuccessful": true, "column": 74, "durationMicroseconds": 55, "errorMessage": "", "functionName": "left", "line": 330}, {"codegenSuccessful": true, "column": 94, "durationMicroseconds": 39, "errorMessage": "", "functionName": "right", "line": 330}, {"codegenSuccessful": true, "column": 54, "durationMicroseconds": 50, "errorMessage": "", "functionName": "visible", "line": 338}, {"codegenSuccessful": true, "column": 113, "durationMicroseconds": 21, "errorMessage": "", "functionName": "centerIn", "line": 338}, {"codegenSuccessful": true, "column": 61, "durationMicroseconds": 51, "errorMessage": "", "functionName": "pixelSize", "line": 339}, {"codegenSuccessful": false, "column": 60, "durationMicroseconds": 214, "errorMessage": "Cannot access value for name fileNameTip", "functionName": "onEntered", "line": 345}, {"codegenSuccessful": false, "column": 59, "durationMicroseconds": 25, "errorMessage": "Cannot access value for name fileNameTip", "functionName": "onExited", "line": 354}, {"codegenSuccessful": false, "column": 68, "durationMicroseconds": 267, "errorMessage": "Cannot access value for name fileNameTip", "functionName": "onPositionChanged", "line": 358}, {"codegenSuccessful": true, "column": 60, "durationMicroseconds": 32, "errorMessage": "", "functionName": "onClicked", "line": 366}, {"codegenSuccessful": true, "column": 60, "durationMicroseconds": 19, "errorMessage": "", "functionName": "onClicked", "line": 366}, {"codegenSuccessful": true, "column": 63, "durationMicroseconds": 25, "errorMessage": "", "functionName": "fill", "line": 343}, {"codegenSuccessful": true, "column": 55, "durationMicroseconds": 34, "errorMessage": "", "functionName": "fillMode", "line": 377}, {"codegenSuccessful": true, "column": 53, "durationMicroseconds": 41, "errorMessage": "", "functionName": "source", "line": 378}, {"codegenSuccessful": true, "column": 54, "durationMicroseconds": 36, "errorMessage": "", "functionName": "visible", "line": 379}, {"codegenSuccessful": true, "column": 52, "durationMicroseconds": 58, "errorMessage": "", "functionName": "scale", "line": 380}, {"codegenSuccessful": true, "column": 62, "durationMicroseconds": 209, "errorMessage": "", "functionName": "onStatusChanged", "line": 384}, {"codegenSuccessful": true, "column": 59, "durationMicroseconds": 22, "errorMessage": "", "functionName": "fill", "line": 376}, {"codegenSuccessful": true, "column": 111, "durationMicroseconds": 31, "errorMessage": "", "functionName": "type", "line": 381}, {"codegenSuccessful": true, "column": 58, "durationMicroseconds": 100, "errorMessage": "", "functionName": "running", "line": 395}, {"codegenSuccessful": true, "column": 58, "durationMicroseconds": 22, "errorMessage": "", "functionName": "visible", "line": 396}, {"codegenSuccessful": true, "column": 67, "durationMicroseconds": 39, "errorMessage": "", "functionName": "centerIn", "line": 394}, {"codegenSuccessful": true, "column": 59, "durationMicroseconds": 19, "errorMessage": "", "functionName": "fill", "line": 403}, {"codegenSuccessful": true, "column": 67, "durationMicroseconds": 20, "errorMessage": "", "functionName": "centerIn", "line": 407}, {"codegenSuccessful": false, "column": 62, "durationMicroseconds": 351, "errorMessage": "method navigateToFolder cannot be resolved.", "functionName": "onDoubleClicked", "line": 418}, {"codegenSuccessful": true, "column": 59, "durationMicroseconds": 22, "errorMessage": "", "functionName": "fill", "line": 417}, {"codegenSuccessful": false, "column": 49, "durationMicroseconds": 36, "errorMessage": "Cannot load property fileNameHeight from (component in C:/Qt/file/palyer/compoment/RemoteImagePage.qml)::parent with type QQuickItem.", "functionName": "height", "line": 432}, {"codegenSuccessful": false, "column": 79, "durationMicroseconds": 92, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 432}, {"codegenSuccessful": true, "column": 57, "durationMicroseconds": 43, "errorMessage": "", "functionName": "left", "line": 431}, {"codegenSuccessful": true, "column": 77, "durationMicroseconds": 80, "errorMessage": "", "functionName": "right", "line": 431}, {"codegenSuccessful": true, "column": 99, "durationMicroseconds": 38, "errorMessage": "", "functionName": "bottom", "line": 431}, {"codegenSuccessful": true, "column": 51, "durationMicroseconds": 30, "errorMessage": "", "functionName": "text", "line": 436}, {"codegenSuccessful": true, "column": 64, "durationMicroseconds": 32, "errorMessage": "", "functionName": "verticalAlignment", "line": 438}, {"codegenSuccessful": true, "column": 66, "durationMicroseconds": 146, "errorMessage": "", "functionName": "horizontalAlignment", "line": 439}, {"codegenSuccessful": true, "column": 57, "durationMicroseconds": 31, "errorMessage": "", "functionName": "textFormat", "line": 440}, {"codegenSuccessful": true, "column": 55, "durationMicroseconds": 28, "errorMessage": "", "functionName": "wrapMode", "line": 441}, {"codegenSuccessful": true, "column": 59, "durationMicroseconds": 29, "errorMessage": "", "functionName": "fill", "line": 435}, {"codegenSuccessful": true, "column": 55, "durationMicroseconds": 59, "errorMessage": "", "functionName": "font", "line": 446}, {"codegenSuccessful": true, "column": 55, "durationMicroseconds": 30, "errorMessage": "", "functionName": "text", "line": 447}, {"codegenSuccessful": true, "column": 66, "durationMicroseconds": 85, "errorMessage": "", "functionName": "acceptedButtons", "line": 452}, {"codegenSuccessful": false, "column": 60, "durationMicroseconds": 225, "errorMessage": "Cannot access value for name fileNameTip", "functionName": "onEntered", "line": 453}, {"codegenSuccessful": false, "column": 59, "durationMicroseconds": 23, "errorMessage": "Cannot access value for name fileNameTip", "functionName": "onExited", "line": 462}, {"codegenSuccessful": false, "column": 68, "durationMicroseconds": 339, "errorMessage": "Cannot access value for name fileNameTip", "functionName": "onPositionChanged", "line": 465}, {"codegenSuccessful": true, "column": 60, "durationMicroseconds": 47, "errorMessage": "", "functionName": "onClicked", "line": 473}, {"codegenSuccessful": true, "column": 60, "durationMicroseconds": 20, "errorMessage": "", "functionName": "onClicked", "line": 473}, {"codegenSuccessful": true, "column": 63, "durationMicroseconds": 26, "errorMessage": "", "functionName": "fill", "line": 451}, {"codegenSuccessful": false, "column": 22, "durationMicroseconds": 128, "errorMessage": "Cannot access value for name sortMode", "functionName": "onActivated", "line": 495}, {"codegenSuccessful": false, "column": 17, "durationMicroseconds": 36, "errorMessage": "Cannot access value for name clientMode", "functionName": "target", "line": 505}, {"codegenSuccessful": false, "column": 9, "durationMicroseconds": 65, "errorMessage": "Cannot access value for name clientMode", "functionName": "onConnectedChanged", "line": 506}, {"codegenSuccessful": false, "column": 9, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "onImageListLoaded", "line": 511}, {"codegenSuccessful": false, "column": 9, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "onThumbnailCached", "line": 523}, {"codegenSuccessful": true, "column": 28, "durationMicroseconds": 127, "errorMessage": "", "functionName": "onCompleted", "line": 630}], "filePath": "C:/Qt/file/palyer/compoment/RemoteImagePage.qml"}], "moduleId": "palyer(palyer)"}]