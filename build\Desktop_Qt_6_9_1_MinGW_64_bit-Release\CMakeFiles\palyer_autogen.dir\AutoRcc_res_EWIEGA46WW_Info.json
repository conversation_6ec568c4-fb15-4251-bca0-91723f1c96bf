{"BUILD_DIR": "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/palyer_autogen", "CMAKE_BINARY_DIR": "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release", "CMAKE_CURRENT_BINARY_DIR": "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release", "CMAKE_CURRENT_SOURCE_DIR": "C:/Qt/file/palyer", "CMAKE_SOURCE_DIR": "C:/Qt/file/palyer", "CROSS_CONFIG": false, "GENERATOR": "Ninja", "INCLUDE_DIR": "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/palyer_autogen/include", "INPUTS": ["C:/Qt/file/palyer/source/7btrrd.mp4", "C:/Qt/file/palyer/source/img/music.svg", "C:/Qt/file/palyer/source/img/seethings.svg", "C:/Qt/file/palyer/source/img/movie.svg", "C:/Qt/file/palyer/source/img/remote.svg", "C:/Qt/file/palyer/source/img/picture.svg", "C:/Qt/file/palyer/source/img/symble.svg", "C:/Qt/file/palyer/compoment/ImagePreviewWindow.qml", "C:/Qt/file/palyer/compoment/imagePage.qml", "C:/Qt/file/palyer/compoment/AppState.qml", "C:/Qt/file/palyer/compoment/SettingsPage.qml", "C:/Qt/file/palyer/compoment/RemoteImagePage.qml", "C:/Qt/file/palyer/compoment/template/BaseWindowTemplate.qml", "C:/Qt/file/palyer/compoment/toolbar/ConversionWindow.qml"], "LOCK_FILE": "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/CMakeFiles/palyer_autogen.dir/AutoRcc_res_EWIEGA46WW_Lock.lock", "MULTI_CONFIG": false, "OPTIONS": ["--no-zstd", "-name", "res"], "OUTPUT_CHECKSUM": "EWIEGA46WW", "OUTPUT_NAME": "qrc_res.cpp", "RCC_EXECUTABLE": "C:/Qt/6.9.1/mingw_64/bin/rcc.exe", "RCC_LIST_OPTIONS": ["--list"], "SETTINGS_FILE": "C:/Qt/file/palyer/build/Desktop_Qt_6_9_1_MinGW_64_bit-Release/CMakeFiles/palyer_autogen.dir/AutoRcc_res_EWIEGA46WW_Used.txt", "SOURCE": "C:/Qt/file/palyer/res.qrc", "USE_BETTER_GRAPH": true, "VERBOSITY": 0}