﻿#include "ConversionTaskRunnable.h"
#include <QImage>
#include <QDir>
#include <QFileInfo>
#include <QDebug>
#include <QProcess>
#include <QCoreApplication>
#include <QFile>
#include <QImageReader>
#include <QTimer>
#include <QRegularExpression>
#include <QDateTime>
#include <QEventLoop>
#include <QMediaPlayer>
#include <QMediaMetaData>

#ifdef Q_OS_WIN
#include <windows.h>
#include <shellapi.h>
#endif

// ===================================================================
// ConversionTaskRunnable: 负责转换图像
// ===================================================================

// 检查图片是否为单色（如绿幕）
bool isSingleColorImage(const QString& filePath, const QColor& suspectColor);

ConversionTaskRunnable::ConversionTaskRunnable(ConversionTask task)
    : m_task(task), m_processRunning(false), m_process(nullptr) {
    // 强制设置为false，确保不会自动删除，让我们可以在processFinished后控制删除时机
    setAutoDelete(false);
}

ConversionTaskRunnable::~ConversionTaskRunnable() {
    // 确保进程被清理
    if (m_process) {
        // 断开所有信号连接以避免在销毁过程中触发回调
        m_process->disconnect();
        
        // 如果进程还在运行，先终止它
        if (m_process->state() != QProcess::NotRunning) {
            // 尝试先优雅地终止进程
            m_process->terminate();
            
            // 等待进程终止，但最多等待1秒
            if (!m_process->waitForFinished(1000)) {
                // 如果进程没有及时终止，强制杀死它
                m_process->kill();
                m_process->waitForFinished(500);
            }
        }
        
        // 安全删除进程
        try {
            delete m_process;
        } catch (const std::exception& e) {
            qDebug() << "删除 QProcess 时发生异常:" << e.what();
        } catch (...) {
            qDebug() << "删除 QProcess 时发生未知异常";
        }
        m_process = nullptr;
    }
}

void ConversionTaskRunnable::run() {
    // 这是线程入口点
    try {
        // 确保不会有旧的 QProcess 实例
        if (m_process) {
            // 如果旧进程仍在运行，先终止它
            if (m_process->state() != QProcess::NotRunning) {
                m_process->disconnect();
                m_process->terminate();
                if (!m_process->waitForFinished(1000)) {
                    m_process->kill();
                    m_process->waitForFinished(1000);
                }
            }
            delete m_process;
            m_process = nullptr;
        }
        
        // 在工作线程中创建QProcess对象
        m_process = new QProcess();
        
        // 必须在工作线程中连接信号
        connect(m_process, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished), 
                this, &ConversionTaskRunnable::onProcessFinished, Qt::DirectConnection);
                
        // 添加环境变量以输出更多调试信息
        QProcessEnvironment env = QProcessEnvironment::systemEnvironment();
        env.insert("AV_LOG_FORCE_NOCOLOR", "1"); // 禁用颜色以便于日志解析
        env.insert("FFREPORT", "file=ffmpeg-log.txt:level=32"); // 创建更详细的日志文件
        m_process->setProcessEnvironment(env);
        
        // 连接输出信号以便调试 - 使用旧式SIGNAL/SLOT语法
        connect(m_process, SIGNAL(readyReadStandardError()), this, SLOT(onReadyReadStandardError()), Qt::DirectConnection);
        connect(m_process, SIGNAL(readyReadStandardOutput()), this, SLOT(onReadyReadStandardOutput()), Qt::DirectConnection);
        
        // 获取原始文件大小
        QFileInfo sourceInfo(m_task.sourcePath);
        if (!sourceInfo.exists()) {
            emit taskFailed(m_task.index);
            emit taskFinished(); // 信号用于清理
            return;
        }
        
        m_originalFileSize = sourceInfo.size();

        // 确保目标目录存在
        QFileInfo destInfo(m_task.destPath);
        QDir destDir = destInfo.dir();
        if (!destDir.exists()) {
            destDir.mkpath(".");
        }
        
        // 检查目标文件是否已存在 - 如果存在，跳过转换但标记为完成
        if (QFile::exists(m_task.destPath)) {
            qDebug() << "目标文件已存在，跳过转换:" << m_task.destPath;
            // 发送taskCompleted信号而不是taskFailed，这样前端可以正确处理删除源文件
            emit taskCompleted(m_task.index, m_task.destPath);
            emit taskFinished();
            return;
        }
        

        
        // 根据目标格式执行相应的转换
        if (m_task.targetFormat.toLower() == "avif") {
            convertToAvif();
        } else if (m_task.targetFormat.toLower() == "webp") {
            convertToWebp();
        } else if (m_task.targetFormat.toLower() == "av1") {
            convertToAV1();
        } else {
            qDebug() << "未知格式:" << m_task.targetFormat;
            emit taskFailed(m_task.index);
            emit taskFinished();
        }
    } catch (const std::exception& e) {
        qDebug() << "ConversionTaskRunnable::run 发生异常:" << e.what();
        try {
            emit taskFailed(m_task.index);
            emit taskFinished();
        } catch (...) {
            qDebug() << "发送信号时发生异常";
        }
    } catch (...) {
        qDebug() << "ConversionTaskRunnable::run 发生未知异常";
        try {
            emit taskFailed(m_task.index);
            emit taskFinished();
        } catch (...) {
            qDebug() << "发送信号时发生异常";
        }
    }
}

void ConversionTaskRunnable::onProcessFinished(int exitCode, QProcess::ExitStatus exitStatus) {
    try {
        m_processRunning = false;
        
        // 确保在退出前断开QProcess的所有连接
        if (m_process) {
            m_process->disconnect();
        }
        
        if (exitStatus == QProcess::CrashExit || exitCode != 0) {
            qDebug() << "进程异常退出或返回非零状态码:" << exitCode;
            
            // 检查重试次数
            if (m_task.retryCount < 3) {
                m_task.retryCount++;
                qDebug() << "重试转换任务，当前重试次数:" << m_task.retryCount;

                // 重新执行转换，不调整质量
                if (m_task.targetFormat.toLower() == "avif") {
                    QTimer::singleShot(100, this, &ConversionTaskRunnable::convertToAvif);
                } else if (m_task.targetFormat.toLower() == "webp") {
                    QTimer::singleShot(100, this, &ConversionTaskRunnable::convertToWebp);
                } else if (m_task.targetFormat.toLower() == "av1") {
                    QTimer::singleShot(100, this, &ConversionTaskRunnable::convertToAV1);
                } else {
                    emit taskFailed(m_task.index);
                    emit taskFinished();
                }
                return;
            }
            
            emit taskFailed(m_task.index);
            
            // 最后一步：使用QueuedConnection确保当前事件处理完毕后再发送完成信号
            QMetaObject::invokeMethod(this, [this]() {
                emit taskFinished();
            }, Qt::QueuedConnection);
            return;
        }
        
        // 检查转换后的文件是否存在
        QFileInfo destInfo(m_task.destPath);
        if (!destInfo.exists()) {
            qDebug() << "目标文件不存在:" << m_task.destPath;
            emit taskFailed(m_task.index);
            emit taskFinished();
            return;
        }

        // 只进行基本的文件大小检查，不重复验证
        qint64 newFileSize = destInfo.size();

        // 检查文件大小是否增加
        if (newFileSize > m_originalFileSize) {
            qDebug() << "onProcessFinished: 文件大小增加，从" << m_originalFileSize << "到" << newFileSize << ":" << m_task.destPath;
            emit fileSizeIncreasedWithSize(m_task.index, m_task.sourcePath, m_originalFileSize, newFileSize);
            QFile::remove(m_task.destPath);
            emit taskFailed(m_task.index);
            emit taskFinished();
            return;
        }

        // 文件大小正常，发送成功信号
        qDebug() << "onProcessFinished: 文件转换成功:" << m_task.destPath;
        emit taskCompleted(m_task.index, m_task.destPath);
        emit taskCompletedWithSize(m_task.index, m_task.destPath, m_originalFileSize, newFileSize);
        emit taskFinished();
    } catch (const std::exception& e) {
        qDebug() << "onProcessFinished 发生异常:" << e.what();
        try {
            emit taskFailed(m_task.index);
            
            // 确保在异常情况下也会发送完成信号
            QMetaObject::invokeMethod(this, [this]() {
                emit taskFinished();
            }, Qt::QueuedConnection);
        } catch (...) {
            qDebug() << "发送信号时发生异常";
        }
    } catch (...) {
        qDebug() << "onProcessFinished 发生未知异常";
        try {
            emit taskFailed(m_task.index);
            
            // 确保在异常情况下也会发送完成信号
            QMetaObject::invokeMethod(this, [this]() {
                emit taskFinished();
            }, Qt::QueuedConnection);
        } catch (...) {
            qDebug() << "发送信号时发生异常";
        }
    }
}

// 处理标准错误输出
void ConversionTaskRunnable::onReadyReadStandardError() {
    if (m_process) {
        m_process->readAllStandardError(); // 清空错误输出缓冲区
    }
}

// 处理标准输出
void ConversionTaskRunnable::onReadyReadStandardOutput() {
    if (m_process) m_process->readAllStandardOutput(); // 清空标准输出缓冲区
}

void ConversionTaskRunnable::convertToAvif() {
    if (m_task.sourcePath.endsWith(".gif", Qt::CaseInsensitive)) { emit taskFailed(m_task.index); emit taskFinished(); return; }

    // 检查分辨率限制
    QSize imageSize = QImageReader(m_task.sourcePath).size();
    if (!imageSize.isValid()) { emit taskFailed(m_task.index); emit taskFinished(); return; }
    if (imageSize.width() > 8192 || imageSize.height() > 8192) {
        qDebug() << "分辨率超出AVIF限制，跳过转换:" << imageSize.width() << "x" << imageSize.height();
        emit fileCorrupted(m_task.index, m_task.sourcePath, "分辨率超出限制");
        emit taskFailed(m_task.index); emit taskFinished(); return;
    }

    QString ffmpegPath = QCoreApplication::applicationDirPath() + "/ffmpeg.exe";
    if (!ffmpegPath.isEmpty()) {
        QProcess process;
        QStringList args;
        args << "-i" << m_task.sourcePath
             << "-c:v" << "av1_qsv"
             << "-global_quality" << QString::number(qMin(51, 101 - m_task.quality))
             << "-preset" << "slower"
             << "-bf" << "0"
             << "-y"  // 自动覆盖已存在的文件
             << m_task.destPath;
        process.start(ffmpegPath, args);
        if (process.waitForStarted(10000)) {
            bool finished = process.waitForFinished(120000); // 最多等2分钟
            if (finished && process.exitCode() == 0) {
                // 检查生成的文件大小，清理空文件或过小文件
                QFileInfo destInfo(m_task.destPath);
                if (!destInfo.exists() || destInfo.size() < 100) { // 小于100字节认为是损坏文件
                    qDebug() << "AVIF文件过小或不存在，大小:" << destInfo.size() << "字节";
                    if (destInfo.exists()) QFile::remove(m_task.destPath); // 直接删除无效文件
                    emit fileCorrupted(m_task.index, m_task.sourcePath, "生成文件过小");
                    emit taskFailed(m_task.index);
                    emit taskFinished();
                    return;
                }

                // 转换完成后立即验证文件
                bool isValid = validateFile(m_task.destPath);

                if (!isValid) {
                    qDebug() << "AVIF文件验证失败，重新尝试转换:" << m_task.destPath;
                    
                    // 移动损坏的文件到回收站
                    moveToRecycleBin(m_task.destPath);
                    
                    // 检查重试次数
                    if (m_task.retryCount < 3) {
                        m_task.retryCount++;
                        qDebug() << "重试AVIF转换，当前重试次数:" << m_task.retryCount;
                        
                        // 重新执行转换，不降低质量
                        convertToAvif();
                        return;
                    } else {
                        // 达到最大重试次数，报告失败
                        qDebug() << "AVIF转换达到最大重试次数，放弃处理:" << m_task.destPath;
                        emit fileCorrupted(m_task.index, m_task.destPath, "达到最大重试次数后仍然验证失败");
                        emit taskFailed(m_task.index);
                        emit taskFinished();
                        return;
                    }
                }
                
                // 验证通过，检查文件大小
                checkFileSize();
                return;
            } else {
                qDebug() << "ffmpeg硬件加速转换AVIF失败，exitCode:" << process.exitCode() << process.readAllStandardError();

                // 清理可能生成的损坏文件
                QFileInfo destInfo(m_task.destPath);
                if (destInfo.exists()) {
                    qDebug() << "清理转换失败的残留文件:" << m_task.destPath;
                    QFile::remove(m_task.destPath); // 直接删除失败文件
                }

                // 转换失败，检查重试次数
                if (m_task.retryCount < 3) {
                    m_task.retryCount++;
                    qDebug() << "AVIF转换失败，重试，当前重试次数:" << m_task.retryCount;

                    // 重新执行转换，不降低质量
                    convertToAvif();
                    return;
                }
            }
        } else {
            qDebug() << "ffmpeg启动失败";
        }
    } else {
        qDebug() << "未找到ffmpeg，跳过ffmpeg转换";
    }
    
    // 所有尝试都失败
    emit taskFailed(m_task.index);
    emit taskFinished();
    return;
}

void ConversionTaskRunnable::convertToAV1() {
    // 检查目标文件是否已存在且为AV1编码
    if (QFileInfo::exists(m_task.destPath)) {
        if (isVideoAV1Encoded(m_task.destPath)) {
            // 文件已存在且是AV1编码，跳过转换
            emit taskCompleted(m_task.index, m_task.destPath);
            emit taskFinished();
            return;
        } else {
            // 文件存在但不是AV1编码，删除后重新转换
            moveToRecycleBin(m_task.destPath);
        }
    }

    QString ffmpegPath = QCoreApplication::applicationDirPath() + "/ffmpeg.exe";
    if (!QFileInfo::exists(ffmpegPath)) {
        qDebug() << "未找到ffmpeg，路径:" << ffmpegPath;
        emit taskFailed(m_task.index);
        emit taskFinished();
        return;
    }

    QProcess process;
    QStringList args;

    // 检查源文件格式，针对MOV文件特殊处理
    bool isMovFile = m_task.sourcePath.toLower().endsWith(".mov");

    args << "-i" << m_task.sourcePath
         << "-c:v" << "av1_qsv"
         << "-global_quality" << "18";

    // MOV文件可能有特殊的音频编码，需要重新编码
    if (isMovFile) {
        args << "-c:a" << "aac" << "-b:a" << "128k"; // 重新编码音频为AAC
    } else {
        args << "-c:a" << "copy"; // 其他格式直接复制音频
    }

    args << "-y" << m_task.destPath;

    // 设置进程输出模式，避免缓冲区阻塞
    process.setProcessChannelMode(QProcess::MergedChannels);

    process.start(ffmpegPath, args);
    if (!process.waitForStarted(10000)) {
        qDebug() << "AV1转换启动失败";
        emit taskFailed(m_task.index);
        emit taskFinished();
        return;
    }

    // 使用同步等待，避免在工作线程中运行事件循环导致UI卡顿
    if (!process.waitForFinished(-1)) { // -1表示无超时限制
        qDebug() << "AV1转换进程异常终止";
        emit taskFailed(m_task.index);
        emit taskFinished();
        return;
    }

    bool finished = (process.state() == QProcess::NotRunning);

    if (finished && process.exitCode() == 0) {
        // FFmpeg成功完成，直接检查文件大小
        checkFileSize();
        return;
    } else {
        qDebug() << "AV1转换失败，exitCode:" << process.exitCode();

        if (m_task.retryCount < 3) {
            m_task.retryCount++;
            convertToAV1();
            return;
        }
    }

    emit taskFailed(m_task.index);
    emit taskFinished();
}

void ConversionTaskRunnable::convertToWebp() {
    // 跳过gif文件
    if (m_task.sourcePath.endsWith(".gif", Qt::CaseInsensitive)) {
        qDebug() << "跳过gif文件的WebP转换:" << m_task.sourcePath;
        emit taskFailed(m_task.index);
        emit taskFinished();
        return;
    }
    QSize imageSize = QImageReader(m_task.sourcePath).size();
    if (!imageSize.isValid()) { qDebug() << "无法读取图像尺寸:" << m_task.sourcePath; emit taskFailed(m_task.index); emit taskFinished(); return; }
    if (imageSize.width() > 16383 || imageSize.height() > 16383) {
        qDebug() << "分辨率超出WebP限制，跳过转换:" << imageSize.width() << "x" << imageSize.height();
        emit fileCorrupted(m_task.index, m_task.sourcePath, "分辨率超出限制");
        emit taskFailed(m_task.index); emit taskFinished(); return;
    }
    
    // 确保没有正在运行的进程
    if (m_process && m_process->state() != QProcess::NotRunning) {
        m_process->disconnect();
        m_process->terminate();
        if (!m_process->waitForFinished(3000)) {  // 增加超时时间到3秒
            qDebug() << "WebP进程终止超时，强制杀死进程";
            m_process->kill();
            m_process->waitForFinished(3000);  // 增加超时时间到3秒
        }
    }
    
    // 首先尝试使用cwebp工具
    QString cwebpPath = QCoreApplication::applicationDirPath() + "/cwebp.exe";
    if (!cwebpPath.isEmpty()) {
        QStringList args;
        if (m_task.quality >= 100) args << "-lossless";
        else args << "-q" << QString::number(m_task.quality);
        args << m_task.sourcePath << "-o" << m_task.destPath;

        // 直接在工作线程中启动进程，不移动到主线程
        m_processRunning = true;

        m_process->start(cwebpPath, args);
        
        if (!m_process->waitForStarted(10000)) {  // 增加启动超时时间到10秒
            m_processRunning = false;
            
            // 如果外部工具启动失败，立即回退到Qt内置处理
            fallbackToQtWebp();
            return;
        }
        
        // 等待进程完成
        qDebug() << "等待WebP转换完成:" << m_task.destPath;

        // 断开信号连接，避免与同步处理冲突
        m_process->disconnect();

        if (m_process->waitForFinished(180000)) {  // 增加超时时间到3分钟
            if (m_process->exitCode() == 0) {
                // 检查生成的文件大小，清理空文件或过小文件
                QFileInfo destInfo(m_task.destPath);
                if (!destInfo.exists() || destInfo.size() < 100) {
                    qDebug() << "WebP文件过小或不存在，大小:" << destInfo.size() << "字节";
                    if (destInfo.exists()) QFile::remove(m_task.destPath); // 直接删除无效文件
                    emit fileCorrupted(m_task.index, m_task.sourcePath, "生成文件过小");
                    emit taskFailed(m_task.index);
                    emit taskFinished();
                    return;
                }

                // 转换完成后立即验证文件
                bool isValid = validateFile(m_task.destPath);
                
                if (!isValid) {
                    qDebug() << "WebP文件验证失败，重新尝试转换:" << m_task.destPath;
                    
                    // 移动损坏的文件到回收站
                    moveToRecycleBin(m_task.destPath);
                    
                    // 检查重试次数
                    if (m_task.retryCount < 3) {
                        m_task.retryCount++;
                        qDebug() << "重试WebP转换，当前重试次数:" << m_task.retryCount;
                        
                        // 重新执行转换，不调整质量
                        convertToWebp();
                        return;
                    } else {
                        // 达到最大重试次数，尝试使用Qt内置方法
                        qDebug() << "WebP转换达到最大重试次数，尝试Qt内置方法";
                        fallbackToQtWebp();
                        return;
                    }
                }
                
                // 验证通过，检查文件大小
                checkFileSize();
                return;
            } else {
                qDebug() << "cwebp转换失败，exitCode:" << m_process->exitCode();

                // 清理可能生成的损坏文件
                QFileInfo destInfo(m_task.destPath);
                if (destInfo.exists()) {
                    qDebug() << "清理转换失败的残留文件:" << m_task.destPath;
                    QFile::remove(m_task.destPath); // 直接删除失败文件
                }

                // 转换失败，检查重试次数
                if (m_task.retryCount < 3) {
                    m_task.retryCount++;
                    qDebug() << "WebP转换失败，重试，当前重试次数:" << m_task.retryCount;
                    
                    // 重新执行转换，不调整质量
                    convertToWebp();
                    return;
                }
            }
        } else {
            qDebug() << "WebP转换超时，尝试Qt内置方法";
        }
        
        // 如果等待超时或进程失败，回退到Qt内置处理
        fallbackToQtWebp();
        return;
    }
    
    qDebug() << "找不到cwebp.exe，使用Qt内置方法";
    // 如果没有找到cwebp或启动失败，使用Qt内置编码器
    fallbackToQtWebp();
}

// 抽取WebP的Qt回退处理为单独的方法，便于错误处理
void ConversionTaskRunnable::fallbackToQtWebp() {
    // 回退到Qt内置编码器 - 在当前工作线程中运行
    
    try {
        QSize imageSize = QImageReader(m_task.sourcePath).size();
        if (!imageSize.isValid()) { emit taskFailed(m_task.index); emit taskFinished(); return; }
        if (imageSize.width() > 16383 || imageSize.height() > 16383) {
            qDebug() << "分辨率超出WebP限制，跳过转换:" << imageSize.width() << "x" << imageSize.height();
            emit fileCorrupted(m_task.index, m_task.sourcePath, "分辨率超出限制");
            emit taskFailed(m_task.index); emit taskFinished(); return;
        }
        QImage image(m_task.sourcePath);
        if (image.isNull()) { emit taskFailed(m_task.index); emit taskFinished(); return; }
        
        bool success = image.save(m_task.destPath, "WEBP", m_task.quality);
        if (!success) {
            emit taskFailed(m_task.index);
            emit taskFinished();
            return;
        }
        
        // 转换完成后立即验证文件
        bool isValid = validateFile(m_task.destPath);
        
        if (!isValid) {
            qDebug() << "Qt内置WebP文件验证失败，重新尝试转换:" << m_task.destPath;
            
            // 移动损坏的文件到回收站
            moveToRecycleBin(m_task.destPath);
            
            // 检查重试次数
            if (m_task.retryCount < 3) {
                m_task.retryCount++;
                qDebug() << "重试Qt内置WebP转换，当前重试次数:" << m_task.retryCount;
                
                // 重新执行转换，不调整质量
                fallbackToQtWebp();
                return;
            } else {
                // 达到最大重试次数，报告失败
                qDebug() << "Qt内置WebP转换达到最大重试次数，放弃处理:" << m_task.destPath;
                emit fileCorrupted(m_task.index, m_task.destPath, "达到最大重试次数后仍然验证失败");
                emit taskFailed(m_task.index);
                emit taskFinished();
                return;
            }
        }
        
        // 验证通过，检查文件大小
        checkFileSize();
        return;
    } catch (const std::exception& e) {
        qDebug() << "Qt内置WebP转换发生异常:" << e.what();
        emit taskFailed(m_task.index);
        emit taskFinished();
    } catch (...) {
        qDebug() << "Qt内置WebP转换发生未知异常";
        emit taskFailed(m_task.index);
        emit taskFinished();
    }
}

// 检查文件大小并处理转换结果
void ConversionTaskRunnable::checkFileSize() {
    // 检查转换后的文件大小
    qint64 newFileSize = 0;
    
    // 使用作用域来确保文件句柄被及时释放
    {
        QFileInfo destInfo(m_task.destPath);
        if (!destInfo.exists()) { emit taskFailed(m_task.index); emit taskFinished(); return; }
        
        newFileSize = destInfo.size();
        
        // 尝试打开并关闭文件，确保文件句柄被释放
        QFile testFile(m_task.destPath);
        if (testFile.open(QIODevice::ReadOnly)) testFile.close();
    }
    
    // 释放文件句柄，不调用processEvents避免UI卡顿
    
    // 检查文件大小是否增加
    if (newFileSize > m_originalFileSize) {
        // 发送文件变大信号，同时包含大小信息
        emit fileSizeIncreasedWithSize(m_task.index, m_task.sourcePath, m_originalFileSize, newFileSize);

        // 直接删除变大的无效文件
        QFile::remove(m_task.destPath);

        emit taskFailed(m_task.index);
        emit taskFinished();
        return;
    }
    
    // 检查是否为绿幕异常
    if (isSingleColorImage(m_task.destPath, QColor(0,255,0))) {
        qDebug() << "检测到绿幕异常，移动到回收站:" << m_task.destPath;
        moveToRecycleBin(m_task.destPath);
        emit fileCorrupted(m_task.index, m_task.destPath, "内容异常（绿幕）");
        emit taskFailed(m_task.index);
        emit taskFinished();
        return;
    }
    
    // 转换成功，发送带文件大小的信号
    emit taskCompleted(m_task.index, m_task.destPath);
    emit taskCompletedWithSize(m_task.index, m_task.destPath, m_originalFileSize, newFileSize);
    emit taskFinished();
}

// findExecutable函数已移除，现在直接使用应用程序目录下的可执行文件

// 使用ffmpeg验证文件是否损坏
bool ConversionTaskRunnable::validateFile(const QString &filePath) {
    QString ffmpegPath = QCoreApplication::applicationDirPath() + "/ffmpeg.exe";
    if (!QFileInfo::exists(ffmpegPath)) return true; // 如果找不到ffmpeg，假定文件有效
    
    QProcess process;
    QStringList args;
    args << "-v" << "error" << "-i" << filePath << "-f" << "null" << "-";
    
    // 设置进程以便捕获标准错误输出
    process.setProcessChannelMode(QProcess::MergedChannels);
    
    // 确保文件句柄被释放
    {
        // 先尝试读取文件的基本信息，然后关闭，确保没有文件句柄持有
        QFile testFile(filePath);
        if (testFile.open(QIODevice::ReadOnly)) testFile.close();
    }
    
    // 释放文件句柄，不调用processEvents避免UI卡顿
    
    process.start(ffmpegPath, args);
    
    if (!process.waitForStarted(5000)) {
        qDebug() << "启动ffmpeg进程失败";
        return false;
    }
    
    if (!process.waitForFinished(15000)) {
        qDebug() << "ffmpeg验证超时，强制终止进程";
        process.kill();
        if (!process.waitForFinished(3000)) {
            process.terminate();
        }
        return false;
    }
    
    // 读取所有输出（包括错误输出）
    QString output = process.readAllStandardOutput();
    
    // 确保进程资源被完全释放
    process.close();
    
    // 只根据退出代码判断，忽略信息性输出
    if (process.exitCode() != 0) {
        qDebug() << "文件验证失败，exitCode:" << process.exitCode();
        return false;
    }

    return true;
}

// 检查图片是否为单色（如绿幕）
bool isSingleColorImage(const QString& filePath, const QColor& suspectColor) {
    QImage img(filePath);
    if (img.isNull()) return false; // 无法加载，交给其它逻辑处理

    int sameColorCount = 0;
    int total = img.width() * img.height();
    for (int y = 0; y < img.height(); ++y) {
        const QRgb* line = reinterpret_cast<const QRgb*>(img.scanLine(y));
        for (int x = 0; x < img.width(); ++x) {
            QColor c(line[x]);
            if (qAbs(c.red() - suspectColor.red()) < 10 &&
                qAbs(c.green() - suspectColor.green()) < 10 &&
                qAbs(c.blue() - suspectColor.blue()) < 10) {
                ++sameColorCount;
            }
        }
    }
    return sameColorCount > total * 0.9;
}

bool ConversionTaskRunnable::isVideoAV1Encoded(const QString &filePath) {
    // 简化检测：通过文件名判断是否为AV1文件
    // 避免在工作线程中使用QMediaPlayer和事件循环
    QFileInfo fileInfo(filePath);
    QString fileName = fileInfo.fileName();

    // 如果文件名包含_av1，认为是AV1编码
    if (fileName.contains("_av1.")) {
        return true;
    }

    // 检查文件是否存在且大小合理
    if (!fileInfo.exists() || fileInfo.size() < 1024) {
        return false; // 文件不存在或太小
    }

    // 默认认为不是AV1编码，让转换程序处理
    return false;
}

// 移动文件到回收站
bool ConversionTaskRunnable::moveToRecycleBin(const QString &filePath) {
#ifdef Q_OS_WIN
    // Windows系统使用SHFileOperation
    QString nativePath = QDir::toNativeSeparators(filePath);

    // 确保路径以双空字符结尾
    std::wstring wPath = nativePath.toStdWString();
    wPath.push_back(L'\0');
    wPath.push_back(L'\0');

    SHFILEOPSTRUCTW fileOp;
    fileOp.hwnd = nullptr;
    fileOp.wFunc = FO_DELETE;
    fileOp.pFrom = wPath.c_str();
    fileOp.pTo = nullptr;
    fileOp.fFlags = FOF_ALLOWUNDO | FOF_NOCONFIRMATION | FOF_SILENT;
    fileOp.fAnyOperationsAborted = FALSE;
    fileOp.hNameMappings = nullptr;
    fileOp.lpszProgressTitle = nullptr;

    int result = SHFileOperationW(&fileOp);
    return (result == 0);
#else
    // 非Windows系统，使用QFile::moveToTrash (Qt 5.15+)
    return QFile::moveToTrash(filePath);
#endif
}
