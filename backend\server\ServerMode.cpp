#include "ServerMode.h"
#include "ImageProcessor.h"
#include <QNetworkInterface>
#include <QHostAddress>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonDocument>
#include <QFileInfo>
#include <QDir>
#include <QUrl>
#include <QMutexLocker>
#include <QRegularExpression>
#include <QTextStream>
#include <QDateTime>
#include <QMimeDatabase>
#include <QTimer>
#include <QQueue>
#include <QDebug>
#include <QThread>
#include <QCryptographicHash>
#include <QRandomGenerator>
#include <QSettings>
#include <functional>

ServerMode::ServerMode(QObject* parent) : QObject(parent) {
    refreshLocalAddresses();

    // 初始化重试定时器
    m_retryTimer = new QTimer(this);
    m_retryTimer->setSingleShot(true);
    m_retryTimer->setInterval(1000); // 1秒后处理重试
    connect(m_retryTimer, &QTimer::timeout, this, &ServerMode::processRetryQueue);

    // 初始化令牌为默认值
    m_authToken = "imageviewer2025";
}

ServerMode::~ServerMode() {
    stopImageServer();
}

void ServerMode::refreshLocalAddresses() {
    QStringList v4;
    QString v6first;
    for (const QNetworkInterface& iface : QNetworkInterface::allInterfaces()) {
        if (!(iface.flags() & QNetworkInterface::IsUp) ||
            (iface.flags() & QNetworkInterface::IsLoopBack) ||
            (iface.flags() & QNetworkInterface::IsPointToPoint) ||
            iface.humanReadableName().contains("VMware", Qt::CaseInsensitive) ||
            iface.humanReadableName().contains("VirtualBox", Qt::CaseInsensitive) ||
            iface.humanReadableName().contains("Loopback", Qt::CaseInsensitive) ||
            iface.humanReadableName().contains("Bluetooth", Qt::CaseInsensitive))
            continue;
        for (const QNetworkAddressEntry& entry : iface.addressEntries()) {
            QHostAddress addr = entry.ip();
            if (addr.protocol() == QAbstractSocket::IPv4Protocol && !addr.isLoopback()) {
                v4 << addr.toString();
            } else if (addr.protocol() == QAbstractSocket::IPv6Protocol && !addr.isLoopback() && v6first.isEmpty()) {
                v6first = addr.toString();
            }
        }
    }

    QStringList v6;
    if (!v6first.isEmpty()) v6 << v6first;

    if (m_ipv4Addresses != v4 || m_ipv6Addresses != v6) {
        m_ipv4Addresses = v4;
        m_ipv6Addresses = v6;
        emit localAddressesChanged();
    }
}

bool ServerMode::startImageServer(int port) {
    QMutexLocker locker(&m_serverMutex);

    if (m_tcpServer && m_tcpServer->isListening()) {
        qWarning() << "Server already running on port" << m_currentPort;
        return false;
    }

    if (!m_imageProcessor) {
        qWarning() << "ImageProcessor not set";
        return false;
    }

    m_tcpServer = new QTcpServer(this);
    connect(m_tcpServer, &QTcpServer::newConnection, this, &ServerMode::onNewConnection);

    if (m_tcpServer->listen(QHostAddress::Any, port)) {
        m_currentPort = m_tcpServer->serverPort();
        qInfo() << "Image server started on port" << m_currentPort;

        // 输出访问地址信息
        qInfo() << "=== 网络访问地址 ===";
        qInfo() << "本地访问: http://127.0.0.1:" << m_currentPort;
        qInfo() << "本地访问: http://localhost:" << m_currentPort;

        // 输出所有可用的网络接口地址
        if (!m_ipv4Addresses.isEmpty()) {
            qInfo() << "局域网访问地址:";
            for (const QString& ip : std::as_const(m_ipv4Addresses)) {
                qInfo() << "  http://" << ip << ":" << m_currentPort;
            }
        }

        if (!m_ipv6Addresses.isEmpty()) {
            qInfo() << "IPv6访问地址:";
            for (const QString& ip : std::as_const(m_ipv6Addresses)) {
                qInfo() << "  http://[" << ip << "]:" << m_currentPort;
            }
        }

        qInfo() << "API示例:";
        QString exampleIP = m_ipv4Addresses.isEmpty() ? "127.0.0.1" : m_ipv4Addresses.first();
        qInfo() << "  获取根目录: http://" << exampleIP << ":" << m_currentPort << "/api/images?path=/";

        // 显示每个监听文件夹的访问示例
        if (!m_watchFolders.isEmpty()) {
            for (const QString& watchFolder : qAsConst(m_watchFolders)) {
                QFileInfo folderInfo(watchFolder);
                QString folderName = folderInfo.baseName();
                qInfo() << "  访问文件夹 '" << folderName << "': http://" << exampleIP << ":" << m_currentPort << "/api/images?path=/" << folderName;
                qInfo() << "  获取图片: http://" << exampleIP << ":" << m_currentPort << "/api/image/" << folderName << "/图片名.jpg";
            }
        }
        qInfo() << "==================";

        emit serverStatusChanged();
        return true;
    } else {
        qWarning() << "Failed to start server:" << m_tcpServer->errorString();
        delete m_tcpServer;
        m_tcpServer = nullptr;
        return false;
    }
}

void ServerMode::stopImageServer() {
    QMutexLocker locker(&m_serverMutex);

    if (m_tcpServer) {
        qInfo() << "Stopping image server...";

        // 关闭所有连接
        for (auto it = m_pendingRequests.begin(); it != m_pendingRequests.end(); ++it) {
            it.key()->close();
        }
        m_pendingRequests.clear();

        m_tcpServer->close();
        delete m_tcpServer;
        m_tcpServer = nullptr;
        m_currentPort = 0;

        // 等待活跃请求完成
        int waitCount = 0;
        while (m_activeRequests.loadRelaxed() > 0 && waitCount < 30) {
            QThread::msleep(100);
            waitCount++;
        }

        m_activeRequests.storeRelaxed(0);
        qInfo() << "Image server stopped";
        emit serverStatusChanged();
        emit requestStatsChanged();
    }
}

QString ServerMode::serverUrl() const {
    if (!m_tcpServer || !m_tcpServer->isListening()) {
        return QString();
    }

    QString ip = m_ipv4Addresses.isEmpty() ? "localhost" : m_ipv4Addresses.first();
    return QString("http://%1:%2").arg(ip).arg(m_currentPort);
}

void ServerMode::addWatchFolder(const QString& folderPath) {
    if (!m_watchFolders.contains(folderPath)) {
        m_watchFolders.append(folderPath);
        qInfo() << "Added watch folder:" << folderPath;
    }
}



void ServerMode::removeWatchFolder(const QString& folderPath) {
    if (m_watchFolders.removeOne(folderPath)) {
        qInfo() << "Removed watch folder:" << folderPath;
    }
}

void ServerMode::onNewConnection() {
    while (m_tcpServer && m_tcpServer->hasPendingConnections()) {
        QTcpSocket* socket = m_tcpServer->nextPendingConnection();
        connect(socket, &QTcpSocket::readyRead, this, &ServerMode::onSocketReadyRead);
        connect(socket, &QTcpSocket::disconnected, this, &ServerMode::onSocketDisconnected);
        m_pendingRequests[socket] = QByteArray();
    }
}

void ServerMode::onSocketReadyRead() {
    QTcpSocket* socket = qobject_cast<QTcpSocket*>(sender());
    if (!socket) return;

    QByteArray data = socket->readAll();
    m_pendingRequests[socket].append(data);

    // 检查是否收到完整的HTTP请求
    QByteArray& requestData = m_pendingRequests[socket];
    if (requestData.contains("\r\n\r\n")) {
        processHttpRequest(socket, requestData);
        m_pendingRequests.remove(socket);
    }
}

void ServerMode::onSocketDisconnected() {
    QTcpSocket* socket = qobject_cast<QTcpSocket*>(sender());
    if (!socket) return;

    m_pendingRequests.remove(socket);
    socket->deleteLater();
}

void ServerMode::processHttpRequest(QTcpSocket* socket, const QByteArray& requestData) {
    m_activeRequests.fetchAndAddRelaxed(1);
    emit requestStatsChanged();

    // 安全检查：验证令牌
    if (!requestData.contains("authorization: Bearer imageviewer2025")) {
        qWarning() << "令牌验证失败，拒绝请求";
        qDebug() << "请求数据:" << requestData.left(500); // 只显示前500字符
        socket->write("HTTP/1.1 401 Unauthorized\r\nContent-Length: 0\r\n\r\n");
        socket->close();
        m_activeRequests.fetchAndSubRelaxed(1);
        emit requestStatsChanged();
        return;
    }
    qDebug() << "令牌验证通过";

    QString method = parseHttpMethod(requestData);
    QString path = parseHttpPath(requestData);
    QHash<QString, QString> headers = parseHttpHeaders(requestData);

    qDebug() << "HTTP Request:" << method << path;

    QByteArray response;

    if (method == "OPTIONS") {
        response = createHttpResponse(200, "text/plain", QByteArray(), {
            {"Access-Control-Allow-Origin", "*"},
            {"Access-Control-Allow-Methods", "GET, OPTIONS"},
            {"Access-Control-Allow-Headers", "Range, Content-Type"}
        });
    } else if (method == "GET") {
        if (path.startsWith("/api/images")) {
            QUrl url(path);
            QUrlQuery query(url);
            QString clientPath = query.queryItemValue("path");
            // URL解码客户端路径
            clientPath = QUrl::fromPercentEncoding(clientPath.toUtf8());
            qDebug() << "处理图片列表请求 - 原始路径:" << path << "解码后客户端路径:" << clientPath;
            response = handleImageList(clientPath, {});
        } else if (path.startsWith("/api/image/")) {
            QString clientPath = path.mid(11); // 移除 "/api/image/"
            clientPath = QUrl::fromPercentEncoding(clientPath.toUtf8());

            // 确保路径以 "/" 开头
            if (!clientPath.startsWith("/")) clientPath = "/" + clientPath;

            // 映射客户端路径到服务器路径
            QString serverPath = mapClientPathToServer(clientPath);

            response = handleImageData(serverPath, headers);
        } else if (path.startsWith("/api/thumbnail/")) {
            QString clientPath = path.mid(15); // 移除 "/api/thumbnail/"
            // 需要URL解码，因为浏览器会自动编码URL
            clientPath = QUrl::fromPercentEncoding(clientPath.toUtf8());

            qDebug() << "Client thumbnail path:" << clientPath;

            // 确保路径以 "/" 开头
            if (!clientPath.startsWith("/")) {
                clientPath = "/" + clientPath;
            }

            // 映射客户端路径到服务器路径
            QString serverPath = mapClientPathToServer(clientPath);
            qDebug() << "Mapped to server path:" << serverPath;

            response = handleThumbnail(serverPath);
        } else {
            response = createErrorResponse("Not Found", 404);
        }
    } else {
        response = createErrorResponse("Method Not Allowed", 405);
    }

    socket->write(response);
    socket->flush();
    socket->close();

    m_activeRequests.fetchAndSubRelaxed(1);
    emit requestStatsChanged();
}

QString ServerMode::parseHttpMethod(const QByteArray& requestData) {
    int spaceIndex = requestData.indexOf(' ');
    if (spaceIndex > 0) {
        return QString::fromLatin1(requestData.left(spaceIndex));
    }
    return QString();
}

QString ServerMode::parseHttpPath(const QByteArray& requestData) {
    int firstSpace = requestData.indexOf(' ');
    int secondSpace = requestData.indexOf(' ', firstSpace + 1);
    if (firstSpace > 0 && secondSpace > firstSpace) {
        return QString::fromLatin1(requestData.mid(firstSpace + 1, secondSpace - firstSpace - 1));
    }
    return QString();
}

QHash<QString, QString> ServerMode::parseHttpHeaders(const QByteArray& requestData) {
    QHash<QString, QString> headers;
    QStringList lines = QString::fromLatin1(requestData).split("\r\n");

    for (int i = 1; i < lines.size(); ++i) {
        const QString& line = lines[i];
        if (line.isEmpty()) break;

        int colonIndex = line.indexOf(':');
        if (colonIndex > 0) {
            QString key = line.left(colonIndex).trimmed().toLower();
            QString value = line.mid(colonIndex + 1).trimmed();
            headers[key] = value;
        }
    }

    return headers;
}

QByteArray ServerMode::createHttpResponse(int statusCode, const QString& contentType,
                                        const QByteArray& body, const QHash<QString, QString>& headers) {
    QByteArray response;
    QTextStream stream(&response);

    // 状态行
    stream << "HTTP/1.1 " << statusCode << " ";
    switch (statusCode) {
        case 200: stream << "OK"; break;
        case 206: stream << "Partial Content"; break;
        case 400: stream << "Bad Request"; break;
        case 404: stream << "Not Found"; break;
        case 405: stream << "Method Not Allowed"; break;
        case 500: stream << "Internal Server Error"; break;
        default: stream << "Unknown"; break;
    }
    stream << "\r\n";

    // 基本头部
    stream << "Content-Type: " << contentType << "\r\n";
    stream << "Content-Length: " << body.size() << "\r\n";
    stream << "Connection: close\r\n";

    // 自定义头部
    for (auto it = headers.begin(); it != headers.end(); ++it) {
        stream << it.key() << ": " << it.value() << "\r\n";
    }

    stream << "\r\n";
    stream.flush();

    response.append(body);
    return response;
}

QByteArray ServerMode::createErrorResponse(const QString &error, int statusCode) {
    QJsonObject errorObj;
    errorObj["error"] = error;
    errorObj["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);

    return createHttpResponse(statusCode, "application/json", QJsonDocument(errorObj).toJson(), {
        {"Access-Control-Allow-Origin", "*"}
    });
}

QByteArray ServerMode::handleImageList(const QString& clientPath, const QHash<QString, QString>& params) {
    Q_UNUSED(params)

    // 处理根路径请求 - 返回所有监听文件夹作为虚拟文件夹
    if (clientPath == "/" || clientPath.isEmpty()) {
        return handleRootFolderList();
    }

    // 映射客户端路径到服务器路径
    QString serverPath = mapClientPathToServer(clientPath);
    if (serverPath.isEmpty()) {
        return createErrorResponse("Invalid path mapping", 400);
    }

    // 验证文件夹路径
    if (!isValidFolderPath(serverPath)) {
        return createErrorResponse("Invalid folder path", 400);
    }

    // 安全检查：只允许访问监听文件夹内的路径
    if (!isPathInWatchFolders(serverPath)) {
        qWarning() << "Access denied for folder path outside watch folders:" << serverPath;
        return createErrorResponse("Access denied", 403);
    }

    QJsonObject result = m_imageProcessor->getNetworkImageList(serverPath);

    // 分离图片和文件夹，并映射路径
    QJsonArray images;
    QJsonArray folders;

    if (result.contains("images")) {
        QJsonArray allItems = result["images"].toArray();
        for (int i = 0; i < allItems.size(); ++i) {
            QJsonObject itemObj = allItems[i].toObject();
            QString serverPath = itemObj["path"].toString();
            itemObj["path"] = mapServerPathToClient(serverPath);

            if (itemObj["isFolder"].toBool()) {
                // 这是文件夹
                folders.append(itemObj);
            } else {
                // 这是图片文件
                images.append(itemObj);
            }
        }
    }

    // 重新构建结果
    QJsonObject finalResult;
    finalResult["images"] = images;
    finalResult["folders"] = folders;
    finalResult["currentPath"] = clientPath;
    finalResult["totalCount"] = images.size() + folders.size();
    finalResult["imageCount"] = images.size();
    finalResult["folderCount"] = folders.size();
    finalResult["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);

    return createHttpResponse(200, "application/json", QJsonDocument(finalResult).toJson(), {
        {"Access-Control-Allow-Origin", "*"}
    });
}

QByteArray ServerMode::handleRootFolderList() {
    qDebug() << "处理根文件夹列表请求，监听文件夹数量:" << m_watchFolders.size();
    for (const QString& folder : qAsConst(m_watchFolders)) {
        qDebug() << "监听文件夹:" << folder;
    }

    QJsonArray folders;

    // 为每个监听文件夹创建虚拟文件夹条目
    for (const QString& watchFolder : qAsConst(m_watchFolders)) {
        QFileInfo folderInfo(watchFolder);
        if (folderInfo.exists() && folderInfo.isDir()) {
            QJsonObject folderObj;
            folderObj["name"] = folderInfo.baseName();
            folderObj["path"] = "/" + folderInfo.baseName();
            folderObj["isFolder"] = true;
            folderObj["size"] = 0;
            folderObj["lastModified"] = folderInfo.lastModified().toString(Qt::ISODate);
            folders.append(folderObj);
        }
    }

    QJsonObject result;
    result["images"] = QJsonArray();
    result["folders"] = folders;
    result["currentPath"] = "/";
    result["totalCount"] = folders.size();
    result["imageCount"] = 0;
    result["folderCount"] = folders.size();
    result["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);

    // 如果没有监听文件夹，添加提示信息
    if (folders.isEmpty()) {
        result["message"] = "服务器未配置监听文件夹，请在设置中添加要共享的文件夹";
        qWarning() << "服务器没有配置监听文件夹";
    }

    qDebug() << "返回根文件夹列表，文件夹数量:" << folders.size();
    return createHttpResponse(200, "application/json", QJsonDocument(result).toJson(), {
        {"Access-Control-Allow-Origin", "*"}
    });
}

// 添加剩余的关键方法的简化实现
QByteArray ServerMode::handleImageData(const QString& imagePath, const QHash<QString, QString>& headers) {
    if (!m_imageProcessor) {
        return createErrorResponse("ImageProcessor not available", 500);
    }

    // 验证图片路径
    if (!isValidImagePath(imagePath)) {
        qWarning() << "Invalid image path:" << imagePath;
        return createErrorResponse("Invalid image path", 400);
    }

    // 安全检查：只允许访问监听文件夹内的路径
    if (!isPathInWatchFolders(imagePath)) {
        qWarning() << "Access denied for image path outside watch folders:" << imagePath;
        return createErrorResponse("Access denied", 403);
    }

    qDebug() << "获取完整图片:" << imagePath;

    // 获取完整图片数据
    QByteArray imageData = m_imageProcessor->getNetworkFullImage(imagePath);

    if (imageData.isEmpty()) {
        qWarning() << "Failed to load image:" << imagePath;
        return createErrorResponse("Failed to load image", 500);
    }

    // 确定MIME类型
    QString mimeType = "image/jpeg"; // 默认为JPEG
    QString lowerPath = imagePath.toLower();
    if (lowerPath.endsWith(".png")) {
        mimeType = "image/png";
    } else if (lowerPath.endsWith(".webp")) {
        mimeType = "image/webp";
    } else if (lowerPath.endsWith(".gif")) {
        mimeType = "image/gif";
    } else if (lowerPath.endsWith(".bmp")) {
        mimeType = "image/bmp";
    } else if (lowerPath.endsWith(".tiff") || lowerPath.endsWith(".tif")) {
        mimeType = "image/tiff";
    }

    qDebug() << "成功获取完整图片，大小:" << imageData.size() << "字节";

    // 处理Range请求（用于大文件分块传输）
    QHash<QString, QString> responseHeaders = {
        {"Access-Control-Allow-Origin", "*"},
        {"Cache-Control", "public, max-age=86400"}, // 缓存24小时
        {"Content-Length", QString::number(imageData.size())}
    };

    if (headers.contains("range")) {
        // TODO: 实现Range请求支持
        // 暂时返回完整文件
    }

    return createHttpResponse(200, mimeType, imageData, responseHeaders);
}

QByteArray ServerMode::handleThumbnail(const QString& imagePath) {
    if (!m_imageProcessor) {
        return createErrorResponse("ImageProcessor not available", 500);
    }

    // 验证图片路径
    if (!isValidImagePath(imagePath)) {
        qWarning() << "Invalid thumbnail path:" << imagePath;
        return createErrorResponse("Invalid image path", 400);
    }

    // 安全检查：只允许访问监听文件夹内的路径
    if (!isPathInWatchFolders(imagePath)) {
        qWarning() << "Access denied for thumbnail path outside watch folders:" << imagePath;
        return createErrorResponse("Access denied", 403);
    }

    qDebug() << "生成缩略图:" << imagePath;

    // 获取缩略图数据
    QByteArray thumbnailData = m_imageProcessor->getNetworkThumbnail(imagePath);

    if (thumbnailData.isEmpty()) {
        qWarning() << "Failed to generate thumbnail for:" << imagePath;
        return createErrorResponse("Failed to generate thumbnail", 500);
    }

    // 确定MIME类型
    QString mimeType = "image/jpeg"; // 默认为JPEG
    if (imagePath.toLower().endsWith(".png")) {
        mimeType = "image/png";
    } else if (imagePath.toLower().endsWith(".webp")) {
        mimeType = "image/webp";
    }

    qDebug() << "成功生成缩略图，大小:" << thumbnailData.size() << "字节";

    return createHttpResponse(200, mimeType, thumbnailData, {
        {"Access-Control-Allow-Origin", "*"},
        {"Cache-Control", "public, max-age=3600"} // 缓存1小时
    });
}

QString ServerMode::extractPathFromUrl(const QString &urlPath, const QString &prefix) {
    if (urlPath.startsWith(prefix)) {
        return urlPath.mid(prefix.length());
    }
    return QString();
}

bool ServerMode::isValidImagePath(const QString &path) {
    return !path.isEmpty() && QFileInfo::exists(path);
}

bool ServerMode::isValidFolderPath(const QString &path) {
    QFileInfo info(path);
    return info.exists() && info.isDir();
}

bool ServerMode::isPathInWatchFolders(const QString &path) {
    for (const QString& watchFolder : qAsConst(m_watchFolders)) {
        if (path.startsWith(watchFolder)) {
            return true;
        }
    }
    return false;
}

QString ServerMode::mapClientPathToServer(const QString &clientPath) {
    // 简化的路径映射实现
    if (clientPath.startsWith("/")) {
        QStringList parts = clientPath.split('/', Qt::SkipEmptyParts);
        if (!parts.isEmpty()) {
            QString folderName = parts.first();
            for (const QString& watchFolder : qAsConst(m_watchFolders)) {
                QFileInfo info(watchFolder);
                if (info.baseName() == folderName) {
                    QString remainingPath = parts.size() > 1 ? "/" + QStringList(parts.mid(1)).join("/") : "";
                    return watchFolder + remainingPath;
                }
            }
        }
    }
    return QString();
}

QString ServerMode::mapServerPathToClient(const QString &serverPath) {
    // 简化的路径映射实现
    for (const QString& watchFolder : qAsConst(m_watchFolders)) {
        if (serverPath.startsWith(watchFolder)) {
            QFileInfo info(watchFolder);
            QString relativePath = serverPath.mid(watchFolder.length());
            return "/" + info.baseName() + relativePath;
        }
    }
    return serverPath;
}

void ServerMode::handleRetryRequest(const RetryRequest& request) {
    Q_UNUSED(request)
    // 简化实现，暂不处理重试
}

void ServerMode::processRetryQueue() {
    // 简化实现，暂不处理重试队列
}

// 兼容性方法实现
void ServerMode::listen(int port) {
    startImageServer(port);
}

void ServerMode::onClientConnected(std::function<void(ConnectionID)> handler) {
    m_clientConnectedHandler = handler;
}

void ServerMode::closeSession(ConnectionID id) {
    Q_UNUSED(id)
    // TODO: 实现会话关闭逻辑
}

std::vector<std::string> ServerMode::getLocalAddresses() {
    std::vector<std::string> addresses;
    for (const QString& addr : QStringList() << "127.0.0.1" << "localhost") {
        addresses.push_back(addr.toStdString());
    }
    return addresses;
}

void ServerMode::setAuthToken(const QString& token) {
    if (m_authToken != token) {
        m_authToken = token;
        emit authTokenChanged();
        qDebug() << "Auth token updated:" << token;
    }
}

