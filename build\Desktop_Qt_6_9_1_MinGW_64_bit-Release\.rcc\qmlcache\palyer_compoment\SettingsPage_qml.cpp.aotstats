[{"moduleFiles": [{"entries": [{"codegenSuccessful": true, "column": 12, "durationMicroseconds": 289, "errorMessage": "", "functionName": "flags", "line": 10}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 255, "errorMessage": "function without return type annotation returns conversion to QString. This may prevent proper compilation to Cpp.", "functionName": "generateRandomToken", "line": 27}, {"codegenSuccessful": false, "column": 5, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "showStatusMessage", "line": 1098}, {"codegenSuccessful": true, "column": 73, "durationMicroseconds": 45, "errorMessage": "", "functionName": "type", "line": 12}, {"codegenSuccessful": true, "column": 16, "durationMicroseconds": 84, "errorMessage": "", "functionName": "width", "line": 16}, {"codegenSuccessful": false, "column": 16, "durationMicroseconds": 133, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 17}, {"codegenSuccessful": true, "column": 54, "durationMicroseconds": 72, "errorMessage": "", "functionName": "visible", "line": 17}, {"codegenSuccessful": true, "column": 62, "durationMicroseconds": 28, "errorMessage": "", "functionName": "centerIn", "line": 19}, {"codegenSuccessful": true, "column": 59, "durationMicroseconds": 81, "errorMessage": "", "functionName": "onTriggered", "line": 20}, {"codegenSuccessful": true, "column": 23, "durationMicroseconds": 29, "errorMessage": "", "functionName": "fill", "line": 36}, {"codegenSuccessful": true, "column": 29, "durationMicroseconds": 62, "errorMessage": "", "functionName": "left", "line": 43}, {"codegenSuccessful": true, "column": 47, "durationMicroseconds": 44, "errorMessage": "", "functionName": "top", "line": 43}, {"codegenSuccessful": true, "column": 66, "durationMicroseconds": 42, "errorMessage": "", "functionName": "right", "line": 43}, {"codegenSuccessful": true, "column": 28, "durationMicroseconds": 62, "errorMessage": "", "functionName": "onPressed", "line": 47}, {"codegenSuccessful": true, "column": 31, "durationMicroseconds": 49, "errorMessage": "", "functionName": "fill", "line": 46}, {"codegenSuccessful": true, "column": 31, "durationMicroseconds": 53, "errorMessage": "", "functionName": "left", "line": 50}, {"codegenSuccessful": true, "column": 41, "durationMicroseconds": 42, "errorMessage": "", "functionName": "verticalCenter", "line": 52}, {"codegenSuccessful": true, "column": 52, "durationMicroseconds": 59, "errorMessage": "", "functionName": "radius", "line": 56}, {"codegenSuccessful": true, "column": 87, "durationMicroseconds": 59, "errorMessage": "", "functionName": "type", "line": 57}, {"codegenSuccessful": true, "column": 89, "durationMicroseconds": 34, "errorMessage": "", "functionName": "type", "line": 58}, {"codegenSuccessful": true, "column": 74, "durationMicroseconds": 49, "errorMessage": "", "functionName": "visible", "line": 61}, {"codegenSuccessful": true, "column": 84, "durationMicroseconds": 25, "errorMessage": "", "functionName": "centerIn", "line": 60}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 102, "errorMessage": "", "functionName": "onClicked", "line": 66}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 104, "errorMessage": "", "functionName": "onEntered", "line": 67}, {"codegenSuccessful": true, "column": 35, "durationMicroseconds": 74, "errorMessage": "", "functionName": "onExited", "line": 68}, {"codegenSuccessful": true, "column": 74, "durationMicroseconds": 27, "errorMessage": "", "functionName": "fill", "line": 65}, {"codegenSuccessful": false, "column": 38, "durationMicroseconds": 72, "errorMessage": "method closingset cannot be resolved.", "functionName": "onTriggered", "line": 72}, {"codegenSuccessful": true, "column": 52, "durationMicroseconds": 82, "errorMessage": "", "functionName": "radius", "line": 78}, {"codegenSuccessful": true, "column": 87, "durationMicroseconds": 44, "errorMessage": "", "functionName": "type", "line": 79}, {"codegenSuccessful": true, "column": 89, "durationMicroseconds": 45, "errorMessage": "", "functionName": "type", "line": 80}, {"codegenSuccessful": true, "column": 34, "durationMicroseconds": 38, "errorMessage": "", "functionName": "visible", "line": 84}, {"codegenSuccessful": true, "column": 84, "durationMicroseconds": 23, "errorMessage": "", "functionName": "centerIn", "line": 82}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 18, "errorMessage": "", "functionName": "onClicked", "line": 89}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 80, "errorMessage": "", "functionName": "onEntered", "line": 90}, {"codegenSuccessful": true, "column": 35, "durationMicroseconds": 69, "errorMessage": "", "functionName": "onExited", "line": 91}, {"codegenSuccessful": true, "column": 56, "durationMicroseconds": 52, "errorMessage": "", "functionName": "fill", "line": 88}, {"codegenSuccessful": true, "column": 52, "durationMicroseconds": 42, "errorMessage": "", "functionName": "radius", "line": 97}, {"codegenSuccessful": true, "column": 87, "durationMicroseconds": 33, "errorMessage": "", "functionName": "type", "line": 98}, {"codegenSuccessful": true, "column": 89, "durationMicroseconds": 30, "errorMessage": "", "functionName": "type", "line": 99}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 71, "errorMessage": "", "functionName": "onEntered", "line": 102}, {"codegenSuccessful": true, "column": 35, "durationMicroseconds": 71, "errorMessage": "", "functionName": "onExited", "line": 103}, {"codegenSuccessful": true, "column": 79, "durationMicroseconds": 23, "errorMessage": "", "functionName": "fill", "line": 101}, {"codegenSuccessful": true, "column": 28, "durationMicroseconds": 50, "errorMessage": "", "functionName": "top", "line": 110}, {"codegenSuccessful": true, "column": 51, "durationMicroseconds": 105, "errorMessage": "", "functionName": "left", "line": 110}, {"codegenSuccessful": true, "column": 71, "durationMicroseconds": 40, "errorMessage": "", "functionName": "right", "line": 110}, {"codegenSuccessful": true, "column": 93, "durationMicroseconds": 38, "errorMessage": "", "functionName": "bottom", "line": 110}, {"codegenSuccessful": true, "column": 37, "durationMicroseconds": 30, "errorMessage": "", "functionName": "height", "line": 114}, {"codegenSuccessful": true, "column": 37, "durationMicroseconds": 22, "errorMessage": "", "functionName": "fill", "line": 116}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 29, "errorMessage": "", "functionName": "width", "line": 120}, {"codegenSuccessful": true, "column": 47, "durationMicroseconds": 21, "errorMessage": "", "functionName": "centerIn", "line": 123}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 31, "errorMessage": "", "functionName": "width", "line": 130}, {"codegenSuccessful": true, "column": 54, "durationMicroseconds": 81, "errorMessage": "", "functionName": "height", "line": 130}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 39, "errorMessage": "", "functionName": "model", "line": 131}, {"codegenSuccessful": true, "column": 53, "durationMicroseconds": 28, "errorMessage": "", "functionName": "delegate", "line": 131}, {"codegenSuccessful": true, "column": 40, "durationMicroseconds": 77, "errorMessage": "", "functionName": "width", "line": 135}, {"codegenSuccessful": false, "column": 40, "durationMicroseconds": 85, "errorMessage": "Cannot access value for name model", "functionName": "color", "line": 136}, {"codegenSuccessful": false, "column": 48, "durationMicroseconds": 796, "errorMessage": "Cannot access value for name menuModel", "functionName": "onClicked", "line": 140}, {"codegenSuccessful": false, "column": 48, "durationMicroseconds": 11, "errorMessage": "Type (component in C:/Qt/file/palyer/compoment/SettingsPage.qml)::parent with type QQuickItem does not have a property hovered for writing", "functionName": "onEntered", "line": 146}, {"codegenSuccessful": false, "column": 47, "durationMicroseconds": 7, "errorMessage": "Type (component in C:/Qt/file/palyer/compoment/SettingsPage.qml)::parent with type QQuickItem does not have a property hovered for writing", "functionName": "onExited", "line": 147}, {"codegenSuccessful": true, "column": 51, "durationMicroseconds": 80, "errorMessage": "", "functionName": "fill", "line": 139}, {"codegenSuccessful": true, "column": 53, "durationMicroseconds": 55, "errorMessage": "", "functionName": "left", "line": 151}, {"codegenSuccessful": true, "column": 98, "durationMicroseconds": 46, "errorMessage": "", "functionName": "verticalCenter", "line": 151}, {"codegenSuccessful": false, "column": 47, "durationMicroseconds": 22, "errorMessage": "Cannot access value for name model", "functionName": "text", "line": 154}, {"codegenSuccessful": false, "column": 48, "durationMicroseconds": 28, "errorMessage": "Cannot access value for name model", "functionName": "color", "line": 155}, {"codegenSuccessful": true, "column": 65, "durationMicroseconds": 42, "errorMessage": "", "functionName": "verticalCenter", "line": 156}, {"codegenSuccessful": false, "column": 47, "durationMicroseconds": 16, "errorMessage": "Cannot access value for name model", "functionName": "text", "line": 159}, {"codegenSuccessful": false, "column": 48, "durationMicroseconds": 25, "errorMessage": "Cannot access value for name model", "functionName": "color", "line": 160}, {"codegenSuccessful": true, "column": 65, "durationMicroseconds": 40, "errorMessage": "", "functionName": "verticalCenter", "line": 161}, {"codegenSuccessful": true, "column": 33, "durationMicroseconds": 46, "errorMessage": "", "functionName": "left", "line": 172}, {"codegenSuccessful": true, "column": 59, "durationMicroseconds": 38, "errorMessage": "", "functionName": "right", "line": 172}, {"codegenSuccessful": true, "column": 78, "durationMicroseconds": 41, "errorMessage": "", "functionName": "top", "line": 172}, {"codegenSuccessful": true, "column": 98, "durationMicroseconds": 65, "errorMessage": "", "functionName": "bottom", "line": 172}, {"codegenSuccessful": true, "column": 35, "durationMicroseconds": 23, "errorMessage": "", "functionName": "contentWidth", "line": 178}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 34, "errorMessage": "", "functionName": "contentHeight", "line": 179}, {"codegenSuccessful": true, "column": 35, "durationMicroseconds": 93, "errorMessage": "", "functionName": "fill", "line": 177}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 34, "errorMessage": "", "functionName": "width", "line": 183}, {"codegenSuccessful": true, "column": 41, "durationMicroseconds": 45, "errorMessage": "", "functionName": "left", "line": 184}, {"codegenSuccessful": true, "column": 61, "durationMicroseconds": 48, "errorMessage": "", "functionName": "right", "line": 184}, {"codegenSuccessful": true, "column": 80, "durationMicroseconds": 140, "errorMessage": "", "functionName": "top", "line": 184}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 36, "errorMessage": "", "functionName": "width", "line": 191}, {"codegenSuccessful": true, "column": 40, "durationMicroseconds": 91, "errorMessage": "", "functionName": "width", "line": 196}, {"codegenSuccessful": false, "column": 50, "durationMicroseconds": 1599, "errorMessage": "Cannot generate efficient code for lookup in QJSValue", "functionName": "sourceComponent", "line": 197}, {"codegenSuccessful": true, "column": 33, "durationMicroseconds": 53, "errorMessage": "", "functionName": "policy", "line": 226}, {"codegenSuccessful": true, "column": 34, "durationMicroseconds": 207, "errorMessage": "", "functionName": "visible", "line": 227}, {"codegenSuccessful": true, "column": 34, "durationMicroseconds": 188, "errorMessage": "", "functionName": "opacity", "line": 228}, {"codegenSuccessful": true, "column": 93, "durationMicroseconds": 35, "errorMessage": "", "functionName": "type", "line": 229}, {"codegenSuccessful": true, "column": 55, "durationMicroseconds": 47, "errorMessage": "", "functionName": "radius", "line": 231}, {"codegenSuccessful": false, "column": 36, "durationMicroseconds": 66, "errorMessage": "Cannot load property pressed from (component in C:/Qt/file/palyer/compoment/SettingsPage.qml)::parent with type QQuickItem.", "functionName": "color", "line": 232}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 43, "errorMessage": "", "functionName": "width", "line": 241}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 29, "errorMessage": "", "functionName": "width", "line": 244}, {"codegenSuccessful": true, "column": 45, "durationMicroseconds": 65, "errorMessage": "", "functionName": "implicitHeight", "line": 245}, {"codegenSuccessful": true, "column": 47, "durationMicroseconds": 45, "errorMessage": "", "functionName": "left", "line": 250}, {"codegenSuccessful": true, "column": 48, "durationMicroseconds": 40, "errorMessage": "", "functionName": "right", "line": 251}, {"codegenSuccessful": true, "column": 46, "durationMicroseconds": 39, "errorMessage": "", "functionName": "top", "line": 252}, {"codegenSuccessful": true, "column": 49, "durationMicroseconds": 39, "errorMessage": "", "functionName": "bottom", "line": 253}, {"codegenSuccessful": true, "column": 44, "durationMicroseconds": 29, "errorMessage": "", "functionName": "width", "line": 259}, {"codegenSuccessful": true, "column": 48, "durationMicroseconds": 29, "errorMessage": "", "functionName": "width", "line": 263}, {"codegenSuccessful": true, "column": 52, "durationMicroseconds": 52, "errorMessage": "", "functionName": "width", "line": 265}, {"codegenSuccessful": true, "column": 68, "durationMicroseconds": 42, "errorMessage": "", "functionName": "verticalAlignment", "line": 272}, {"codegenSuccessful": true, "column": 63, "durationMicroseconds": 22, "errorMessage": "", "functionName": "fill", "line": 269}, {"codegenSuccessful": true, "column": 86, "durationMicroseconds": 156, "errorMessage": "", "functionName": "color", "line": 276}, {"codegenSuccessful": true, "column": 79, "durationMicroseconds": 25, "errorMessage": "", "functionName": "centerIn", "line": 279}, {"codegenSuccessful": false, "column": 60, "durationMicroseconds": 31, "errorMessage": "Cannot access value for name cacheFolderDialog", "functionName": "onClicked", "line": 284}, {"codegenSuccessful": true, "column": 82, "durationMicroseconds": 21, "errorMessage": "", "functionName": "fill", "line": 283}, {"codegenSuccessful": true, "column": 44, "durationMicroseconds": 31, "errorMessage": "", "functionName": "width", "line": 292}, {"codegenSuccessful": false, "column": 52, "durationMicroseconds": 44, "errorMessage": "Cannot access value for name cacheCleanMode", "functionName": "color", "line": 298}, {"codegenSuccessful": false, "column": 61, "durationMicroseconds": 29, "errorMessage": "Cannot access value for name cacheCleanMode", "functionName": "color", "line": 299}, {"codegenSuccessful": false, "column": 56, "durationMicroseconds": 30, "errorMessage": "Cannot access value for name cacheCleanMode", "functionName": "color", "line": 302}, {"codegenSuccessful": true, "column": 86, "durationMicroseconds": 29, "errorMessage": "", "functionName": "centerIn", "line": 301}, {"codegenSuccessful": false, "column": 60, "durationMicroseconds": 19, "errorMessage": "Cannot find name cacheCleanMode", "functionName": "onClicked", "line": 306}, {"codegenSuccessful": true, "column": 85, "durationMicroseconds": 20, "errorMessage": "", "functionName": "fill", "line": 305}, {"codegenSuccessful": false, "column": 52, "durationMicroseconds": 40, "errorMessage": "Cannot access value for name cacheCleanMode", "functionName": "color", "line": 311}, {"codegenSuccessful": false, "column": 61, "durationMicroseconds": 27, "errorMessage": "Cannot access value for name cacheCleanMode", "functionName": "color", "line": 312}, {"codegenSuccessful": false, "column": 56, "durationMicroseconds": 28, "errorMessage": "Cannot access value for name cacheCleanMode", "functionName": "color", "line": 315}, {"codegenSuccessful": true, "column": 83, "durationMicroseconds": 21, "errorMessage": "", "functionName": "centerIn", "line": 314}, {"codegenSuccessful": false, "column": 60, "durationMicroseconds": 18, "errorMessage": "Cannot find name cacheCleanMode", "functionName": "onClicked", "line": 319}, {"codegenSuccessful": true, "column": 82, "durationMicroseconds": 20, "errorMessage": "", "functionName": "fill", "line": 318}, {"codegenSuccessful": true, "column": 86, "durationMicroseconds": 129, "errorMessage": "", "functionName": "color", "line": 324}, {"codegenSuccessful": true, "column": 81, "durationMicroseconds": 20, "errorMessage": "", "functionName": "centerIn", "line": 327}, {"codegenSuccessful": false, "column": 60, "durationMicroseconds": 24, "errorMessage": "Cannot access value for name clearCacheDialog", "functionName": "onClicked", "line": 332}, {"codegenSuccessful": true, "column": 81, "durationMicroseconds": 21, "errorMessage": "", "functionName": "fill", "line": 331}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 28, "errorMessage": "", "functionName": "width", "line": 348}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 27, "errorMessage": "", "functionName": "width", "line": 352}, {"codegenSuccessful": true, "column": 45, "durationMicroseconds": 95, "errorMessage": "", "functionName": "implicitHeight", "line": 353}, {"codegenSuccessful": true, "column": 47, "durationMicroseconds": 47, "errorMessage": "", "functionName": "left", "line": 358}, {"codegenSuccessful": true, "column": 48, "durationMicroseconds": 84, "errorMessage": "", "functionName": "right", "line": 360}, {"codegenSuccessful": true, "column": 46, "durationMicroseconds": 39, "errorMessage": "", "functionName": "top", "line": 362}, {"codegenSuccessful": true, "column": 49, "durationMicroseconds": 37, "errorMessage": "", "functionName": "bottom", "line": 364}, {"codegenSuccessful": true, "column": 66, "durationMicroseconds": 27, "errorMessage": "", "functionName": "height", "line": 373}, {"codegenSuccessful": true, "column": 99, "durationMicroseconds": 30, "errorMessage": "", "functionName": "verticalAlignment", "line": 373}, {"codegenSuccessful": false, "column": 48, "durationMicroseconds": 214, "errorMessage": "Cannot access value for name serverMode", "functionName": "model", "line": 376}, {"codegenSuccessful": true, "column": 60, "durationMicroseconds": 56, "errorMessage": "", "functionName": "implicitWidth", "line": 381}, {"codegenSuccessful": false, "column": 55, "durationMicroseconds": 191, "errorMessage": "Cannot access value for name modelData", "functionName": "text", "line": 385}, {"codegenSuccessful": true, "column": 67, "durationMicroseconds": 24, "errorMessage": "", "functionName": "centerIn", "line": 384}, {"codegenSuccessful": true, "column": 88, "durationMicroseconds": 52, "errorMessage": "", "functionName": "acceptedButtons", "line": 391}, {"codegenSuccessful": false, "column": 60, "durationMicroseconds": 383, "errorMessage": "Cannot access value for name textEdit", "functionName": "onClicked", "line": 392}, {"codegenSuccessful": true, "column": 60, "durationMicroseconds": 20, "errorMessage": "", "functionName": "onClicked", "line": 392}, {"codegenSuccessful": true, "column": 63, "durationMicroseconds": 106, "errorMessage": "", "functionName": "fill", "line": 391}, {"codegenSuccessful": true, "column": 66, "durationMicroseconds": 33, "errorMessage": "", "functionName": "height", "line": 410}, {"codegenSuccessful": true, "column": 99, "durationMicroseconds": 32, "errorMessage": "", "functionName": "verticalAlignment", "line": 410}, {"codegenSuccessful": false, "column": 48, "durationMicroseconds": 279, "errorMessage": "Cannot access value for name serverMode", "functionName": "model", "line": 413}, {"codegenSuccessful": true, "column": 60, "durationMicroseconds": 61, "errorMessage": "", "functionName": "implicitWidth", "line": 418}, {"codegenSuccessful": false, "column": 55, "durationMicroseconds": 179, "errorMessage": "Cannot access value for name modelData", "functionName": "text", "line": 422}, {"codegenSuccessful": true, "column": 67, "durationMicroseconds": 24, "errorMessage": "", "functionName": "centerIn", "line": 421}, {"codegenSuccessful": true, "column": 88, "durationMicroseconds": 83, "errorMessage": "", "functionName": "acceptedButtons", "line": 428}, {"codegenSuccessful": false, "column": 60, "durationMicroseconds": 431, "errorMessage": "Cannot access value for name textEdit", "functionName": "onClicked", "line": 429}, {"codegenSuccessful": true, "column": 60, "durationMicroseconds": 20, "errorMessage": "", "functionName": "onClicked", "line": 429}, {"codegenSuccessful": true, "column": 63, "durationMicroseconds": 23, "errorMessage": "", "functionName": "fill", "line": 428}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 30, "errorMessage": "", "functionName": "width", "line": 447}, {"codegenSuccessful": true, "column": 45, "durationMicroseconds": 54, "errorMessage": "", "functionName": "implicitHeight", "line": 448}, {"codegenSuccessful": true, "column": 47, "durationMicroseconds": 67, "errorMessage": "", "functionName": "left", "line": 455}, {"codegenSuccessful": true, "column": 48, "durationMicroseconds": 39, "errorMessage": "", "functionName": "right", "line": 457}, {"codegenSuccessful": true, "column": 46, "durationMicroseconds": 40, "errorMessage": "", "functionName": "top", "line": 459}, {"codegenSuccessful": true, "column": 49, "durationMicroseconds": 38, "errorMessage": "", "functionName": "bottom", "line": 461}, {"codegenSuccessful": true, "column": 65, "durationMicroseconds": 43, "errorMessage": "", "functionName": "verticalCenter", "line": 469}, {"codegenSuccessful": true, "column": 65, "durationMicroseconds": 39, "errorMessage": "", "functionName": "verticalCenter", "line": 473}, {"codegenSuccessful": true, "column": 63, "durationMicroseconds": 20, "errorMessage": "", "functionName": "centerIn", "line": 475}, {"codegenSuccessful": false, "column": 56, "durationMicroseconds": 418, "errorMessage": "Cannot access value for name serverMode", "functionName": "onClicked", "line": 481}, {"codegenSuccessful": true, "column": 59, "durationMicroseconds": 22, "errorMessage": "", "functionName": "fill", "line": 480}, {"codegenSuccessful": true, "column": 65, "durationMicroseconds": 51, "errorMessage": "", "functionName": "verticalCenter", "line": 496}, {"codegenSuccessful": true, "column": 63, "durationMicroseconds": 20, "errorMessage": "", "functionName": "centerIn", "line": 498}, {"codegenSuccessful": false, "column": 56, "durationMicroseconds": 126, "errorMessage": "Cannot access value for name serverMode", "functionName": "onClicked", "line": 504}, {"codegenSuccessful": true, "column": 59, "durationMicroseconds": 20, "errorMessage": "", "functionName": "fill", "line": 503}, {"codegenSuccessful": true, "column": 83, "durationMicroseconds": 39, "errorMessage": "", "functionName": "verticalCenter", "line": 518}, {"codegenSuccessful": false, "column": 48, "durationMicroseconds": 103, "errorMessage": "Cannot access value for name serverMode", "functionName": "color", "line": 522}, {"codegenSuccessful": true, "column": 65, "durationMicroseconds": 40, "errorMessage": "", "functionName": "verticalCenter", "line": 523}, {"codegenSuccessful": false, "column": 47, "durationMicroseconds": 291, "errorMessage": "Cannot access value for name serverMode", "functionName": "text", "line": 526}, {"codegenSuccessful": false, "column": 48, "durationMicroseconds": 144, "errorMessage": "Cannot access value for name serverMode", "functionName": "color", "line": 529}, {"codegenSuccessful": true, "column": 65, "durationMicroseconds": 43, "errorMessage": "", "functionName": "verticalCenter", "line": 530}, {"codegenSuccessful": true, "column": 83, "durationMicroseconds": 40, "errorMessage": "", "functionName": "verticalCenter", "line": 538}, {"codegenSuccessful": true, "column": 66, "durationMicroseconds": 36, "errorMessage": "", "functionName": "horizontalAlignment", "line": 549}, {"codegenSuccessful": true, "column": 104, "durationMicroseconds": 30, "errorMessage": "", "functionName": "verticalAlignment", "line": 549}, {"codegenSuccessful": true, "column": 59, "durationMicroseconds": 20, "errorMessage": "", "functionName": "fill", "line": 545}, {"codegenSuccessful": true, "column": 36, "durationMicroseconds": 28, "errorMessage": "", "functionName": "width", "line": 557}, {"codegenSuccessful": true, "column": 37, "durationMicroseconds": 57, "errorMessage": "", "functionName": "height", "line": 558}, {"codegenSuccessful": true, "column": 47, "durationMicroseconds": 40, "errorMessage": "", "functionName": "left", "line": 566}, {"codegenSuccessful": true, "column": 46, "durationMicroseconds": 38, "errorMessage": "", "functionName": "top", "line": 568}, {"codegenSuccessful": true, "column": 48, "durationMicroseconds": 38, "errorMessage": "", "functionName": "right", "line": 570}, {"codegenSuccessful": true, "column": 44, "durationMicroseconds": 27, "errorMessage": "", "functionName": "width", "line": 575}, {"codegenSuccessful": true, "column": 65, "durationMicroseconds": 89, "errorMessage": "", "functionName": "verticalCenter", "line": 581}, {"codegenSuccessful": true, "column": 65, "durationMicroseconds": 41, "errorMessage": "", "functionName": "verticalCenter", "line": 586}, {"codegenSuccessful": true, "column": 63, "durationMicroseconds": 20, "errorMessage": "", "functionName": "centerIn", "line": 589}, {"codegenSuccessful": false, "column": 56, "durationMicroseconds": 25, "errorMessage": "Cannot access value for name folderDialog", "functionName": "onClicked", "line": 596}, {"codegenSuccessful": true, "column": 59, "durationMicroseconds": 19, "errorMessage": "", "functionName": "fill", "line": 595}, {"codegenSuccessful": true, "column": 44, "durationMicroseconds": 28, "errorMessage": "", "functionName": "width", "line": 605}, {"codegenSuccessful": true, "column": 66, "durationMicroseconds": 84, "errorMessage": "", "functionName": "height", "line": 605}, {"codegenSuccessful": false, "column": 44, "durationMicroseconds": 17, "errorMessage": "Cannot access value for name folderListModel", "functionName": "model", "line": 606}, {"codegenSuccessful": false, "column": 48, "durationMicroseconds": 18, "errorMessage": "Cannot access value for name folderListView", "functionName": "width", "line": 609}, {"codegenSuccessful": true, "column": 52, "durationMicroseconds": 52, "errorMessage": "", "functionName": "width", "line": 618}, {"codegenSuccessful": true, "column": 59, "durationMicroseconds": 39, "errorMessage": "", "functionName": "left", "line": 614}, {"codegenSuccessful": true, "column": 69, "durationMicroseconds": 38, "errorMessage": "", "functionName": "verticalCenter", "line": 616}, {"codegenSuccessful": true, "column": 73, "durationMicroseconds": 37, "errorMessage": "", "functionName": "verticalCenter", "line": 622}, {"codegenSuccessful": false, "column": 55, "durationMicroseconds": 23, "errorMessage": "Cannot access value for name model", "functionName": "text", "line": 626}, {"codegenSuccessful": true, "column": 56, "durationMicroseconds": 29, "errorMessage": "", "functionName": "elide", "line": 628}, {"codegenSuccessful": true, "column": 81, "durationMicroseconds": 65, "errorMessage": "", "functionName": "width", "line": 628}, {"codegenSuccessful": true, "column": 73, "durationMicroseconds": 39, "errorMessage": "", "functionName": "verticalCenter", "line": 627}, {"codegenSuccessful": true, "column": 62, "durationMicroseconds": 38, "errorMessage": "", "functionName": "right", "line": 633}, {"codegenSuccessful": true, "column": 109, "durationMicroseconds": 37, "errorMessage": "", "functionName": "verticalCenter", "line": 633}, {"codegenSuccessful": false, "column": 60, "durationMicroseconds": 330, "errorMessage": "Cannot access value for name folderListModel", "functionName": "onClicked", "line": 638}, {"codegenSuccessful": true, "column": 63, "durationMicroseconds": 21, "errorMessage": "", "functionName": "fill", "line": 637}, {"codegenSuccessful": true, "column": 44, "durationMicroseconds": 62, "errorMessage": "", "functionName": "width", "line": 653}, {"codegenSuccessful": false, "column": 46, "durationMicroseconds": 19, "errorMessage": "Cannot access value for name folderListModel", "functionName": "visible", "line": 656}, {"codegenSuccessful": true, "column": 59, "durationMicroseconds": 29, "errorMessage": "", "functionName": "centerIn", "line": 659}, {"codegenSuccessful": true, "column": 69, "durationMicroseconds": 41, "errorMessage": "", "functionName": "verticalCenter", "line": 664}, {"codegenSuccessful": true, "column": 69, "durationMicroseconds": 38, "errorMessage": "", "functionName": "verticalCenter", "line": 670}, {"codegenSuccessful": true, "column": 28, "durationMicroseconds": 129, "errorMessage": "", "functionName": "width", "line": 686}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 44, "errorMessage": "", "functionName": "width", "line": 690}, {"codegenSuccessful": true, "column": 33, "durationMicroseconds": 68, "errorMessage": "", "functionName": "height", "line": 691}, {"codegenSuccessful": true, "column": 43, "durationMicroseconds": 43, "errorMessage": "", "functionName": "left", "line": 699}, {"codegenSuccessful": true, "column": 42, "durationMicroseconds": 39, "errorMessage": "", "functionName": "top", "line": 701}, {"codegenSuccessful": true, "column": 44, "durationMicroseconds": 39, "errorMessage": "", "functionName": "right", "line": 703}, {"codegenSuccessful": true, "column": 40, "durationMicroseconds": 28, "errorMessage": "", "functionName": "width", "line": 708}, {"codegenSuccessful": true, "column": 61, "durationMicroseconds": 41, "errorMessage": "", "functionName": "verticalCenter", "line": 713}, {"codegenSuccessful": true, "column": 61, "durationMicroseconds": 83, "errorMessage": "", "functionName": "verticalCenter", "line": 717}, {"codegenSuccessful": true, "column": 59, "durationMicroseconds": 23, "errorMessage": "", "functionName": "centerIn", "line": 719}, {"codegenSuccessful": false, "column": 52, "durationMicroseconds": 1119, "errorMessage": "Cannot access value for name clientMode", "functionName": "onClicked", "line": 725}, {"codegenSuccessful": true, "column": 55, "durationMicroseconds": 34, "errorMessage": "", "functionName": "fill", "line": 724}, {"codegenSuccessful": true, "column": 61, "durationMicroseconds": 45, "errorMessage": "", "functionName": "verticalCenter", "line": 756}, {"codegenSuccessful": true, "column": 59, "durationMicroseconds": 22, "errorMessage": "", "functionName": "centerIn", "line": 758}, {"codegenSuccessful": true, "column": 52, "durationMicroseconds": 16, "errorMessage": "", "functionName": "onClicked", "line": 764}, {"codegenSuccessful": true, "column": 55, "durationMicroseconds": 22, "errorMessage": "", "functionName": "fill", "line": 763}, {"codegenSuccessful": true, "column": 79, "durationMicroseconds": 47, "errorMessage": "", "functionName": "verticalCenter", "line": 774}, {"codegenSuccessful": true, "column": 62, "durationMicroseconds": 38, "errorMessage": "", "functionName": "horizontalAlignment", "line": 785}, {"codegenSuccessful": true, "column": 100, "durationMicroseconds": 30, "errorMessage": "", "functionName": "verticalAlignment", "line": 785}, {"codegenSuccessful": true, "column": 55, "durationMicroseconds": 20, "errorMessage": "", "functionName": "fill", "line": 782}, {"codegenSuccessful": true, "column": 50, "durationMicroseconds": 61, "errorMessage": "", "functionName": "visible", "line": 791}, {"codegenSuccessful": true, "column": 59, "durationMicroseconds": 20, "errorMessage": "", "functionName": "centerIn", "line": 790}, {"codegenSuccessful": true, "column": 61, "durationMicroseconds": 40, "errorMessage": "", "functionName": "verticalCenter", "line": 800}, {"codegenSuccessful": true, "column": 62, "durationMicroseconds": 29, "errorMessage": "", "functionName": "horizontalAlignment", "line": 812}, {"codegenSuccessful": true, "column": 100, "durationMicroseconds": 27, "errorMessage": "", "functionName": "verticalAlignment", "line": 812}, {"codegenSuccessful": true, "column": 55, "durationMicroseconds": 21, "errorMessage": "", "functionName": "fill", "line": 808}, {"codegenSuccessful": true, "column": 79, "durationMicroseconds": 39, "errorMessage": "", "functionName": "verticalCenter", "line": 821}, {"codegenSuccessful": true, "column": 62, "durationMicroseconds": 83, "errorMessage": "", "functionName": "horizontalAlignment", "line": 832}, {"codegenSuccessful": true, "column": 100, "durationMicroseconds": 74, "errorMessage": "", "functionName": "verticalAlignment", "line": 832}, {"codegenSuccessful": true, "column": 55, "durationMicroseconds": 19, "errorMessage": "", "functionName": "fill", "line": 829}, {"codegenSuccessful": true, "column": 50, "durationMicroseconds": 49, "errorMessage": "", "functionName": "visible", "line": 838}, {"codegenSuccessful": true, "column": 59, "durationMicroseconds": 21, "errorMessage": "", "functionName": "centerIn", "line": 837}, {"codegenSuccessful": true, "column": 79, "durationMicroseconds": 38, "errorMessage": "", "functionName": "verticalCenter", "line": 843}, {"codegenSuccessful": true, "column": 62, "durationMicroseconds": 28, "errorMessage": "", "functionName": "horizontalAlignment", "line": 853}, {"codegenSuccessful": true, "column": 100, "durationMicroseconds": 26, "errorMessage": "", "functionName": "verticalAlignment", "line": 853}, {"codegenSuccessful": true, "column": 55, "durationMicroseconds": 19, "errorMessage": "", "functionName": "fill", "line": 850}, {"codegenSuccessful": true, "column": 79, "durationMicroseconds": 38, "errorMessage": "", "functionName": "verticalCenter", "line": 863}, {"codegenSuccessful": false, "column": 47, "durationMicroseconds": 386, "errorMessage": "Cannot access value for name serverMode", "functionName": "text", "line": 870}, {"codegenSuccessful": true, "column": 62, "durationMicroseconds": 32, "errorMessage": "", "functionName": "horizontalAlignment", "line": 874}, {"codegenSuccessful": true, "column": 100, "durationMicroseconds": 28, "errorMessage": "", "functionName": "verticalAlignment", "line": 874}, {"codegenSuccessful": true, "column": 51, "durationMicroseconds": 28, "errorMessage": "", "functionName": "echoMode", "line": 875}, {"codegenSuccessful": false, "column": 56, "durationMicroseconds": 538, "errorMessage": "Cannot access value for name serverMode", "functionName": "onTextChanged", "line": 876}, {"codegenSuccessful": true, "column": 57, "durationMicroseconds": 43, "errorMessage": "", "functionName": "left", "line": 871}, {"codegenSuccessful": true, "column": 77, "durationMicroseconds": 45, "errorMessage": "", "functionName": "right", "line": 871}, {"codegenSuccessful": true, "column": 95, "durationMicroseconds": 39, "errorMessage": "", "functionName": "top", "line": 871}, {"codegenSuccessful": true, "column": 115, "durationMicroseconds": 38, "errorMessage": "", "functionName": "bottom", "line": 871}, {"codegenSuccessful": false, "column": 47, "durationMicroseconds": 314, "errorMessage": "Cannot access value for name serverMode", "functionName": "text", "line": 883}, {"codegenSuccessful": true, "column": 50, "durationMicroseconds": 53, "errorMessage": "", "functionName": "visible", "line": 887}, {"codegenSuccessful": true, "column": 59, "durationMicroseconds": 56, "errorMessage": "", "functionName": "centerIn", "line": 886}, {"codegenSuccessful": true, "column": 82, "durationMicroseconds": 202, "errorMessage": "", "functionName": "color", "line": 892}, {"codegenSuccessful": true, "column": 58, "durationMicroseconds": 41, "errorMessage": "", "functionName": "right", "line": 893}, {"codegenSuccessful": true, "column": 88, "durationMicroseconds": 38, "errorMessage": "", "functionName": "verticalCenter", "line": 893}, {"codegenSuccessful": true, "column": 51, "durationMicroseconds": 101, "errorMessage": "", "functionName": "text", "line": 895}, {"codegenSuccessful": true, "column": 63, "durationMicroseconds": 21, "errorMessage": "", "functionName": "centerIn", "line": 896}, {"codegenSuccessful": true, "column": 56, "durationMicroseconds": 149, "errorMessage": "", "functionName": "onClicked", "line": 900}, {"codegenSuccessful": true, "column": 72, "durationMicroseconds": 21, "errorMessage": "", "functionName": "fill", "line": 899}, {"codegenSuccessful": true, "column": 78, "durationMicroseconds": 176, "errorMessage": "", "functionName": "color", "line": 907}, {"codegenSuccessful": true, "column": 61, "durationMicroseconds": 41, "errorMessage": "", "functionName": "verticalCenter", "line": 909}, {"codegenSuccessful": true, "column": 71, "durationMicroseconds": 21, "errorMessage": "", "functionName": "centerIn", "line": 911}, {"codegenSuccessful": false, "column": 52, "durationMicroseconds": 572, "errorMessage": "method generateRandomToken cannot be resolved.", "functionName": "onClicked", "line": 916}, {"codegenSuccessful": true, "column": 70, "durationMicroseconds": 23, "errorMessage": "", "functionName": "fill", "line": 915}, {"codegenSuccessful": true, "column": 20, "durationMicroseconds": 57, "errorMessage": "", "functionName": "width", "line": 936}, {"codegenSuccessful": true, "column": 39, "durationMicroseconds": 39, "errorMessage": "", "functionName": "horizontalCenter", "line": 933}, {"codegenSuccessful": true, "column": 29, "durationMicroseconds": 114, "errorMessage": "", "functionName": "bottom", "line": 934}, {"codegenSuccessful": true, "column": 35, "durationMicroseconds": 20, "errorMessage": "", "functionName": "centerIn", "line": 943}, {"codegenSuccessful": true, "column": 30, "durationMicroseconds": 52, "errorMessage": "", "functionName": "onTriggered", "line": 952}, {"codegenSuccessful": false, "column": 32, "durationMicroseconds": 44, "errorMessage": "Cannot access value for name serverMode", "functionName": "onCompleted", "line": 960}, {"codegenSuccessful": false, "column": 21, "durationMicroseconds": 1152, "errorMessage": "Cannot access value for name serverMode", "functionName": "onAccepted", "line": 982}, {"codegenSuccessful": false, "column": 21, "durationMicroseconds": 76, "errorMessage": "Cannot access value for name cachePathInput", "functionName": "onAccepted", "line": 1011}, {"codegenSuccessful": false, "column": 38, "durationMicroseconds": 104, "errorMessage": "Cannot generate efficient code for internal conversion with incompatible or ambiguous types: call to method rgba, returning QVariant -> QColor stored as QVariant", "functionName": "color", "line": 1025}, {"codegenSuccessful": true, "column": 9, "durationMicroseconds": 20, "errorMessage": "", "functionName": "open", "line": 1084}, {"codegenSuccessful": true, "column": 23, "durationMicroseconds": 21, "errorMessage": "", "functionName": "fill", "line": 1025}, {"codegenSuccessful": true, "column": 54, "durationMicroseconds": 48, "errorMessage": "", "functionName": "onClicked", "line": 1026}, {"codegenSuccessful": true, "column": 35, "durationMicroseconds": 20, "errorMessage": "", "functionName": "fill", "line": 1026}, {"codegenSuccessful": true, "column": 31, "durationMicroseconds": 18, "errorMessage": "", "functionName": "centerIn", "line": 1030}, {"codegenSuccessful": true, "column": 33, "durationMicroseconds": 20, "errorMessage": "", "functionName": "fill", "line": 1034}, {"codegenSuccessful": true, "column": 65, "durationMicroseconds": 45, "errorMessage": "", "functionName": "horizontalCenter", "line": 1038}, {"codegenSuccessful": true, "column": 42, "durationMicroseconds": 33, "errorMessage": "", "functionName": "horizontalAlignment", "line": 1044}, {"codegenSuccessful": true, "column": 47, "durationMicroseconds": 39, "errorMessage": "", "functionName": "horizontalCenter", "line": 1043}, {"codegenSuccessful": true, "column": 60, "durationMicroseconds": 39, "errorMessage": "", "functionName": "horizontalCenter", "line": 1047}, {"codegenSuccessful": true, "column": 66, "durationMicroseconds": 134, "errorMessage": "", "functionName": "color", "line": 1050}, {"codegenSuccessful": true, "column": 59, "durationMicroseconds": 20, "errorMessage": "", "functionName": "centerIn", "line": 1053}, {"codegenSuccessful": true, "column": 40, "durationMicroseconds": 102, "errorMessage": "", "functionName": "onClicked", "line": 1058}, {"codegenSuccessful": true, "column": 59, "durationMicroseconds": 19, "errorMessage": "", "functionName": "fill", "line": 1057}, {"codegenSuccessful": true, "column": 66, "durationMicroseconds": 126, "errorMessage": "", "functionName": "color", "line": 1063}, {"codegenSuccessful": true, "column": 59, "durationMicroseconds": 19, "errorMessage": "", "functionName": "centerIn", "line": 1065}, {"codegenSuccessful": false, "column": 40, "durationMicroseconds": 464, "errorMessage": "Cannot access value for name imageProcessor", "functionName": "onClicked", "line": 1070}, {"codegenSuccessful": true, "column": 60, "durationMicroseconds": 21, "errorMessage": "", "functionName": "fill", "line": 1069}, {"codegenSuccessful": false, "column": 17, "durationMicroseconds": 30, "errorMessage": "Cannot access value for name clientMode", "functionName": "target", "line": 1089}, {"codegenSuccessful": false, "column": 9, "durationMicroseconds": 74, "errorMessage": "Cannot access value for name clientMode", "functionName": "onConnectedChanged", "line": 1090}, {"codegenSuccessful": false, "column": 9, "durationMicroseconds": 0, "errorMessage": "Functions without type annotations won't be compiled", "functionName": "onConnectionError", "line": 1094}], "filePath": "C:/Qt/file/palyer/compoment/SettingsPage.qml"}], "moduleId": "palyer(palyer)"}]