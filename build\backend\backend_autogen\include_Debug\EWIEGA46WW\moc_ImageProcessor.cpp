/****************************************************************************
** Meta object code from reading C++ file 'ImageProcessor.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.1)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../../backend/ImageProcessor.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ImageProcessor.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.1. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN14ImageProcessorE_t {};
} // unnamed namespace

template <> constexpr inline auto ImageProcessor::qt_create_metaobjectdata<qt_meta_tag_ZN14ImageProcessorE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ImageProcessor",
        "lastErrorChanged",
        "",
        "processingChanged",
        "countChanged",
        "currentFolderChanged",
        "sortModeChanged",
        "decoderTypeChanged",
        "processingFinished",
        "success",
        "thumbnailReady",
        "index",
        "handleThumbnailReady",
        "loadImagesFromFolder",
        "folderPath",
        "includeFolders",
        "sortImageList",
        "sortMode",
        "refreshCurrentFolder",
        "clearCache",
        "getFilePath",
        "getFileName",
        "getFileSize",
        "getImageInfo",
        "getThumbnailImage",
        "getImageUrl",
        "switchDecoder",
        "decoderType",
        "getThumbnailSource",
        "isItemLoading",
        "requestThumbnails",
        "startIndex",
        "endIndex",
        "getPreviewImage",
        "getAvifPreviewUrl",
        "getAvifFullImageUrl",
        "getAvifImage",
        "imageToUrl",
        "image",
        "getOptimalSize",
        "getNetworkImageList",
        "getNetworkThumbnail",
        "imagePath",
        "getNetworkFullImage",
        "canServeNetworkRequest",
        "estimatedSize",
        "lastError",
        "processing",
        "count",
        "currentFolder"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'lastErrorChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'processingChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'countChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'currentFolderChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'sortModeChanged'
        QtMocHelpers::SignalData<void()>(6, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'decoderTypeChanged'
        QtMocHelpers::SignalData<void()>(7, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'processingFinished'
        QtMocHelpers::SignalData<void(bool)>(8, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 9 },
        }}),
        // Signal 'thumbnailReady'
        QtMocHelpers::SignalData<void(int)>(10, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 11 },
        }}),
        // Slot 'handleThumbnailReady'
        QtMocHelpers::SlotData<void(int)>(12, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Int, 11 },
        }}),
        // Method 'loadImagesFromFolder'
        QtMocHelpers::MethodData<void(const QString &, bool)>(13, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 14 }, { QMetaType::Bool, 15 },
        }}),
        // Method 'loadImagesFromFolder'
        QtMocHelpers::MethodData<void(const QString &)>(13, 2, QMC::AccessPublic | QMC::MethodCloned, QMetaType::Void, {{
            { QMetaType::QString, 14 },
        }}),
        // Method 'sortImageList'
        QtMocHelpers::MethodData<void(int)>(16, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 17 },
        }}),
        // Method 'refreshCurrentFolder'
        QtMocHelpers::MethodData<void()>(18, 2, QMC::AccessPublic, QMetaType::Void),
        // Method 'clearCache'
        QtMocHelpers::MethodData<void()>(19, 2, QMC::AccessPublic, QMetaType::Void),
        // Method 'getFilePath'
        QtMocHelpers::MethodData<QString(int) const>(20, 2, QMC::AccessPublic, QMetaType::QString, {{
            { QMetaType::Int, 11 },
        }}),
        // Method 'getFileName'
        QtMocHelpers::MethodData<QString(int) const>(21, 2, QMC::AccessPublic, QMetaType::QString, {{
            { QMetaType::Int, 11 },
        }}),
        // Method 'getFileSize'
        QtMocHelpers::MethodData<qint64(int) const>(22, 2, QMC::AccessPublic, QMetaType::LongLong, {{
            { QMetaType::Int, 11 },
        }}),
        // Method 'getImageInfo'
        QtMocHelpers::MethodData<QString(int) const>(23, 2, QMC::AccessPublic, QMetaType::QString, {{
            { QMetaType::Int, 11 },
        }}),
        // Method 'getThumbnailImage'
        QtMocHelpers::MethodData<QString(int) const>(24, 2, QMC::AccessPublic, QMetaType::QString, {{
            { QMetaType::Int, 11 },
        }}),
        // Method 'getImageUrl'
        QtMocHelpers::MethodData<QString(int) const>(25, 2, QMC::AccessPublic, QMetaType::QString, {{
            { QMetaType::Int, 11 },
        }}),
        // Method 'switchDecoder'
        QtMocHelpers::MethodData<void(int)>(26, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 27 },
        }}),
        // Method 'getThumbnailSource'
        QtMocHelpers::MethodData<QString(int)>(28, 2, QMC::AccessPublic, QMetaType::QString, {{
            { QMetaType::Int, 11 },
        }}),
        // Method 'isItemLoading'
        QtMocHelpers::MethodData<bool(int) const>(29, 2, QMC::AccessPublic, QMetaType::Bool, {{
            { QMetaType::Int, 11 },
        }}),
        // Method 'requestThumbnails'
        QtMocHelpers::MethodData<void(int, int)>(30, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 31 }, { QMetaType::Int, 32 },
        }}),
        // Method 'getPreviewImage'
        QtMocHelpers::MethodData<QImage(int) const>(33, 2, QMC::AccessPublic, QMetaType::QImage, {{
            { QMetaType::Int, 11 },
        }}),
        // Method 'getAvifPreviewUrl'
        QtMocHelpers::MethodData<QString(int) const>(34, 2, QMC::AccessPublic, QMetaType::QString, {{
            { QMetaType::Int, 11 },
        }}),
        // Method 'getAvifFullImageUrl'
        QtMocHelpers::MethodData<QString(int) const>(35, 2, QMC::AccessPublic, QMetaType::QString, {{
            { QMetaType::Int, 11 },
        }}),
        // Method 'getAvifImage'
        QtMocHelpers::MethodData<QImage(int) const>(36, 2, QMC::AccessPublic, QMetaType::QImage, {{
            { QMetaType::Int, 11 },
        }}),
        // Method 'imageToUrl'
        QtMocHelpers::MethodData<QUrl(const QImage &) const>(37, 2, QMC::AccessPublic, QMetaType::QUrl, {{
            { QMetaType::QImage, 38 },
        }}),
        // Method 'getOptimalSize'
        QtMocHelpers::MethodData<QSize() const>(39, 2, QMC::AccessPublic, QMetaType::QSize),
        // Method 'getNetworkImageList'
        QtMocHelpers::MethodData<QJsonObject(const QString &)>(40, 2, QMC::AccessPublic, QMetaType::QJsonObject, {{
            { QMetaType::QString, 14 },
        }}),
        // Method 'getNetworkThumbnail'
        QtMocHelpers::MethodData<QByteArray(const QString &)>(41, 2, QMC::AccessPublic, QMetaType::QByteArray, {{
            { QMetaType::QString, 42 },
        }}),
        // Method 'getNetworkFullImage'
        QtMocHelpers::MethodData<QByteArray(const QString &)>(43, 2, QMC::AccessPublic, QMetaType::QByteArray, {{
            { QMetaType::QString, 42 },
        }}),
        // Method 'canServeNetworkRequest'
        QtMocHelpers::MethodData<bool(qint64)>(44, 2, QMC::AccessPublic, QMetaType::Bool, {{
            { QMetaType::LongLong, 45 },
        }}),
        // Method 'canServeNetworkRequest'
        QtMocHelpers::MethodData<bool()>(44, 2, QMC::AccessPublic | QMC::MethodCloned, QMetaType::Bool),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'lastError'
        QtMocHelpers::PropertyData<QString>(46, QMetaType::QString, QMC::DefaultPropertyFlags, 0),
        // property 'processing'
        QtMocHelpers::PropertyData<bool>(47, QMetaType::Bool, QMC::DefaultPropertyFlags, 1),
        // property 'count'
        QtMocHelpers::PropertyData<int>(48, QMetaType::Int, QMC::DefaultPropertyFlags, 2),
        // property 'currentFolder'
        QtMocHelpers::PropertyData<QString>(49, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 3),
        // property 'sortMode'
        QtMocHelpers::PropertyData<int>(17, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 4),
        // property 'decoderType'
        QtMocHelpers::PropertyData<int>(27, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 5),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ImageProcessor, qt_meta_tag_ZN14ImageProcessorE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject ImageProcessor::staticMetaObject = { {
    QMetaObject::SuperData::link<QAbstractListModel::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN14ImageProcessorE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN14ImageProcessorE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN14ImageProcessorE_t>.metaTypes,
    nullptr
} };

void ImageProcessor::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ImageProcessor *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->lastErrorChanged(); break;
        case 1: _t->processingChanged(); break;
        case 2: _t->countChanged(); break;
        case 3: _t->currentFolderChanged(); break;
        case 4: _t->sortModeChanged(); break;
        case 5: _t->decoderTypeChanged(); break;
        case 6: _t->processingFinished((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 7: _t->thumbnailReady((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 8: _t->handleThumbnailReady((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 9: _t->loadImagesFromFolder((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<bool>>(_a[2]))); break;
        case 10: _t->loadImagesFromFolder((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 11: _t->sortImageList((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 12: _t->refreshCurrentFolder(); break;
        case 13: _t->clearCache(); break;
        case 14: { QString _r = _t->getFilePath((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 15: { QString _r = _t->getFileName((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 16: { qint64 _r = _t->getFileSize((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])));
            if (_a[0]) *reinterpret_cast< qint64*>(_a[0]) = std::move(_r); }  break;
        case 17: { QString _r = _t->getImageInfo((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 18: { QString _r = _t->getThumbnailImage((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 19: { QString _r = _t->getImageUrl((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 20: _t->switchDecoder((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 21: { QString _r = _t->getThumbnailSource((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 22: { bool _r = _t->isItemLoading((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 23: _t->requestThumbnails((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2]))); break;
        case 24: { QImage _r = _t->getPreviewImage((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QImage*>(_a[0]) = std::move(_r); }  break;
        case 25: { QString _r = _t->getAvifPreviewUrl((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 26: { QString _r = _t->getAvifFullImageUrl((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 27: { QImage _r = _t->getAvifImage((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QImage*>(_a[0]) = std::move(_r); }  break;
        case 28: { QUrl _r = _t->imageToUrl((*reinterpret_cast< std::add_pointer_t<QImage>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QUrl*>(_a[0]) = std::move(_r); }  break;
        case 29: { QSize _r = _t->getOptimalSize();
            if (_a[0]) *reinterpret_cast< QSize*>(_a[0]) = std::move(_r); }  break;
        case 30: { QJsonObject _r = _t->getNetworkImageList((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QJsonObject*>(_a[0]) = std::move(_r); }  break;
        case 31: { QByteArray _r = _t->getNetworkThumbnail((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QByteArray*>(_a[0]) = std::move(_r); }  break;
        case 32: { QByteArray _r = _t->getNetworkFullImage((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QByteArray*>(_a[0]) = std::move(_r); }  break;
        case 33: { bool _r = _t->canServeNetworkRequest((*reinterpret_cast< std::add_pointer_t<qint64>>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 34: { bool _r = _t->canServeNetworkRequest();
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ImageProcessor::*)()>(_a, &ImageProcessor::lastErrorChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ImageProcessor::*)()>(_a, &ImageProcessor::processingChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ImageProcessor::*)()>(_a, &ImageProcessor::countChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (ImageProcessor::*)()>(_a, &ImageProcessor::currentFolderChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (ImageProcessor::*)()>(_a, &ImageProcessor::sortModeChanged, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (ImageProcessor::*)()>(_a, &ImageProcessor::decoderTypeChanged, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (ImageProcessor::*)(bool )>(_a, &ImageProcessor::processingFinished, 6))
            return;
        if (QtMocHelpers::indexOfMethod<void (ImageProcessor::*)(int )>(_a, &ImageProcessor::thumbnailReady, 7))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<QString*>(_v) = _t->lastError(); break;
        case 1: *reinterpret_cast<bool*>(_v) = _t->processing(); break;
        case 2: *reinterpret_cast<int*>(_v) = _t->count(); break;
        case 3: *reinterpret_cast<QString*>(_v) = _t->currentFolder(); break;
        case 4: *reinterpret_cast<int*>(_v) = _t->sortMode(); break;
        case 5: *reinterpret_cast<int*>(_v) = _t->decoderType(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 3: _t->setCurrentFolder(*reinterpret_cast<QString*>(_v)); break;
        case 4: _t->setSortMode(*reinterpret_cast<int*>(_v)); break;
        case 5: _t->setDecoderType(*reinterpret_cast<int*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *ImageProcessor::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ImageProcessor::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN14ImageProcessorE_t>.strings))
        return static_cast<void*>(this);
    return QAbstractListModel::qt_metacast(_clname);
}

int ImageProcessor::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QAbstractListModel::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 35)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 35;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 35)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 35;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    }
    return _id;
}

// SIGNAL 0
void ImageProcessor::lastErrorChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ImageProcessor::processingChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ImageProcessor::countChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ImageProcessor::currentFolderChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void ImageProcessor::sortModeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void ImageProcessor::decoderTypeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}

// SIGNAL 6
void ImageProcessor::processingFinished(bool _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 6, nullptr, _t1);
}

// SIGNAL 7
void ImageProcessor::thumbnailReady(int _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 7, nullptr, _t1);
}
QT_WARNING_POP
