{"AUTOGEN_COMMAND_LINE_LENGTH_MAX": 32000, "BUILD_DIR": "C:/Qt/file/palyer/build/backend/backend_autogen", "CMAKE_BINARY_DIR": "C:/Qt/file/palyer/build", "CMAKE_CURRENT_BINARY_DIR": "C:/Qt/file/palyer/build/backend", "CMAKE_CURRENT_SOURCE_DIR": "C:/Qt/file/palyer/backend", "CMAKE_EXECUTABLE": "C:/Qt/Tools/CMake_64/bin/cmake.exe", "CMAKE_LIST_FILES": ["C:/Qt/file/palyer/backend/CMakeLists.txt", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6ConfigVersion.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Config.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6ConfigExtras.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtInstallPaths.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Targets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtFeature.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CheckCXXCompilerFlag.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtFeatureCommon.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicAppleHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicGitHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicPluginHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicTargetHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicTestHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicToolHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Dependencies.cmake"], "CMAKE_SOURCE_DIR": "C:/Qt/file/palyer", "CROSS_CONFIG": false, "DEP_FILE": "", "DEP_FILE_RULE_NAME": "", "HEADERS": [["C:/Qt/file/palyer/backend/ConversionTaskRunnable.h", "Mu", "EWIEGA46WW/moc_ConversionTaskRunnable.cpp", null], ["C:/Qt/file/palyer/backend/ImageConversionManager.h", "Mu", "EWIEGA46WW/moc_ImageConversionManager.cpp", null], ["C:/Qt/file/palyer/backend/ImageProcessor.h", "Mu", "EWIEGA46WW/moc_ImageProcessor.cpp", null], ["C:/Qt/file/palyer/backend/NetworkBackend.h", "Mu", "EWIEGA46WW/moc_NetworkBackend.cpp", null], ["C:/Qt/file/palyer/backend/server/ClientMode.h", "Mu", "ORNZQ2F6DW/moc_ClientMode.cpp", null], ["C:/Qt/file/palyer/backend/server/ServerMode.h", "Mu", "ORNZQ2F6DW/moc_ServerMode.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "C:/Qt/file/palyer/build/backend/backend_autogen/include", "INCLUDE_DIR_Debug": "C:/Qt/file/palyer/build/backend/backend_autogen/include_Debug", "INCLUDE_DIR_MinSizeRel": "C:/Qt/file/palyer/build/backend/backend_autogen/include_MinSizeRel", "INCLUDE_DIR_RelWithDebInfo": "C:/Qt/file/palyer/build/backend/backend_autogen/include_RelWithDebInfo", "INCLUDE_DIR_Release": "C:/Qt/file/palyer/build/backend/backend_autogen/include_Release", "MOC_COMPILATION_FILE": "C:/Qt/file/palyer/build/backend/backend_autogen/mocs_compilation.cpp", "MOC_COMPILATION_FILE_Debug": "C:/Qt/file/palyer/build/backend/backend_autogen/mocs_compilation_Debug.cpp", "MOC_COMPILATION_FILE_MinSizeRel": "C:/Qt/file/palyer/build/backend/backend_autogen/mocs_compilation_MinSizeRel.cpp", "MOC_COMPILATION_FILE_RelWithDebInfo": "C:/Qt/file/palyer/build/backend/backend_autogen/mocs_compilation_RelWithDebInfo.cpp", "MOC_COMPILATION_FILE_Release": "C:/Qt/file/palyer/build/backend/backend_autogen/mocs_compilation_Release.cpp", "MOC_DEFINITIONS": [], "MOC_DEFINITIONS_Debug": ["BACKEND_LIBRARY", "MINGW_HAS_SECURE_API=1", "QT_CONCURRENT_LIB", "QT_CORE_LIB", "QT_DEPRECATED_WARNINGS", "QT_GUI_LIB", "QT_NETWORK_LIB", "QT_OPENGL_LIB", "QT_QMLINTEGRATION_LIB", "QT_QMLMETA_LIB", "QT_QMLMODELS_LIB", "QT_QMLWORKERSCRIPT_LIB", "QT_QML_LIB", "QT_QUICK_LIB", "UNICODE", "WIN32", "WIN64", "_CRT_SECURE_NO_WARNINGS", "_ENABLE_EXTENDED_ALIGNED_STORAGE", "_UNICODE", "_WIN64", "backend_EXPORTS"], "MOC_DEFINITIONS_MinSizeRel": ["BACKEND_LIBRARY", "MINGW_HAS_SECURE_API=1", "QT_CONCURRENT_LIB", "QT_CORE_LIB", "QT_DEPRECATED_WARNINGS", "QT_GUI_LIB", "QT_NETWORK_LIB", "QT_NO_DEBUG", "QT_OPENGL_LIB", "QT_QMLINTEGRATION_LIB", "QT_QMLMETA_LIB", "QT_QMLMODELS_LIB", "QT_QMLWORKERSCRIPT_LIB", "QT_QML_LIB", "QT_QUICK_LIB", "UNICODE", "WIN32", "WIN64", "_CRT_SECURE_NO_WARNINGS", "_ENABLE_EXTENDED_ALIGNED_STORAGE", "_UNICODE", "_WIN64", "backend_EXPORTS"], "MOC_DEFINITIONS_RelWithDebInfo": ["BACKEND_LIBRARY", "MINGW_HAS_SECURE_API=1", "QT_CONCURRENT_LIB", "QT_CORE_LIB", "QT_DEPRECATED_WARNINGS", "QT_GUI_LIB", "QT_NETWORK_LIB", "QT_NO_DEBUG", "QT_OPENGL_LIB", "QT_QMLINTEGRATION_LIB", "QT_QMLMETA_LIB", "QT_QMLMODELS_LIB", "QT_QMLWORKERSCRIPT_LIB", "QT_QML_LIB", "QT_QUICK_LIB", "UNICODE", "WIN32", "WIN64", "_CRT_SECURE_NO_WARNINGS", "_ENABLE_EXTENDED_ALIGNED_STORAGE", "_UNICODE", "_WIN64", "backend_EXPORTS"], "MOC_DEFINITIONS_Release": ["BACKEND_LIBRARY", "MINGW_HAS_SECURE_API=1", "QT_CONCURRENT_LIB", "QT_CORE_LIB", "QT_DEPRECATED_WARNINGS", "QT_GUI_LIB", "QT_NETWORK_LIB", "QT_NO_DEBUG", "QT_OPENGL_LIB", "QT_QMLINTEGRATION_LIB", "QT_QMLMETA_LIB", "QT_QMLMODELS_LIB", "QT_QMLWORKERSCRIPT_LIB", "QT_QML_LIB", "QT_QUICK_LIB", "UNICODE", "WIN32", "WIN64", "_CRT_SECURE_NO_WARNINGS", "_ENABLE_EXTENDED_ALIGNED_STORAGE", "_UNICODE", "_WIN64", "backend_EXPORTS"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": [], "MOC_INCLUDES_Debug": ["C:/Qt/file/palyer/backend", "C:/Qt/6.9.1/mingw_64/include/QtCore", "C:/Qt/6.9.1/mingw_64/include", "C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++", "C:/Qt/6.9.1/mingw_64/include/QtConcurrent", "C:/Qt/6.9.1/mingw_64/include/QtGui", "C:/Qt/6.9.1/mingw_64/include/QtQuick", "C:/Qt/6.9.1/mingw_64/include/QtQml", "C:/Qt/6.9.1/mingw_64/include/QtQmlIntegration", "C:/Qt/6.9.1/mingw_64/include/QtNetwork", "C:/Qt/6.9.1/mingw_64/include/QtQmlMeta", "C:/Qt/6.9.1/mingw_64/include/QtQmlModels", "C:/Qt/6.9.1/mingw_64/include/QtQmlWorkerScript", "C:/Qt/6.9.1/mingw_64/include/QtOpenGL"], "MOC_INCLUDES_MinSizeRel": ["C:/Qt/file/palyer/backend", "C:/Qt/6.9.1/mingw_64/include/QtCore", "C:/Qt/6.9.1/mingw_64/include", "C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++", "C:/Qt/6.9.1/mingw_64/include/QtConcurrent", "C:/Qt/6.9.1/mingw_64/include/QtGui", "C:/Qt/6.9.1/mingw_64/include/QtQuick", "C:/Qt/6.9.1/mingw_64/include/QtQml", "C:/Qt/6.9.1/mingw_64/include/QtQmlIntegration", "C:/Qt/6.9.1/mingw_64/include/QtNetwork", "C:/Qt/6.9.1/mingw_64/include/QtQmlMeta", "C:/Qt/6.9.1/mingw_64/include/QtQmlModels", "C:/Qt/6.9.1/mingw_64/include/QtQmlWorkerScript", "C:/Qt/6.9.1/mingw_64/include/QtOpenGL"], "MOC_INCLUDES_RelWithDebInfo": ["C:/Qt/file/palyer/backend", "C:/Qt/6.9.1/mingw_64/include/QtCore", "C:/Qt/6.9.1/mingw_64/include", "C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++", "C:/Qt/6.9.1/mingw_64/include/QtConcurrent", "C:/Qt/6.9.1/mingw_64/include/QtGui", "C:/Qt/6.9.1/mingw_64/include/QtQuick", "C:/Qt/6.9.1/mingw_64/include/QtQml", "C:/Qt/6.9.1/mingw_64/include/QtQmlIntegration", "C:/Qt/6.9.1/mingw_64/include/QtNetwork", "C:/Qt/6.9.1/mingw_64/include/QtQmlMeta", "C:/Qt/6.9.1/mingw_64/include/QtQmlModels", "C:/Qt/6.9.1/mingw_64/include/QtQmlWorkerScript", "C:/Qt/6.9.1/mingw_64/include/QtOpenGL"], "MOC_INCLUDES_Release": ["C:/Qt/file/palyer/backend", "C:/Qt/6.9.1/mingw_64/include/QtCore", "C:/Qt/6.9.1/mingw_64/include", "C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++", "C:/Qt/6.9.1/mingw_64/include/QtConcurrent", "C:/Qt/6.9.1/mingw_64/include/QtGui", "C:/Qt/6.9.1/mingw_64/include/QtQuick", "C:/Qt/6.9.1/mingw_64/include/QtQml", "C:/Qt/6.9.1/mingw_64/include/QtQmlIntegration", "C:/Qt/6.9.1/mingw_64/include/QtNetwork", "C:/Qt/6.9.1/mingw_64/include/QtQmlMeta", "C:/Qt/6.9.1/mingw_64/include/QtQmlModels", "C:/Qt/6.9.1/mingw_64/include/QtQmlWorkerScript", "C:/Qt/6.9.1/mingw_64/include/QtOpenGL"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT", "Q_GADGET_EXPORT", "Q_ENUM_NS"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": [], "MOC_PREDEFS_FILE": "", "MOC_RELAXED_MODE": false, "MOC_SKIP": [], "MULTI_CONFIG": true, "PARALLEL": 14, "PARSE_CACHE_FILE": "C:/Qt/file/palyer/build/backend/CMakeFiles/backend_autogen.dir/ParseCache.txt", "PARSE_CACHE_FILE_Debug": "C:/Qt/file/palyer/build/backend/CMakeFiles/backend_autogen.dir/ParseCache_Debug.txt", "PARSE_CACHE_FILE_MinSizeRel": "C:/Qt/file/palyer/build/backend/CMakeFiles/backend_autogen.dir/ParseCache_MinSizeRel.txt", "PARSE_CACHE_FILE_RelWithDebInfo": "C:/Qt/file/palyer/build/backend/CMakeFiles/backend_autogen.dir/ParseCache_RelWithDebInfo.txt", "PARSE_CACHE_FILE_Release": "C:/Qt/file/palyer/build/backend/CMakeFiles/backend_autogen.dir/ParseCache_Release.txt", "QT_MOC_EXECUTABLE": "", "QT_MOC_EXECUTABLE_Debug": "C:/Qt/6.9.1/mingw_64/bin/moc.exe", "QT_MOC_EXECUTABLE_MinSizeRel": "C:/Qt/6.9.1/mingw_64/bin/moc.exe", "QT_MOC_EXECUTABLE_RelWithDebInfo": "C:/Qt/6.9.1/mingw_64/bin/moc.exe", "QT_MOC_EXECUTABLE_Release": "C:/Qt/6.9.1/mingw_64/bin/moc.exe", "QT_UIC_EXECUTABLE": "", "QT_VERSION_MAJOR": 6, "QT_VERSION_MINOR": 9, "SETTINGS_FILE": "C:/Qt/file/palyer/build/backend/CMakeFiles/backend_autogen.dir/AutogenUsed.txt", "SETTINGS_FILE_Debug": "C:/Qt/file/palyer/build/backend/CMakeFiles/backend_autogen.dir/AutogenUsed_Debug.txt", "SETTINGS_FILE_MinSizeRel": "C:/Qt/file/palyer/build/backend/CMakeFiles/backend_autogen.dir/AutogenUsed_MinSizeRel.txt", "SETTINGS_FILE_RelWithDebInfo": "C:/Qt/file/palyer/build/backend/CMakeFiles/backend_autogen.dir/AutogenUsed_RelWithDebInfo.txt", "SETTINGS_FILE_Release": "C:/Qt/file/palyer/build/backend/CMakeFiles/backend_autogen.dir/AutogenUsed_Release.txt", "SOURCES": [["C:/Qt/file/palyer/backend/ConversionTaskRunnable.cpp", "Mu", null], ["C:/Qt/file/palyer/backend/ImageConversionManager.cpp", "Mu", null], ["C:/Qt/file/palyer/backend/ImageProcessor.cpp", "Mu", null], ["C:/Qt/file/palyer/backend/NetworkBackend.cpp", "Mu", null], ["C:/Qt/file/palyer/backend/server/ClientMode.cpp", "Mu", null], ["C:/Qt/file/palyer/backend/server/ServerMode.cpp", "Mu", null]], "USE_BETTER_GRAPH": true, "VERBOSITY": 0}